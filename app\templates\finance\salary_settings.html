{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
        <a href="{{ url_for('finance.salaries') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i>العودة إلى الرواتب
        </a>
    </div>

    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-cog me-2"></i>إعدادات رواتب الموظفين
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>ملاحظة:</strong> هذه الإعدادات ستكون الافتراضية عند إضافة رواتب جديدة. 
                            يمكن تغييرها لكل راتب على حدة.
                        </div>

                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">الحسابات الافتراضية</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="mb-3">
                                            <label for="default_pending_account" class="form-label">
                                                حساب المال المعلق الافتراضي
                                            </label>
                                            <select class="form-select" id="default_pending_account" name="default_pending_account">
                                                <option value="">اختر الحساب</option>
                                                {% for account in accounts %}
                                                <option value="{{ account.id }}" 
                                                        {% if account.account_type == 'pending' %}selected{% endif %}>
                                                    {{ account.full_path }}
                                                    {% if account.account_type == 'pending' %}
                                                    <span class="badge bg-warning">موصى به</span>
                                                    {% endif %}
                                                </option>
                                                {% endfor %}
                                            </select>
                                            <div class="form-text">
                                                الحساب الذي سيتم إضافة المبالغ إليه عند تعليق الرواتب
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="mb-3">
                                            <label for="default_deduction_account" class="form-label">
                                                حساب الخصم الافتراضي
                                            </label>
                                            <select class="form-select" id="default_deduction_account" name="default_deduction_account">
                                                <option value="">اختر الحساب</option>
                                                {% for account in accounts %}
                                                <option value="{{ account.id }}"
                                                        {% if account.account_type in ['cash', 'bank'] %}selected{% endif %}>
                                                    {{ account.full_path }}
                                                    {% if account.account_type in ['cash', 'bank'] %}
                                                    <span class="badge bg-success">موصى به</span>
                                                    {% endif %}
                                                </option>
                                                {% endfor %}
                                            </select>
                                            <div class="form-text">
                                                الحساب الذي سيتم خصم المبالغ منه عند دفع الرواتب
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="mb-3">
                                            <label for="default_paid_account" class="form-label">
                                                حساب الرواتب المدفوعة الافتراضي
                                            </label>
                                            <select class="form-select" id="default_paid_account" name="default_paid_account">
                                                <option value="">اختر الحساب</option>
                                                {% for account in accounts %}
                                                <option value="{{ account.id }}"
                                                        {% if account.account_type == 'expense' %}selected{% endif %}>
                                                    {{ account.full_path }}
                                                    {% if account.account_type == 'expense' %}
                                                    <span class="badge bg-danger">موصى به</span>
                                                    {% endif %}
                                                </option>
                                                {% endfor %}
                                            </select>
                                            <div class="form-text">
                                                الحساب الذي سيتم تسجيل الرواتب المدفوعة فيه
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">إعدادات عامة</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="auto_generate_transfer_date" class="form-label">
                                                موعد الحوالة الافتراضي
                                            </label>
                                            <select class="form-select" id="auto_generate_transfer_date" name="auto_generate_transfer_date">
                                                <option value="none">لا يوجد</option>
                                                <option value="now">الوقت الحالي</option>
                                                <option value="end_of_month">نهاية الشهر</option>
                                                <option value="custom">تاريخ مخصص</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="default_status" class="form-label">
                                                الحالة الافتراضية للرواتب الجديدة
                                            </label>
                                            <select class="form-select" id="default_status" name="default_status">
                                                <option value="draft">مسودة</option>
                                                <option value="pending">معلق</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="require_supervisor" name="require_supervisor">
                                            <label class="form-check-label" for="require_supervisor">
                                                يتطلب تحديد مشرف للحوالة
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="auto_link_projects" name="auto_link_projects">
                                            <label class="form-check-label" for="auto_link_projects">
                                                ربط تلقائي بالمشاريع النشطة للموظف
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">إعدادات التنبيهات</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="notify_on_status_change" name="notify_on_status_change">
                                            <label class="form-check-label" for="notify_on_status_change">
                                                إرسال تنبيه عند تغيير حالة الراتب
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="notify_supervisor" name="notify_supervisor">
                                            <label class="form-check-label" for="notify_supervisor">
                                                إرسال تنبيه للمشرف عند إضافة راتب جديد
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('finance.salaries') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-info">
                                <i class="fas fa-save me-1"></i>حفظ الإعدادات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form validation
    const form = document.querySelector('form');
    
    form.addEventListener('submit', function(e) {
        // Add any validation logic here if needed
        console.log('Saving salary settings...');
    });
});
</script>
{% endblock %}
