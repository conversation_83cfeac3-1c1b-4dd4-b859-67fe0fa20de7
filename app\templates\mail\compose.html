{% extends 'base.html' %}

{% block styles %}
<link href="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote-bs4.min.css" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<style>
    .note-editor {
        direction: rtl;
    }
    .note-toolbar {
        direction: ltr;
    }
    .select2-container {
        width: 100% !important;
    }
    .select2-selection__choice {
        float: right !important;
        margin-right: 0 !important;
        margin-left: 5px !important;
    }
    .select2-search__field {
        direction: rtl;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>{{ title }}</h1>
    <a href="{{ url_for('mail.inbox') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-right me-1"></i>العودة إلى البريد الوارد
    </a>
</div>

<div class="card">
    <div class="card-body">
        <form action="{{ url_for('mail.compose') }}" method="POST" enctype="multipart/form-data">
            <div class="mb-3">
                <label for="recipients" class="form-label">إلى: <span class="text-danger">*</span></label>
                <select class="form-control select2-recipients" id="recipients" name="recipients" multiple required>
                    {% for user in users %}
                    <option value="{{ user.id }}"
                            data-department="{{ user.department.name if user.department else 'غير محدد' }}"
                            data-email="{{ user.email }}"
                            {% if recipients and user in recipients %}selected{% endif %}>
                        {{ user.get_full_name() }} ({{ user.department.name if user.department else 'غير محدد' }})
                    </option>
                    {% endfor %}
                </select>
                <div class="form-text">يمكنك اختيار أكثر من مستلم</div>
            </div>

            <div class="mb-3">
                <label for="subject" class="form-label">الموضوع:</label>
                <input type="text" class="form-control" id="subject" name="subject" value="{{ subject }}" required>
            </div>

            <div class="mb-3">
                <label for="body" class="form-label">المحتوى:</label>
                <textarea class="form-control summernote" id="body" name="body" rows="10">{{ body }}</textarea>
                <div class="form-text mt-2">
                    <i class="fas fa-info-circle me-1"></i>
                    في أي رسالة جديدة مهما كانت ، يرجى ذكر ايضاً حساب (اسم الحساب) كمستلم ليتم لتسجيل كافة الإيميلات
                </div>
            </div>

            <div class="mb-3">
                <label for="attachments" class="form-label">المرفقات:</label>
                <input type="file" class="form-control" id="attachments" name="attachments" multiple>
                <div class="form-text">يمكنك تحديد أكثر من ملف</div>
            </div>

            <div class="d-flex justify-content-between">
                <button type="submit" name="save_draft" class="btn btn-secondary">
                    <i class="fas fa-save me-1"></i>حفظ كمسودة
                </button>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-paper-plane me-1"></i>إرسال
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/jquery@3.5.1/dist/jquery.slim.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.1/dist/js/bootstrap.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote-bs4.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
    $(document).ready(function() {
        // تهيئة محرر النص الغني
        $('.summernote').summernote({
            icons: {
                align: 'fa fa-align',
                alignCenter: 'fa fa-align-center',
                alignJustify: 'fa fa-align-justify',
                alignLeft: 'fa fa-align-left',
                alignRight: 'fa fa-align-right',
                indent: 'fa fa-indent',
                outdent: 'fa fa-outdent',
                arrowsAlt: 'fa fa-arrows-alt',
                bold: 'fa fa-bold',
                caret: 'fa fa-caret-down',
                circle: 'fa fa-circle',
                close: 'fa fa-close',
                code: 'fa fa-code',
                eraser: 'fa fa-eraser',
                font: 'fa fa-font',
                frame: 'fa fa-frame',
                italic: 'fa fa-italic',
                link: 'fa fa-link',
                unlink: 'fa fa-unlink',
                magic: 'fa fa-magic',
                menuCheck: 'fa fa-check',
                minus: 'fa fa-minus',
                orderedlist: 'fa fa-list-ol',
                pencil: 'fa fa-pencil',
                picture: 'fa fa-picture-o',
                question: 'fa fa-question',
                redo: 'fa fa-repeat',
                square: 'fa fa-square',
                strikethrough: 'fa fa-strikethrough',
                subscript: 'fa fa-subscript',
                superscript: 'fa fa-superscript',
                table: 'fa fa-table',
                textHeight: 'fa fa-text-height',
                trash: 'fa fa-trash',
                underline: 'fa fa-underline',
                undo: 'fa fa-undo',
                unorderedlist: 'fa fa-list-ul',
                video: 'fa fa-video-camera'
            },
            height: 300,
            toolbar: [
                ['style', ['style']],
                ['font', ['bold', 'italic', 'underline', 'strikethrough', 'superscript', 'subscript', 'clear']],
                ['fontname', ['fontname']],
                ['fontsize', ['fontsize']],
                ['color', ['color']],
                ['para', ['ul', 'ol', 'paragraph', 'height']],
                ['table', ['table']],
                ['insert', ['link', 'picture', 'video']],
                ['view', ['fullscreen', 'codeview', 'help']]
            ],
            fontNames: ['Arial', 'Arial Black', 'Comic Sans MS', 'Courier New', 'Helvetica', 'Impact', 'Tahoma', 'Times New Roman', 'Verdana', 'Tajawal', 'Cairo', 'Amiri', 'Scheherazade', 'Lateef', 'Harmattan'],
            fontSizes: ['8', '9', '10', '11', '12', '14', '16', '18', '20', '22', '24', '28', '36', '48', '72'],
            styleTags: ['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'blockquote', 'pre'],
            dialogsInBody: true,
            dialogsFade: true,
            disableDragAndDrop: false,
            linkTargetBlank: true,
            popover: {
                link: [
                    ['link', ['linkDialogShow', 'unlink']]
                ],
                table: [
                    ['add', ['addRowDown', 'addRowUp', 'addColLeft', 'addColRight']],
                    ['delete', ['deleteRow', 'deleteCol', 'deleteTable']]
                ],
                image: [
                    ['image', ['resizeFull', 'resizeHalf', 'resizeQuarter', 'resizeNone']],
                    ['float', ['floatLeft', 'floatRight', 'floatNone']],
                    ['remove', ['removeMedia']]
                ],
                air: [
                    ['color', ['color']],
                    ['font', ['bold', 'underline', 'clear']],
                    ['para', ['ul', 'paragraph']]
                ]
            },
            callbacks: {
                onInit: function() {
                    // إضافة أزرار لتغيير اتجاه النص
                    var rtlBtn = '<button type="button" class="btn btn-sm btn-outline-secondary" data-direction="rtl" title="من اليمين إلى اليسار"><i class="fas fa-align-right"></i></button>';
                    var ltrBtn = '<button type="button" class="btn btn-sm btn-outline-secondary" data-direction="ltr" title="من اليسار إلى اليمين"><i class="fas fa-align-left"></i></button>';

                    $('.note-toolbar').append('<div class="note-btn-group btn-group note-direction">' + rtlBtn + ltrBtn + '</div>');

                    // معالجة النقرات على أزرار الاتجاه
                    $('.note-direction button').on('click', function() {
                        var direction = $(this).data('direction');
                        var $editor = $('.note-editable');

                        if (direction === 'rtl') {
                            $editor.css('direction', 'rtl');
                            $editor.css('text-align', 'right');
                        } else {
                            $editor.css('direction', 'ltr');
                            $editor.css('text-align', 'left');
                        }
                    });

                    // تعيين اتجاه RTL كافتراضي
                    $('.note-editable').css('direction', 'rtl');
                    $('.note-editable').css('text-align', 'right');

                    // تعديل نص النافذة المنبثقة للرابط
                    setTimeout(function() {
                        // تعديل عناوين النافذة المنبثقة للرابط
                        $('.note-link-dialog .note-modal-title').text('إدراج رابط');
                        $('.note-link-dialog .note-form-label').eq(0).text('رابط URL:');
                        $('.note-link-dialog .note-form-label').eq(1).text('نص الرابط:');
                        $('.note-link-dialog .sn-checkbox-open-in-new-window').text('فتح في نافذة جديدة');
                        $('.note-link-dialog .note-btn-primary').text('إدراج');
                        $('.note-link-dialog .note-btn-secondary').text('إلغاء');

                        // تحسين مظهر النافذة المنبثقة
                        $('.note-link-dialog .note-input').attr('placeholder', 'https://example.com');
                        $('.note-link-dialog .note-link-text').attr('placeholder', 'نص الرابط');
                    }, 100);
                },

                onDialogShown: function(dialog) {
                    // تعديل نص النافذة المنبثقة للرابط عند فتحها
                    if ($(dialog).hasClass('note-link-dialog')) {
                        $('.note-link-dialog .note-modal-title').text('إدراج رابط');
                        $('.note-link-dialog .note-form-label').eq(0).text('رابط URL:');
                        $('.note-link-dialog .note-form-label').eq(1).text('نص الرابط:');
                        $('.note-link-dialog .sn-checkbox-open-in-new-window').text('فتح في نافذة جديدة');
                        $('.note-link-dialog .note-btn-primary').text('إدراج');
                        $('.note-link-dialog .note-btn-secondary').text('إلغاء');

                        // تحسين مظهر النافذة المنبثقة
                        $('.note-link-dialog .note-input').attr('placeholder', 'https://example.com');
                        $('.note-link-dialog .note-link-text').attr('placeholder', 'نص الرابط');
                    }
                }
            }
        });

        // Initialize Select2 for recipients selector
        $('.select2-recipients').select2({
            placeholder: 'اختر المستلمين...',
            allowClear: true,
            dir: 'rtl',
            templateResult: function(user) {
                if (!user.id) {
                    return user.text;
                }

                var $user = $(
                    '<div class="d-flex align-items-center">' +
                        '<div class="me-2">' +
                            '<i class="fas fa-user text-primary"></i>' +
                        '</div>' +
                        '<div>' +
                            '<div class="fw-bold">' + user.text.split(' (')[0] + '</div>' +
                            '<small class="text-muted">' + $(user.element).data('department') + '</small>' +
                        '</div>' +
                    '</div>'
                );
                return $user;
            },
            templateSelection: function(user) {
                if (!user.id) {
                    return user.text;
                }
                return user.text.split(' (')[0];
            }
        });
    });
</script>
{% endblock %}
