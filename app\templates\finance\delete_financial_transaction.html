{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
        <a href="{{ url_for('finance.financial_transactions') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i>العودة إلى المعاملات المالية
        </a>
    </div>

    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card shadow border-danger">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>تأكيد حذف المعاملة المالية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-danger" role="alert">
                        <h5 class="alert-heading">
                            <i class="fas fa-warning me-2"></i>تحذير!
                        </h5>
                        <p>أنت على وشك حذف معاملة مالية نهائياً. هذا الإجراء لا يمكن التراجع عنه.</p>
                        <hr>
                        <p class="mb-0">سيتم حذف المعاملة وجميع البنود المرتبطة بها وإلغاء تأثيرها على أرصدة الحسابات.</p>
                    </div>

                    <!-- تفاصيل المعاملة المراد حذفها -->
                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h6 class="mb-0">تفاصيل المعاملة المراد حذفها</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>رقم المعاملة:</strong></td>
                                            <td>{{ transaction.transaction_number }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>اسم المعاملة:</strong></td>
                                            <td>{{ transaction.transaction_name }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>الحالة:</strong></td>
                                            <td>
                                                {% if transaction.status == 'draft' %}
                                                <span class="badge bg-secondary">مسودة</span>
                                                {% elif transaction.status == 'pending' %}
                                                <span class="badge bg-warning">معلقة</span>
                                                {% elif transaction.status == 'paid' %}
                                                <span class="badge bg-success">مدفوعة</span>
                                                {% elif transaction.status == 'cancelled' %}
                                                <span class="badge bg-danger">ملغية</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>أنشأ بواسطة:</strong></td>
                                            <td>{{ transaction.created_by.first_name }} {{ transaction.created_by.last_name }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>تاريخ الإنشاء:</strong></td>
                                            <td>{{ transaction.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>عدد البنود:</strong></td>
                                            <td><span class="badge bg-info">{{ transaction.items.count() }}</span></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>

                            {% if transaction.notes %}
                            <div class="mt-3">
                                <h6 class="text-muted">الملاحظات:</h6>
                                <div class="alert alert-info">
                                    {{ transaction.notes }}
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- بنود المعاملة -->
                    {% if transaction.items.count() > 0 %}
                    <div class="card mb-4">
                        <div class="card-header bg-warning text-dark">
                            <h6 class="mb-0">
                                <i class="fas fa-list me-2"></i>بنود المعاملة التي سيتم حذفها ({{ transaction.items.count() }})
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>#</th>
                                            <th>الحساب</th>
                                            <th>الإجراء</th>
                                            <th>المبلغ</th>
                                            <th>تاريخ الإجراء</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for item in transaction.items %}
                                        <tr>
                                            <td>{{ loop.index }}</td>
                                            <td>
                                                <div>
                                                    <strong>{{ item.account.name }}</strong>
                                                    {% if item.account.account_number %}
                                                    <br><small class="text-muted">{{ item.account.account_number }}</small>
                                                    {% endif %}
                                                </div>
                                            </td>
                                            <td>
                                                {% if item.action == 'add' %}
                                                <span class="badge bg-success">إضافة</span>
                                                {% else %}
                                                <span class="badge bg-danger">خصم</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <strong class="{% if item.action == 'add' %}text-success{% else %}text-danger{% endif %}">
                                                    {% if item.action == 'add' %}+{% else %}-{% endif %}${{ "{:,.2f}".format(item.amount) }}
                                                </strong>
                                            </td>
                                            <td>{{ item.transaction_date.strftime('%Y-%m-%d %H:%M') }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- تأثير الحذف على النظام -->
                    <div class="card mb-4">
                        <div class="card-header bg-info text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-info-circle me-2"></i>تأثير الحذف على النظام
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-12">
                                    <ul class="list-group list-group-flush">
                                        <li class="list-group-item px-0">
                                            <i class="fas fa-times text-danger me-2"></i>
                                            سيتم حذف المعاملة المالية نهائياً
                                        </li>
                                        <li class="list-group-item px-0">
                                            <i class="fas fa-times text-danger me-2"></i>
                                            سيتم حذف جميع البنود المرتبطة ({{ transaction.items.count() }} بند)
                                        </li>
                                        {% if transaction.status == 'paid' %}
                                        <li class="list-group-item px-0">
                                            <i class="fas fa-undo text-warning me-2"></i>
                                            سيتم إلغاء تأثير المعاملة على أرصدة الحسابات
                                        </li>
                                        {% endif %}
                                        {% if transaction.attachments.count() > 0 %}
                                        <li class="list-group-item px-0">
                                            <i class="fas fa-paperclip text-warning me-2"></i>
                                            سيتم حذف {{ transaction.attachments.count() }} مرفق
                                        </li>
                                        {% endif %}
                                        {% if transaction.links.count() > 0 %}
                                        <li class="list-group-item px-0">
                                            <i class="fas fa-link text-warning me-2"></i>
                                            سيتم حذف {{ transaction.links.count() }} رابط
                                        </li>
                                        {% endif %}
                                        <li class="list-group-item px-0">
                                            <i class="fas fa-info text-info me-2"></i>
                                            سيتم الاحتفاظ بسجل المعاملة في قاعدة البيانات للمراجعة
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- تأثير على الحسابات -->
                    {% if transaction.status == 'paid' and transaction.items.count() > 0 %}
                    <div class="card mb-4">
                        <div class="card-header bg-warning text-dark">
                            <h6 class="mb-0">
                                <i class="fas fa-calculator me-2"></i>التأثير على أرصدة الحسابات
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-warning">
                                <strong>تحذير:</strong> سيتم إلغاء التأثيرات التالية على أرصدة الحسابات:
                            </div>
                            {% for item in transaction.items %}
                            <div class="d-flex justify-content-between align-items-center mb-2 p-2 border rounded">
                                <div>
                                    <strong>{{ item.account.name }}</strong>
                                    {% if item.account.account_number %}
                                    <br><small class="text-muted">{{ item.account.account_number }}</small>
                                    {% endif %}
                                </div>
                                <div>
                                    <span class="badge {% if item.action == 'add' %}bg-danger{% else %}bg-success{% endif %}">
                                        سيتم {% if item.action == 'add' %}خصم{% else %}إضافة{% endif %} ${{ "{:,.2f}".format(item.amount) }}
                                    </span>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}

                    <!-- أزرار التأكيد -->
                    <div class="d-flex justify-content-between align-items-center">
                        <a href="{{ url_for('finance.view_financial_transaction', id=transaction.id) }}" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i>إلغاء
                        </a>
                        
                        <div>
                            <a href="{{ url_for('finance.financial_transactions') }}" class="btn btn-outline-secondary me-2">
                                <i class="fas fa-arrow-right me-1"></i>العودة للقائمة
                            </a>
                            <a href="{{ url_for('finance.edit_financial_transaction', id=transaction.id) }}" class="btn btn-outline-warning me-2">
                                <i class="fas fa-edit me-1"></i>تعديل بدلاً من الحذف
                            </a>
                            <form method="POST" style="display: inline;" onsubmit="return confirmDelete()">
                                {{ csrf_token() }}
                                <button type="submit" class="btn btn-danger">
                                    <i class="fas fa-trash me-1"></i>تأكيد الحذف النهائي
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function confirmDelete() {
    const transactionName = "{{ transaction.transaction_name }}";
    const itemCount = {{ transaction.items.count() }};
    const isPaid = {{ 'true' if transaction.status == 'paid' else 'false' }};
    
    let message = `هل أنت متأكد تماماً من حذف المعاملة "${transactionName}"؟`;
    
    if (itemCount > 0) {
        message += `\n\nسيتم حذف ${itemCount} بند من المعاملة.`;
    }
    
    if (isPaid) {
        message += '\n\nسيتم إلغاء تأثير المعاملة على أرصدة الحسابات.';
    }
    
    message += '\n\nلا يمكن التراجع عن هذا الإجراء!';
    
    return confirm(message);
}

// Auto focus on cancel button for safety
document.addEventListener('DOMContentLoaded', function() {
    const cancelButton = document.querySelector('a[href*="view_financial_transaction"]');
    if (cancelButton) {
        cancelButton.focus();
    }
});
</script>
{% endblock %}
