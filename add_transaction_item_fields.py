#!/usr/bin/env python3
"""
Script to add new fields to financial_transaction_items table
"""

import sqlite3
import os

def add_transaction_item_fields():
    """Add new fields to financial_transaction_items table"""
    # الاتصال بقاعدة البيانات الصحيحة
    db_path = 'app/sparkle.db'
    if not os.path.exists(db_path):
        print('ملف قاعدة البيانات غير موجود في app/sparkle.db')
        return False
        
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # التحقق من وجود الحقول أولاً
        cursor.execute('PRAGMA table_info(financial_transaction_items)')
        columns = [column[1] for column in cursor.fetchall()]
        print(f'الحقول الموجودة: {columns}')
        
        # إضافة حقول بنود المعاملات المالية الجديدة
        if 'item_notes' not in columns:
            cursor.execute('ALTER TABLE financial_transaction_items ADD COLUMN item_notes TEXT')
            print('تم إضافة حقل item_notes')
            
        if 'currency_id' not in columns:
            # إضافة حقل العملة مع قيمة افتراضية (1 للدولار الأمريكي)
            cursor.execute('ALTER TABLE financial_transaction_items ADD COLUMN currency_id INTEGER DEFAULT 1')
            print('تم إضافة حقل currency_id')
            
            # إضافة foreign key constraint (إذا لم يكن موجوداً)
            try:
                cursor.execute('''
                    CREATE INDEX IF NOT EXISTS idx_financial_transaction_items_currency_id 
                    ON financial_transaction_items(currency_id)
                ''')
                print('تم إضافة فهرس للعملة')
            except Exception as e:
                print(f'تحذير: لم يتم إضافة الفهرس: {e}')
            
        conn.commit()
        print('✅ تم إضافة جميع حقول بنود المعاملات المالية بنجاح')
        return True
        
    except Exception as e:
        print(f'❌ خطأ: {e}')
        conn.rollback()
        return False
    finally:
        conn.close()

if __name__ == '__main__':
    add_transaction_item_fields()
