from flask import Blueprint, render_template, request
from flask_login import login_required, current_user
from datetime import datetime, timezone

from app.models.project import Project
from app.models.task import Task
from app.models.client import Client
from app.models.finance import Transaction
from app.models.notification import Notification

dashboard_bp = Blueprint('dashboard', __name__)

@dashboard_bp.route('/')
@dashboard_bp.route('/dashboard')
@login_required
def index():
    # Get statistics based on user role
    stats = {}

    # Get recent activities
    activities = []

    # Get unread notifications
    notifications = Notification.query.filter_by(user_id=current_user.id, is_read=False).order_by(Notification.created_at.desc()).limit(5).all()

    # For admin and managers, show overall statistics
    if current_user.has_role('admin') or current_user.has_role('manager'):
        stats['total_projects'] = Project.query.count()
        stats['active_projects'] = Project.query.filter_by(status='in_progress').count()
        stats['total_clients'] = Client.query.count()
        stats['pending_tasks'] = Task.query.filter_by(status='pending').count()

        # إزالة الإحصائيات المالية من لوحة التحكم
        # سيتم عرضها في النظام المالي الجديد فقط

        # Get pagination parameters for projects
        projects_page = request.args.get('projects_page', 1, type=int)
        projects_per_page = request.args.get('projects_per_page', 5, type=int)
        projects_status_filter = request.args.get('projects_status', 'all')

        # Build projects query
        projects_query = Project.query

        # Apply status filter to projects
        if projects_status_filter != 'all':
            projects_query = projects_query.filter(Project.status == projects_status_filter)

        # Order projects by creation date (newest first)
        projects_query = projects_query.order_by(Project.created_at.desc())

        # Paginate projects
        recent_projects = projects_query.paginate(page=projects_page, per_page=projects_per_page, error_out=False)

        # Get pagination parameters for clients
        clients_page = request.args.get('clients_page', 1, type=int)
        clients_per_page = request.args.get('clients_per_page', 5, type=int)

        # Build clients query
        clients_query = Client.query

        # Order clients by creation date (newest first)
        clients_query = clients_query.order_by(Client.created_at.desc())

        # Paginate clients
        recent_clients = clients_query.paginate(page=clients_page, per_page=clients_per_page, error_out=False)

        return render_template('dashboard/index.html',
                              title='Dashboard',
                              stats=stats,
                              notifications=notifications,
                              recent_projects=recent_projects,
                              recent_clients=recent_clients,
                              # Project pagination and filtering parameters
                              projects_status_filter=projects_status_filter,
                              projects_per_page=projects_per_page,
                              # Client pagination parameters
                              clients_per_page=clients_per_page)

    # For regular employees, show their assigned tasks and projects
    else:
        stats['my_projects'] = len(current_user.projects)
        stats['my_active_projects'] = sum(1 for project in current_user.projects if project.status == 'in_progress')
        stats['my_tasks'] = current_user.tasks.count()
        stats['my_pending_tasks'] = current_user.tasks.filter_by(status='pending').count()

        # Get pagination parameters for employee projects
        projects_page = request.args.get('projects_page', 1, type=int)
        projects_per_page = request.args.get('projects_per_page', 5, type=int)
        projects_status_filter = request.args.get('projects_status', 'all')

        # Get all projects for the current employee
        # Exclude cancelled projects unless specifically filtered
        if projects_status_filter == 'cancelled':
            all_projects = current_user.projects
        else:
            all_projects = [p for p in current_user.projects if p.status != 'cancelled']

        # Filter projects by status if needed
        if projects_status_filter != 'all':
            filtered_projects = [p for p in all_projects if p.status == projects_status_filter]
        else:
            filtered_projects = all_projects

        # Sort projects by creation date (newest first)
        sorted_projects = sorted(filtered_projects, key=lambda p: p.created_at if p.created_at else datetime.now(timezone.utc), reverse=True)

        # Manual pagination for projects
        total_projects = len(sorted_projects)
        total_pages = (total_projects + projects_per_page - 1) // projects_per_page
        start_idx = (projects_page - 1) * projects_per_page
        end_idx = min(start_idx + projects_per_page, total_projects)

        # Create a custom pagination object for projects
        class CustomPagination:
            def __init__(self, items, page, per_page, total):
                self.items = items
                self.page = page
                self.per_page = per_page
                self.total = total
                self.total_pages = (total + per_page - 1) // per_page

            @property
            def has_prev(self):
                return self.page > 1

            @property
            def has_next(self):
                return self.page < self.total_pages

            @property
            def prev_num(self):
                return self.page - 1

            @property
            def next_num(self):
                return self.page + 1

            def iter_pages(self, left_edge=2, left_current=2, right_current=2, right_edge=2):
                last = 0
                for num in range(1, self.total_pages + 1):
                    if num <= left_edge or \
                       (num > self.page - left_current - 1 and num < self.page + right_current) or \
                       num > self.total_pages - right_edge:
                        if last + 1 != num:
                            yield None
                        yield num
                        last = num

        recent_projects = CustomPagination(
            sorted_projects[start_idx:end_idx],
            projects_page,
            projects_per_page,
            total_projects
        )

        # Get pagination parameters for employee tasks
        tasks_page = request.args.get('tasks_page', 1, type=int)
        tasks_per_page = request.args.get('tasks_per_page', 5, type=int)
        tasks_status_filter = request.args.get('tasks_status', 'all')
        tasks_priority_filter = request.args.get('tasks_priority', 'all')

        # Build tasks query
        tasks_query = current_user.tasks

        # Apply status filter to tasks
        if tasks_status_filter != 'all':
            tasks_query = tasks_query.filter(Task.status == tasks_status_filter)

        # Apply priority filter to tasks
        if tasks_priority_filter != 'all':
            tasks_query = tasks_query.filter(Task.priority == tasks_priority_filter)

        # Order tasks by creation date (newest first)
        tasks_query = tasks_query.order_by(Task.created_at.desc())

        # Paginate tasks
        recent_tasks = tasks_query.paginate(page=tasks_page, per_page=tasks_per_page, error_out=False)

        return render_template('dashboard/index.html',
                              title='Dashboard',
                              stats=stats,
                              notifications=notifications,
                              recent_tasks=recent_tasks,
                              recent_projects=recent_projects,
                              # Project pagination and filtering parameters
                              projects_status_filter=projects_status_filter,
                              projects_per_page=projects_per_page,
                              # Task pagination and filtering parameters
                              tasks_status_filter=tasks_status_filter,
                              tasks_priority_filter=tasks_priority_filter,
                              tasks_per_page=tasks_per_page)
