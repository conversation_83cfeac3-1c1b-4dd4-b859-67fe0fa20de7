{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-folder-edit me-2"></i>{{ title }}: {{ folder.name }}
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">اسم المجلد <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name" value="{{ folder.name }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="sort_order" class="form-label">ترتيب العرض</label>
                                    <input type="number" class="form-control" id="sort_order" name="sort_order" value="{{ folder.sort_order }}">
                                    <div class="form-text">الرقم الأصغر يظهر أولاً</div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">الوصف</label>
                            <textarea class="form-control" id="description" name="description" rows="3">{{ folder.description or '' }}</textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="icon" class="form-label">الأيقونة</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i id="icon-preview" class="{{ folder.icon }}"></i>
                                        </span>
                                        <select class="form-select" id="icon" name="icon">
                                            <option value="fas fa-folder" {% if folder.icon == 'fas fa-folder' %}selected{% endif %}>📁 مجلد عادي</option>
                                            <option value="fas fa-folder-open" {% if folder.icon == 'fas fa-folder-open' %}selected{% endif %}>📂 مجلد مفتوح</option>
                                            <option value="fas fa-money-bill-wave" {% if folder.icon == 'fas fa-money-bill-wave' %}selected{% endif %}>💰 مالي</option>
                                            <option value="fas fa-chart-line" {% if folder.icon == 'fas fa-chart-line' %}selected{% endif %}>📈 إيرادات</option>
                                            <option value="fas fa-shopping-cart" {% if folder.icon == 'fas fa-shopping-cart' %}selected{% endif %}>🛒 مصروفات</option>
                                            <option value="fas fa-users" {% if folder.icon == 'fas fa-users' %}selected{% endif %}>👥 عملاء</option>
                                            <option value="fas fa-building" {% if folder.icon == 'fas fa-building' %}selected{% endif %}>🏢 أصول</option>
                                            <option value="fas fa-university" {% if folder.icon == 'fas fa-university' %}selected{% endif %}>🏦 بنوك</option>
                                            <option value="fas fa-credit-card" {% if folder.icon == 'fas fa-credit-card' %}selected{% endif %}>💳 بطاقات</option>
                                            <option value="fas fa-coins" {% if folder.icon == 'fas fa-coins' %}selected{% endif %}>🪙 عملات</option>
                                            <option value="fas fa-piggy-bank" {% if folder.icon == 'fas fa-piggy-bank' %}selected{% endif %}>🐷 مدخرات</option>
                                            <option value="fas fa-handshake" {% if folder.icon == 'fas fa-handshake' %}selected{% endif %}>🤝 شراكات</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="color" class="form-label">لون المجلد</label>
                                    <div class="input-group">
                                        <input type="color" class="form-control form-control-color" id="color" name="color" value="{{ folder.color }}">
                                        <input type="text" class="form-control" id="color_text" value="{{ folder.color }}" readonly>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="parent_id" class="form-label">المجلد الأب</label>
                                    <select class="form-select" id="parent_id" name="parent_id">
                                        <option value="">مجلد رئيسي</option>
                                        {% for parent_folder in all_folders %}
                                        <option value="{{ parent_folder.id }}" {% if folder.parent_id == parent_folder.id %}selected{% endif %}>
                                            {{ parent_folder.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                    <div class="form-text">اختر المجلد الأب لجعل هذا مجلداً فرعياً</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="is_expanded" name="is_expanded" {% if folder.is_expanded %}checked{% endif %}>
                                        <label class="form-check-label" for="is_expanded">
                                            مفتوح افتراضياً
                                        </label>
                                        <div class="form-text">هل يظهر المجلد مفتوحاً عند تحميل الصفحة؟</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('finance.folders') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-save me-2"></i>حفظ التغييرات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تحديث معاينة الأيقونة
    const iconSelect = document.getElementById('icon');
    const iconPreview = document.getElementById('icon-preview');
    
    iconSelect.addEventListener('change', function() {
        iconPreview.className = this.value;
    });
    
    // تحديث لون النص
    const colorPicker = document.getElementById('color');
    const colorText = document.getElementById('color_text');
    
    colorPicker.addEventListener('change', function() {
        colorText.value = this.value;
    });
});
</script>
{% endblock %}
