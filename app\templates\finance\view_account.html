{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
        <div>
            {% if current_user.has_role('admin') or current_user.has_role('finance') %}
            <a href="{{ url_for('finance.edit_account', id=account.id) }}" class="btn btn-warning me-2">
                <i class="fas fa-edit me-1"></i>تعديل الحساب
            </a>
            {% endif %}
            <a href="{{ url_for('finance.accounts') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-1"></i>العودة إلى شجرة الحسابات
            </a>
        </div>
    </div>

    <!-- فلترة المعاملات -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header bg-light">
                    <h6 class="mb-0">
                        <i class="fas fa-filter me-2"></i>فلترة المعاملات حسب الفترة الزمنية
                    </h6>
                </div>
                <div class="card-body">
                    <form method="GET" action="{{ url_for('finance.view_account', id=account.id) }}" id="filter-form">
                        <div class="row">
                            <div class="col-md-3">
                                <label for="filter_type" class="form-label">نوع الفلترة</label>
                                <select class="form-select" id="filter_type" name="filter_type" onchange="toggleDateInputs()">
                                    <option value="">بدون فلترة</option>
                                    <option value="month" {% if request.args.get('filter_type') == 'month' %}selected{% endif %}>شهر محدد</option>
                                    <option value="range" {% if request.args.get('filter_type') == 'range' %}selected{% endif %}>فترة زمنية</option>
                                    <option value="year" {% if request.args.get('filter_type') == 'year' %}selected{% endif %}>سنة محددة</option>
                                </select>
                            </div>
                            <div class="col-md-3" id="month-input" style="display: none;">
                                <label for="filter_month" class="form-label">الشهر والسنة</label>
                                <input type="month" class="form-control" id="filter_month" name="filter_month"
                                       value="{{ request.args.get('filter_month', '') }}">
                            </div>
                            <div class="col-md-3" id="year-input" style="display: none;">
                                <label for="filter_year" class="form-label">السنة</label>
                                <input type="number" class="form-control" id="filter_year" name="filter_year"
                                       min="2020" max="2030" value="{{ request.args.get('filter_year', '') }}">
                            </div>
                            <div class="col-md-3" id="start-date-input" style="display: none;">
                                <label for="start_date" class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="start_date" name="start_date"
                                       value="{{ request.args.get('start_date', '') }}">
                            </div>
                            <div class="col-md-3" id="end-date-input" style="display: none;">
                                <label for="end_date" class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="end_date" name="end_date"
                                       value="{{ request.args.get('end_date', '') }}">
                            </div>
                            <div class="col-md-3 d-flex align-items-end">
                                <div class="btn-group w-100">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search me-1"></i>تطبيق الفلتر
                                    </button>
                                    <a href="{{ url_for('finance.view_account', id=account.id) }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-times me-1"></i>إزالة الفلتر
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- إشعار الفلترة -->
    {% if filter_applied %}
    <div class="row mb-3">
        <div class="col-md-12">
            <div class="alert alert-info alert-dismissible fade show" role="alert">
                <i class="fas fa-info-circle me-2"></i>
                <strong>تم تطبيق فلتر:</strong>
                {% if request.args.get('filter_type') == 'month' %}
                    عرض المعاملات لشهر {{ request.args.get('filter_month') }}
                {% elif request.args.get('filter_type') == 'year' %}
                    عرض المعاملات لسنة {{ request.args.get('filter_year') }}
                {% elif request.args.get('filter_type') == 'range' %}
                    عرض المعاملات من {{ request.args.get('start_date') }} إلى {{ request.args.get('end_date') }}
                {% endif %}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
    </div>
    {% endif %}

    <div class="row">
        <div class="col-md-8">
            <!-- تفاصيل الحساب -->
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>تفاصيل الحساب
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>اسم الحساب:</strong></td>
                                    <td>{{ account.name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>رقم الحساب:</strong></td>
                                    <td>{{ account.account_number or 'غير محدد' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>نوع الحساب:</strong></td>
                                    <td>
                                        {% if account.account_type == 'cash' %}
                                        <span class="badge bg-warning">نقدي</span>
                                        {% elif account.account_type == 'bank' %}
                                        <span class="badge bg-primary">بنكي</span>
                                        {% elif account.account_type == 'income' %}
                                        <span class="badge bg-success">دخل</span>
                                        {% elif account.account_type == 'expense' %}
                                        <span class="badge bg-danger">مصروف</span>
                                        {% elif account.account_type == 'pending' %}
                                        <span class="badge bg-warning">معلق</span>
                                        {% elif account.account_type == 'overdue' %}
                                        <span class="badge bg-danger">متأخر</span>
                                        {% else %}
                                        <span class="badge bg-secondary">{{ account.account_type }}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>الرصيد الحالي:</strong></td>
                                    <td>
                                        <span class="h5 {% if account.balance >= 0 %}text-success{% else %}text-danger{% endif %}">
                                            {% if account.currency %}{{ account.currency.symbol }}{% else %}${% endif %}{{ "{:,.2f}".format(account.balance) }}
                                        </span>
                                    </td>
                                </tr>
                                {% if filter_applied and filtered_balance is not none %}
                                <tr>
                                    <td><strong>رصيد الفترة المحددة:</strong></td>
                                    <td>
                                        <span class="h6 {% if filtered_balance >= 0 %}text-info{% else %}text-warning{% endif %}">
                                            {% if account.currency %}{{ account.currency.symbol }}{% else %}${% endif %}{{ "{:,.2f}".format(filtered_balance) }}
                                        </span>
                                        <small class="text-muted d-block">
                                            {% if request.args.get('filter_type') == 'month' %}
                                                لشهر {{ request.args.get('filter_month') }}
                                            {% elif request.args.get('filter_type') == 'year' %}
                                                لسنة {{ request.args.get('filter_year') }}
                                            {% elif request.args.get('filter_type') == 'range' %}
                                                من {{ request.args.get('start_date') }} إلى {{ request.args.get('end_date') }}
                                            {% endif %}
                                        </small>
                                    </td>
                                </tr>
                                {% endif %}
                                <tr>
                                    <td><strong>الحساب الأب:</strong></td>
                                    <td>
                                        {% if account.parent %}
                                        <a href="{{ url_for('finance.view_account', id=account.parent.id) }}">
                                            {{ account.parent.name }}
                                        </a>
                                        {% else %}
                                        حساب رئيسي
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>العملة:</strong></td>
                                    <td>
                                        {% if account.currency %}
                                        <span class="badge bg-primary">{{ account.currency.symbol }} - {{ account.currency.name }}</span>
                                        {% else %}
                                        <span class="badge bg-secondary">غير محدد</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>لون الحساب:</strong></td>
                                    <td>
                                        <span class="badge" style="background-color: {{ account.account_color or '#007bff' }}; color: white;">
                                            {{ account.account_color or '#007bff' }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>الحالة:</strong></td>
                                    <td>
                                        {% if account.is_active %}
                                        <span class="badge bg-success">نشط</span>
                                        {% else %}
                                        <span class="badge bg-secondary">غير نشط</span>
                                        {% endif %}
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    {% if account.description %}
                    <div class="row mt-3">
                        <div class="col-md-12">
                            <h6 class="text-muted">الوصف:</h6>
                            <div class="alert alert-info">
                                {{ account.description }}
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- الحسابات الفرعية -->
            {% if account.children %}
            <div class="card shadow mt-4">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-sitemap me-2"></i>الحسابات الفرعية ({{ account.children|length }})
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for child in account.children %}
                        <div class="col-md-6 mb-3">
                            <div class="card border-info">
                                <div class="card-body py-2">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-1">
                                                <a href="{{ url_for('finance.view_account', id=child.id) }}">
                                                    {{ child.name }}
                                                </a>
                                            </h6>
                                            <small class="text-muted">{{ child.account_number or 'بدون رقم' }}</small>
                                        </div>
                                        <div class="text-end">
                                            <span class="badge {% if child.balance >= 0 %}bg-success{% else %}bg-danger{% endif %}">
                                                ${{ "{:,.2f}".format(child.balance) }}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- المعاملات المالية -->
            {% if financial_transactions %}
            <div class="card shadow mt-4">
                <div class="card-header bg-success text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">
                            <i class="fas fa-exchange-alt me-2"></i>المعاملات المالية المرتبطة ({{ financial_transactions.total if financial_transactions.total is defined else financial_transactions|length }})
                        </h6>
                        <button class="btn btn-outline-light btn-sm" type="button" data-bs-toggle="collapse" data-bs-target="#transactionsFilter" aria-expanded="false">
                            <i class="fas fa-filter me-1"></i>فلترة
                        </button>
                    </div>
                </div>

                <!-- فلترة المعاملات -->
                <div class="collapse" id="transactionsFilter">
                    <div class="card-header bg-light">
                        <form method="GET" class="row g-3">
                            <!-- الحفاظ على معاملات الفلترة الأخرى -->
                            {% for key, value in request.args.items() %}
                                {% if key not in ['trans_page', 'trans_per_page', 'trans_action', 'trans_status', 'trans_date_from', 'trans_date_to'] %}
                                <input type="hidden" name="{{ key }}" value="{{ value }}">
                                {% endif %}
                            {% endfor %}

                            <div class="col-md-3">
                                <label class="form-label">الإجراء</label>
                                <select name="trans_action" class="form-select form-select-sm">
                                    <option value="">جميع الإجراءات</option>
                                    <option value="add" {% if request.args.get('trans_action') == 'add' %}selected{% endif %}>إضافة</option>
                                    <option value="subtract" {% if request.args.get('trans_action') == 'subtract' %}selected{% endif %}>خصم</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">الحالة</label>
                                <select name="trans_status" class="form-select form-select-sm">
                                    <option value="">جميع الحالات</option>
                                    <option value="pending" {% if request.args.get('trans_status') == 'pending' %}selected{% endif %}>معلق</option>
                                    <option value="completed" {% if request.args.get('trans_status') == 'completed' %}selected{% endif %}>مكتمل</option>
                                    <option value="cancelled" {% if request.args.get('trans_status') == 'cancelled' %}selected{% endif %}>ملغي</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">من تاريخ</label>
                                <input type="date" name="trans_date_from" class="form-control form-control-sm" value="{{ request.args.get('trans_date_from', '') }}">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">إلى تاريخ</label>
                                <input type="date" name="trans_date_to" class="form-control form-control-sm" value="{{ request.args.get('trans_date_to', '') }}">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">عدد النتائج</label>
                                <select name="trans_per_page" class="form-select form-select-sm">
                                    <option value="10" {% if request.args.get('trans_per_page', '10') == '10' %}selected{% endif %}>10</option>
                                    <option value="25" {% if request.args.get('trans_per_page', '10') == '25' %}selected{% endif %}>25</option>
                                    <option value="50" {% if request.args.get('trans_per_page', '10') == '50' %}selected{% endif %}>50</option>
                                    <option value="100" {% if request.args.get('trans_per_page', '10') == '100' %}selected{% endif %}>100</option>
                                </select>
                            </div>
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary btn-sm">
                                    <i class="fas fa-search me-1"></i>تطبيق الفلتر
                                </button>
                                <a href="{{ url_for('finance.view_account', id=account.id) }}" class="btn btn-secondary btn-sm">
                                    <i class="fas fa-times me-1"></i>إزالة الفلتر
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>رقم المعاملة</th>
                                    <th>اسم المعاملة</th>
                                    <th>الإجراء</th>
                                    <th>المبلغ</th>
                                    <th>التاريخ</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in financial_transactions %}
                                <tr>
                                    <td>
                                        <a href="{{ url_for('finance.view_financial_transaction', id=item.transaction.id) }}">
                                            {{ item.transaction.transaction_number }}
                                        </a>
                                    </td>
                                    <td>{{ item.transaction.transaction_name }}</td>
                                    <td>
                                        {% if item.action == 'add' %}
                                        <span class="badge bg-success">إضافة</span>
                                        {% else %}
                                        <span class="badge bg-danger">خصم</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <strong class="{% if item.action == 'add' %}text-success{% else %}text-danger{% endif %}">
                                            {% if item.action == 'add' %}+{% else %}-{% endif %}${{ "{:,.2f}".format(item.amount) }}
                                        </strong>
                                    </td>
                                    <td>{{ item.transaction_date.strftime('%Y-%m-%d %H:%M') }}</td>
                                    <td>
                                        {% if item.transaction.status == 'draft' %}
                                        <span class="badge bg-secondary">مسودة</span>
                                        {% elif item.transaction.status == 'pending' %}
                                        <span class="badge bg-warning">معلقة</span>
                                        {% elif item.transaction.status == 'paid' %}
                                        <span class="badge bg-success">مدفوعة</span>
                                        {% elif item.transaction.status == 'cancelled' %}
                                        <span class="badge bg-danger">ملغية</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- تقسيم الصفحات للمعاملات -->
                    {% if financial_transactions.pages is defined and financial_transactions.pages > 1 %}
                    <div class="card-footer">
                        <nav aria-label="تقسيم صفحات المعاملات المالية">
                            <ul class="pagination pagination-sm justify-content-center mb-0">
                                {% if financial_transactions.has_prev %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('finance.view_account', id=account.id, trans_page=financial_transactions.prev_num, **request.args) }}">السابق</a>
                                </li>
                                {% endif %}

                                {% for page_num in financial_transactions.iter_pages() %}
                                    {% if page_num %}
                                        {% if page_num != financial_transactions.page %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('finance.view_account', id=account.id, trans_page=page_num, **request.args) }}">{{ page_num }}</a>
                                        </li>
                                        {% else %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ page_num }}</span>
                                        </li>
                                        {% endif %}
                                    {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link">...</span>
                                    </li>
                                    {% endif %}
                                {% endfor %}

                                {% if financial_transactions.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('finance.view_account', id=account.id, trans_page=financial_transactions.next_num, **request.args) }}">التالي</a>
                                </li>
                                {% endif %}
                            </ul>
                        </nav>
                        <div class="text-center mt-2">
                            <small class="text-muted">
                                عرض {{ financial_transactions.per_page * (financial_transactions.page - 1) + 1 }} -
                                {{ financial_transactions.per_page * financial_transactions.page if financial_transactions.page < financial_transactions.pages else financial_transactions.total }}
                                من {{ financial_transactions.total }} معاملة
                            </small>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endif %}

            <!-- الرواتب المرتبطة -->
            {% if salaries_paginated %}
            <div class="card shadow mt-4">
                <div class="card-header bg-warning text-dark d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        <i class="fas fa-money-bill-wave me-2"></i>الرواتب المرتبطة ({{ salaries_paginated.total }})
                    </h6>
                    <div class="d-flex align-items-center">
                        <select class="form-select form-select-sm me-2" onchange="changeSalariesPerPage(this.value)" style="width: auto;">
                            <option value="10" {% if salaries_per_page == 10 %}selected{% endif %}>10 رواتب</option>
                            <option value="25" {% if salaries_per_page == 25 %}selected{% endif %}>25 راتب</option>
                            <option value="50" {% if salaries_per_page == 50 %}selected{% endif %}>50 راتب</option>
                            <option value="100" {% if salaries_per_page == 100 %}selected{% endif %}>100 راتب</option>
                        </select>
                    </div>
                </div>

                <!-- فلاتر الرواتب -->
                <div class="card-body border-bottom">
                    <form method="GET" action="{{ url_for('finance.view_account', id=account.id) }}" class="row g-3">
                        <!-- الاحتفاظ بمعاملات الفلترة الأخرى -->
                        {% if request.args.get('start_date') %}
                        <input type="hidden" name="start_date" value="{{ request.args.get('start_date') }}">
                        {% endif %}
                        {% if request.args.get('end_date') %}
                        <input type="hidden" name="end_date" value="{{ request.args.get('end_date') }}">
                        {% endif %}
                        {% if request.args.get('trans_page') %}
                        <input type="hidden" name="trans_page" value="{{ request.args.get('trans_page') }}">
                        {% endif %}

                        <div class="col-md-3">
                            <label for="salary_status" class="form-label">حالة الراتب</label>
                            <select class="form-select" name="salary_status" id="salary_status">
                                <option value="">جميع الحالات</option>
                                <option value="draft" {% if salary_status_filter == 'draft' %}selected{% endif %}>مسودة</option>
                                <option value="pending" {% if salary_status_filter == 'pending' %}selected{% endif %}>معلق</option>
                                <option value="paid" {% if salary_status_filter == 'paid' %}selected{% endif %}>مدفوع</option>
                                <option value="cancelled" {% if salary_status_filter == 'cancelled' %}selected{% endif %}>ملغي</option>
                            </select>
                        </div>

                        <div class="col-md-3">
                            <label for="salary_employee" class="form-label">اسم الموظف</label>
                            <input type="text" class="form-control" name="salary_employee" id="salary_employee"
                                   value="{{ salary_employee_filter or '' }}" placeholder="البحث بالاسم...">
                        </div>

                        <div class="col-md-2">
                            <label for="salaries_per_page" class="form-label">عدد النتائج</label>
                            <select class="form-select" name="salaries_per_page" id="salaries_per_page">
                                <option value="10" {% if salaries_per_page == 10 %}selected{% endif %}>10</option>
                                <option value="25" {% if salaries_per_page == 25 %}selected{% endif %}>25</option>
                                <option value="50" {% if salaries_per_page == 50 %}selected{% endif %}>50</option>
                                <option value="100" {% if salaries_per_page == 100 %}selected{% endif %}>100</option>
                            </select>
                        </div>

                        <div class="col-md-4">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i>بحث
                                </button>
                                <a href="{{ url_for('finance.view_account', id=account.id) }}" class="btn btn-secondary">
                                    <i class="fas fa-times me-1"></i>مسح
                                </a>
                            </div>
                        </div>
                    </form>
                </div>

                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>الموظف</th>
                                    <th>المبلغ</th>
                                    <th>الحالة</th>
                                    <th>نوع الحساب</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for salary in salaries_paginated.items %}
                                <tr>
                                    <td>{{ salary.employee_name }}</td>
                                    <td>${{ "{:,.2f}".format(salary.total_amount) }}</td>
                                    <td>
                                        {% if salary.status == 'pending' %}
                                            <span class="badge bg-warning">معلق</span>
                                        {% elif salary.status == 'paid' %}
                                            <span class="badge bg-success">مدفوع</span>
                                        {% elif salary.status == 'draft' %}
                                            <span class="badge bg-secondary">مسودة</span>
                                        {% elif salary.status == 'cancelled' %}
                                            <span class="badge bg-danger">ملغي</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if salary.pending_account_id == account.id %}
                                            <span class="badge bg-warning">حساب معلق</span>
                                        {% elif salary.paid_account_id == account.id %}
                                            <span class="badge bg-success">حساب مدفوع</span>
                                        {% elif salary.deduction_account_id == account.id %}
                                            <span class="badge bg-danger">حساب خصم</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ salary.created_at.strftime('%Y-%m-%d') }}</td>
                                    <td>
                                        <a href="{{ url_for('finance.view_salary', id=salary.id) }}"
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- تقسيم الصفحات للرواتب -->
                    {% if salaries_paginated.pages > 1 %}
                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <div>
                            <small class="text-muted">
                                عرض {{ salaries_paginated.per_page * (salaries_paginated.page - 1) + 1 }} إلى
                                {{ salaries_paginated.per_page * (salaries_paginated.page - 1) + salaries_paginated.items|length }}
                                من {{ salaries_paginated.total }} راتب
                            </small>
                        </div>
                        <nav aria-label="تقسيم صفحات الرواتب">
                            <ul class="pagination pagination-sm mb-0">
                                {% if salaries_paginated.has_prev %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('finance.view_account', id=account.id,
                                        salaries_page=salaries_paginated.prev_num,
                                        salaries_per_page=salaries_per_page,
                                        salary_status=salary_status_filter,
                                        salary_employee=salary_employee_filter,
                                        start_date=request.args.get('start_date'),
                                        end_date=request.args.get('end_date')) }}">السابق</a>
                                </li>
                                {% endif %}

                                {% for page_num in salaries_paginated.iter_pages() %}
                                    {% if page_num %}
                                        {% if page_num != salaries_paginated.page %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('finance.view_account', id=account.id,
                                                salaries_page=page_num,
                                                salaries_per_page=salaries_per_page,
                                                salary_status=salary_status_filter,
                                                salary_employee=salary_employee_filter,
                                                start_date=request.args.get('start_date'),
                                                end_date=request.args.get('end_date')) }}">{{ page_num }}</a>
                                        </li>
                                        {% else %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ page_num }}</span>
                                        </li>
                                        {% endif %}
                                    {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link">…</span>
                                    </li>
                                    {% endif %}
                                {% endfor %}

                                {% if salaries_paginated.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('finance.view_account', id=account.id,
                                        salaries_page=salaries_paginated.next_num,
                                        salaries_per_page=salaries_per_page,
                                        salary_status=salary_status_filter,
                                        salary_employee=salary_employee_filter,
                                        start_date=request.args.get('start_date'),
                                        end_date=request.args.get('end_date')) }}">التالي</a>
                                </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                    {% endif %}
            {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-money-bill-wave fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد رواتب مرتبطة بهذا الحساب</p>
                    </div>
            {% endif %}
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- ملخص الحساب -->
            <div class="card shadow">
                <div class="card-header bg-light">
                    <h6 class="mb-0">ملخص الحساب</h6>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <div class="display-4 {% if account.balance >= 0 %}text-success{% else %}text-danger{% endif %}">
                            {% if account.currency %}{{ account.currency.symbol }}{% else %}${% endif %}{{ "{:,.2f}".format(account.balance) }}
                        </div>
                        <small class="text-muted">الرصيد الحالي</small>
                    </div>
                    
                    <hr>
                    
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="text-success">
                                <i class="fas fa-arrow-up fa-2x"></i>
                                <div class="mt-2">
                                    <strong>{{ financial_transactions|selectattr('action', 'equalto', 'add')|list|length }}</strong>
                                    <br><small>عمليات إضافة</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-danger">
                                <i class="fas fa-arrow-down fa-2x"></i>
                                <div class="mt-2">
                                    <strong>{{ financial_transactions|selectattr('action', 'equalto', 'subtract')|list|length }}</strong>
                                    <br><small>عمليات خصم</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- إجراءات سريعة -->
            {% if current_user.has_role('admin') or current_user.has_role('finance') %}
            <div class="card shadow mt-4">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0">إجراءات سريعة</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('finance.edit_account', id=account.id) }}" class="btn btn-warning btn-sm">
                            <i class="fas fa-edit me-1"></i>تعديل الحساب
                        </a>
                        <a href="{{ url_for('finance.add_account') }}?parent_id={{ account.id }}" class="btn btn-success btn-sm">
                            <i class="fas fa-plus me-1"></i>إضافة حساب فرعي
                        </a>
                        <hr>
                        <button type="button" class="btn btn-warning btn-sm mb-2" onclick="confirmResetAccount()">
                            <i class="fas fa-undo me-1"></i>تصفير الحساب
                        </button>
                        <a href="{{ url_for('finance.delete_account', id=account.id) }}" class="btn btn-outline-danger btn-sm">
                            <i class="fas fa-trash me-1"></i>حذف الحساب
                        </a>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function confirmResetAccount() {
    const accountName = "{{ account.name }}";
    const currentBalance = {{ account.balance }};
    const transactionCount = {{ financial_transactions.total if financial_transactions.total is defined else 0 }};
    const salaryCount = {{ salaries_paginated.total if salaries_paginated else 0 }};

    let message = `⚠️ تحذير خطير! ⚠️\n\n`;
    message += `أنت على وشك تصفير الحساب "${accountName}"\n\n`;
    message += `هذا الإجراء سيؤدي إلى:\n`;
    message += `• تصفير الرصيد الحالي: $${currentBalance.toFixed(2)}\n`;

    if (transactionCount > 0) {
        message += `• حذف ${transactionCount} معاملة مالية مرتبطة\n`;
    }

    if (salaryCount > 0) {
        message += `• حذف ${salaryCount} راتب مرتبط\n`;
    }

    message += `• حذف جميع الإجراءات المالية المرتبطة بهذا الحساب\n\n`;
    message += `⚠️ لا يمكن التراجع عن هذا الإجراء! ⚠️\n\n`;
    message += `هل أنت متأكد تماماً من المتابعة؟`;

    if (confirm(message)) {
        // تأكيد إضافي
        const finalConfirm = prompt(`للتأكيد النهائي، اكتب اسم الحساب "${accountName}" بالضبط:`);

        if (finalConfirm === accountName) {
            // إرسال طلب تصفير الحساب
            fetch(`{{ url_for('finance.reset_account', id=account.id) }}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': '{{ csrf_token() }}'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('تم تصفير الحساب بنجاح');
                    location.reload();
                } else {
                    alert('حدث خطأ: ' + data.message);
                }
            })
            .catch(error => {
                alert('حدث خطأ في الاتصال');
                console.error('Error:', error);
            });
        } else {
            alert('لم يتم تأكيد اسم الحساب بشكل صحيح. تم إلغاء العملية.');
        }
    }
}

// إدارة مدخلات الفلترة
function toggleDateInputs() {
    const filterType = document.getElementById('filter_type').value;
    const monthInput = document.getElementById('month-input');
    const yearInput = document.getElementById('year-input');
    const startDateInput = document.getElementById('start-date-input');
    const endDateInput = document.getElementById('end-date-input');

    // إخفاء جميع المدخلات
    monthInput.style.display = 'none';
    yearInput.style.display = 'none';
    startDateInput.style.display = 'none';
    endDateInput.style.display = 'none';

    // إظهار المدخلات المناسبة
    switch(filterType) {
        case 'month':
            monthInput.style.display = 'block';
            break;
        case 'year':
            yearInput.style.display = 'block';
            break;
        case 'range':
            startDateInput.style.display = 'block';
            endDateInput.style.display = 'block';
            break;
    }
}

// تشغيل الدالة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    toggleDateInputs();
});

function changeSalariesPerPage(perPage) {
    const url = new URL(window.location);
    url.searchParams.set('salaries_per_page', perPage);
    url.searchParams.set('salaries_page', 1); // Reset to first page
    window.location.href = url.toString();
}
</script>
{% endblock %}
