{% extends 'base.html' %}

{% block styles %}
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>تعديل مشروع</h1>
    <div>
        <a href="{{ url_for('project.view', id=project.id) }}" class="btn btn-secondary me-2">
            <i class="fas fa-arrow-right me-1"></i>العودة للتفاصيل
        </a>
        <a href="{{ url_for('project.index') }}" class="btn btn-secondary">
            <i class="fas fa-list me-1"></i>قائمة المشاريع
        </a>
    </div>
</div>

<div class="card shadow">
    <div class="card-header bg-light">
        <h5 class="mb-0">تعديل بيانات مشروع {{ project.name }}</h5>
    </div>
    <div class="card-body">
        <form method="POST" action="{{ url_for('project.edit', id=project.id) }}">
            <div class="row">
                <!-- Basic Information -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header bg-light">
                            <h6 class="mb-0">المعلومات الأساسية</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="name" class="form-label">اسم المشروع <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" value="{{ project.name }}" required>
                            </div>

                            <div class="mb-3">
                                <label for="description" class="form-label">وصف المشروع</label>
                                <textarea class="form-control" id="description" name="description" rows="4">{{ project.description or '' }}</textarea>
                            </div>

                            <div class="mb-3">
                                <label for="start_date" class="form-label">تاريخ البدء</label>
                                <input type="date" class="form-control" id="start_date" name="start_date" value="{{ project.start_date.strftime('%Y-%m-%d') }}">
                            </div>

                            <div class="mb-3">
                                <label for="end_date" class="form-label">تاريخ الانتهاء المتوقع</label>
                                <input type="date" class="form-control" id="end_date" name="end_date" value="{{ project.end_date.strftime('%Y-%m-%d') if project.end_date else '' }}">
                            </div>

                            <div class="mb-3">
                                <label for="invoice_approval_date" class="form-label">تاريخ اعتماد الفاتورة</label>
                                <input type="date" class="form-control" id="invoice_approval_date" name="invoice_approval_date" value="{{ project.invoice_approval_date.strftime('%Y-%m-%d') if project.invoice_approval_date else '' }}">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Additional Information -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header bg-light">
                            <h6 class="mb-0">معلومات إضافية</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="status" class="form-label">الحالة</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="pending" {% if project.status == 'pending' %}selected{% endif %}>معلق</option>
                                    <option value="in_progress" {% if project.status == 'in_progress' %}selected{% endif %}>قيد التنفيذ</option>
                                    <option value="completed" {% if project.status == 'completed' %}selected{% endif %}>مكتمل</option>
                                    <option value="cancelled" {% if project.status == 'cancelled' %}selected{% endif %}>ملغي</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="priority" class="form-label">الأولوية</label>
                                <select class="form-select" id="priority" name="priority">
                                    <option value="low" {% if project.priority == 'low' %}selected{% endif %}>منخفضة</option>
                                    <option value="medium" {% if project.priority == 'medium' %}selected{% endif %}>متوسطة</option>
                                    <option value="high" {% if project.priority == 'high' %}selected{% endif %}>عالية</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="department_id" class="form-label">القسم الرئيسي</label>
                                <select class="form-select" id="department_id" name="department_id">
                                    <option value="">-- اختر القسم --</option>
                                    {% for department in departments %}
                                    <option value="{{ department.id }}" {% if project.department_id == department.id %}selected{% endif %}>{{ department.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="department_ids" class="form-label">الأقسام المشاركة</label>
                                <select class="form-select select2-multiple" id="department_ids" name="department_ids" multiple>
                                    {% for department in departments %}
                                    <option value="{{ department.id }}" {% if department in project.departments %}selected{% endif %}>{{ department.name }}</option>
                                    {% endfor %}
                                </select>
                                <small class="text-muted">يمكنك اختيار أكثر من قسم</small>
                            </div>

                            <div class="mb-3">
                                <label for="client_id" class="form-label">العميل</label>
                                <select class="form-select" id="client_id" name="client_id">
                                    <option value="">-- اختر العميل --</option>
                                    {% for client in clients %}
                                    <option value="{{ client.id }}" {% if project.client_id == client.id %}selected{% endif %}>{{ client.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>

                            {% if current_user.has_role('admin') %}
                            <div class="mb-3">
                                <label for="manager_id" class="form-label">مدير المشروع الرئيسي (اختياري)</label>
                                <select class="form-select" id="manager_id" name="manager_id">
                                    <option value="">-- بدون مدير --</option>
                                    {% for manager in potential_managers %}
                                    <option value="{{ manager.id }}" {% if project.manager_id == manager.id %}selected{% endif %}>{{ manager.get_full_name() }}</option>
                                    {% endfor %}
                                </select>
                                <small class="text-muted">يمكن ترك هذا الحقل فارغاً</small>
                            </div>

                            <div class="mb-3">
                                <label for="manager_ids" class="form-label">مدراء المشروع المشاركين</label>
                                <select class="form-select select2-multiple" id="manager_ids" name="manager_ids" multiple>
                                    {% for manager in potential_managers %}
                                    <option value="{{ manager.id }}" {% if manager in project.managers %}selected{% endif %}>{{ manager.get_full_name() }}</option>
                                    {% endfor %}
                                </select>
                                <small class="text-muted">يمكنك اختيار أكثر من مدير</small>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- اختيار الموظفين المشاركين -->
            <div class="card shadow mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">الموظفين المشاركين (اختياري)</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">اختر الموظفين المشاركين في المشروع</label>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th style="width: 50px;">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="select-all">
                                            </div>
                                        </th>
                                        <th>الاسم</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>القسم</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for user in all_users %}
                                    <tr>
                                        <td>
                                            <div class="form-check">
                                                <input class="form-check-input user-checkbox" type="checkbox" id="user_{{ user.id }}" name="member_ids" value="{{ user.id }}" {% if user in project.members %}checked{% endif %}>
                                            </div>
                                        </td>
                                        <td>{{ user.get_full_name() }}</td>
                                        <td>{{ user.email }}</td>
                                        <td>{{ user.department.name if user.department else 'غير محدد' }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <a href="{{ url_for('project.view', id=project.id) }}" class="btn btn-secondary me-md-2">إلغاء</a>
                <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
            </div>

            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    // Select all checkbox functionality
                    const selectAllCheckbox = document.getElementById('select-all');
                    const userCheckboxes = document.querySelectorAll('.user-checkbox');

                    if (selectAllCheckbox) {
                        selectAllCheckbox.addEventListener('change', function() {
                            const isChecked = this.checked;

                            userCheckboxes.forEach(function(checkbox) {
                                checkbox.checked = isChecked;
                            });
                        });

                        // Update "select all" checkbox when individual checkboxes change
                        userCheckboxes.forEach(function(checkbox) {
                            checkbox.addEventListener('change', function() {
                                const allChecked = Array.from(userCheckboxes).every(function(cb) {
                                    return cb.checked;
                                });

                                const anyChecked = Array.from(userCheckboxes).some(function(cb) {
                                    return cb.checked;
                                });

                                selectAllCheckbox.checked = allChecked;
                                selectAllCheckbox.indeterminate = anyChecked && !allChecked;
                            });
                        });
                    }
                });

                // Initialize Select2 for multiple selects
                $('.select2-multiple').select2({
                    placeholder: 'اختر من القائمة...',
                    allowClear: true,
                    dir: 'rtl'
                });
            </script>
        </form>
    </div>
</div>

{% block scripts %}
<!-- Select2 JS -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
{% endblock %}
{% endblock %}
