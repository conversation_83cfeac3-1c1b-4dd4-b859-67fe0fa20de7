{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-sitemap text-primary me-2"></i>{{ title }}
            </h1>
            <p class="text-muted mb-0">إدارة شجرة الحسابات المالية والأرصدة</p>
        </div>
        {% if current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance') %}
        <div class="btn-group">
            <a href="{{ url_for('finance.folders') }}" class="btn btn-info me-2">
                <i class="fas fa-folder-plus me-1"></i>إدارة المجلدات
            </a>
            <a href="{{ url_for('finance.add_account') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>إضافة حساب جديد
            </a>
            <button type="button" class="btn btn-outline-primary dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown">
                <span class="visually-hidden">Toggle Dropdown</span>
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="#" onclick="expandAll()">
                    <i class="fas fa-expand-arrows-alt me-2"></i>توسيع الكل
                </a></li>
                <li><a class="dropdown-item" href="#" onclick="collapseAll()">
                    <i class="fas fa-compress-arrows-alt me-2"></i>طي الكل
                </a></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item" href="{{ url_for('finance.index') }}">
                    <i class="fas fa-chart-line me-2"></i>التقارير المالية
                </a></li>
            </ul>
        </div>
        {% endif %}
    </div>

    <!-- فلترة الحسابات -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header bg-light">
                    <h6 class="mb-0">
                        <i class="fas fa-filter me-2"></i>فلترة الحسابات حسب الفترة الزمنية
                    </h6>
                </div>
                <div class="card-body">
                    <form method="GET" action="{{ url_for('finance.accounts') }}" id="filter-form">
                        <div class="row">
                            <div class="col-md-3">
                                <label for="filter_type" class="form-label">نوع الفلترة</label>
                                <select class="form-select" id="filter_type" name="filter_type" onchange="toggleDateInputs()">
                                    <option value="">بدون فلترة</option>
                                    <option value="month" {% if request.args.get('filter_type') == 'month' %}selected{% endif %}>شهر محدد</option>
                                    <option value="range" {% if request.args.get('filter_type') == 'range' %}selected{% endif %}>فترة زمنية</option>
                                    <option value="year" {% if request.args.get('filter_type') == 'year' %}selected{% endif %}>سنة محددة</option>
                                </select>
                            </div>
                            <div class="col-md-3" id="month-input" style="display: none;">
                                <label for="filter_month" class="form-label">الشهر والسنة</label>
                                <input type="month" class="form-control" id="filter_month" name="filter_month"
                                       value="{{ request.args.get('filter_month', '') }}">
                            </div>
                            <div class="col-md-3" id="year-input" style="display: none;">
                                <label for="filter_year" class="form-label">السنة</label>
                                <input type="number" class="form-control" id="filter_year" name="filter_year"
                                       min="2020" max="2030" value="{{ request.args.get('filter_year', '') }}">
                            </div>
                            <div class="col-md-3" id="start-date-input" style="display: none;">
                                <label for="start_date" class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="start_date" name="start_date"
                                       value="{{ request.args.get('start_date', '') }}">
                            </div>
                            <div class="col-md-3" id="end-date-input" style="display: none;">
                                <label for="end_date" class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="end_date" name="end_date"
                                       value="{{ request.args.get('end_date', '') }}">
                            </div>
                            <div class="col-md-3 d-flex align-items-end">
                                <div class="btn-group w-100">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search me-1"></i>تطبيق الفلتر
                                    </button>
                                    <a href="{{ url_for('finance.accounts') }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-times me-1"></i>إزالة الفلتر
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- إشعار الفلترة -->
    {% if filter_applied %}
    <div class="row mb-3">
        <div class="col-md-12">
            <div class="alert alert-info alert-dismissible fade show" role="alert">
                <i class="fas fa-info-circle me-2"></i>
                <strong>تم تطبيق فلتر:</strong>
                {% if request.args.get('filter_type') == 'month' %}
                    عرض البيانات لشهر {{ request.args.get('filter_month') }}
                {% elif request.args.get('filter_type') == 'year' %}
                    عرض البيانات لسنة {{ request.args.get('filter_year') }}
                {% elif request.args.get('filter_type') == 'range' %}
                    عرض البيانات من {{ request.args.get('start_date') }} إلى {{ request.args.get('end_date') }}
                {% endif %}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- شجرة الحسابات -->
    <div class="row">
        <div class="col-md-12">
            <div class="card shadow-lg border-0">
                <div class="card-header bg-gradient-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-tree me-2"></i>شجرة الحسابات
                        </h5>
                        <div class="btn-group btn-group-sm">
                            <button type="button" class="btn btn-outline-light" onclick="expandAll()" title="توسيع الكل">
                                <i class="fas fa-expand-arrows-alt"></i>
                            </button>
                            <button type="button" class="btn btn-outline-light" onclick="collapseAll()" title="طي الكل">
                                <i class="fas fa-compress-arrows-alt"></i>
                            </button>
                            <button type="button" class="btn btn-outline-light" onclick="toggleSearch()" title="البحث">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- شريط البحث -->
                <div class="card-header bg-light border-bottom d-none" id="search-bar">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" class="form-control" id="account-search" placeholder="ابحث في الحسابات..." onkeyup="searchAccounts()">
                        <button class="btn btn-outline-secondary" type="button" onclick="clearSearch()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>

                <div class="card-body p-0">
                    {% if folders or root_accounts %}
                    <div id="accounts-tree" class="accounts-container">
                        <!-- المجلدات -->
                        {% for folder in folders %}
                            {% include 'finance/folder_tree_item.html' %}
                        {% endfor %}

                        <!-- الحسابات بدون مجلد -->
                        {% if root_accounts %}
                        <div class="folder-section">
                            <div class="folder-header" style="background: #f8f9fa; color: #6c757d; padding: 10px; border-left: 4px solid #6c757d;">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="fas fa-folder-open me-2"></i>
                                        <strong>حسابات عامة</strong>
                                        <small class="text-muted ms-2">({{ root_accounts|length }})</small>
                                    </div>
                                    <button class="btn btn-sm btn-outline-secondary toggle-folder" data-target="general-accounts">
                                        <i class="fas fa-minus"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="folder-content" id="general-accounts">
                                {% for account in root_accounts %}
                                    {% include 'finance/account_tree_item.html' %}
                                {% endfor %}
                            </div>
                        </div>
                        {% endif %}
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <div class="mb-4">
                            <i class="fas fa-folder-open fa-4x text-muted"></i>
                        </div>
                        <h4 class="text-muted mb-3">لا توجد حسابات مضافة بعد</h4>
                        <p class="text-muted mb-4">ابدأ بإنشاء أول حساب في شجرة الحسابات المالية</p>
                        {% if current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance') %}
                        <a href="{{ url_for('finance.add_account') }}" class="btn btn-primary btn-lg">
                            <i class="fas fa-plus me-2"></i>إضافة أول حساب
                        </a>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>


{% endblock %}

{% block styles %}
<style>
/* تحسينات عامة */
.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

/* تصميم شجرة الحسابات */
.tree-container {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    padding: 1rem;
    background: linear-gradient(135deg, #f8f9fc 0%, #ffffff 100%);
}

.accounts-container {
    padding: 1rem;
    background: linear-gradient(135deg, #f8f9fc 0%, #ffffff 100%);
}

.account-item {
    margin: 12px 0;
    padding: 20px;
    border: none;
    border-radius: 15px;
    background: #ffffff;
    position: relative;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    cursor: pointer;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    overflow: hidden;
}

.account-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.account-item:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.account-item:hover::before {
    transform: scaleX(1);
}

.account-item.level-0 {
    font-weight: bold;
    padding: 15px;
}

.account-item.level-1 {
    margin-right: 30px;
    padding: 12px;
}

.account-item.level-2 {
    margin-right: 60px;
    padding: 10px;
}

.account-item.level-3 {
    margin-right: 90px;
    padding: 8px;
}

.account-info {
    display: flex;
    justify-content: between;
    align-items: center;
}

.account-details {
    flex-grow: 1;
}

.account-name {
    font-size: 1.1em;
    margin-bottom: 2px;
}

.account-code {
    font-size: 0.9em;
    opacity: 0.8;
}

.account-balance {
    font-weight: bold;
    margin-right: 15px;
}

.account-actions {
    display: flex;
    gap: 5px;
}

.account-actions .btn {
    padding: 2px 8px;
    font-size: 0.8em;
}

.account-item.inactive {
    opacity: 0.6;
}

.account-item.inactive .account-name::after {
    content: " (غير نشط)";
    font-size: 0.8em;
    font-style: italic;
}

.children-container {
    margin-top: 10px;
}

.toggle-children {
    background: none;
    border: none;
    color: inherit;
    font-size: 0.9em;
    padding: 2px 5px;
    margin-left: 5px;
    cursor: pointer;
}

.toggle-children:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}
</style>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle toggle children
    const toggleButtons = document.querySelectorAll('.toggle-children');

    toggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetId = this.getAttribute('data-target');
            const target = document.getElementById(targetId);
            const icon = this.querySelector('i');

            if (target.style.display === 'none') {
                target.style.display = 'block';
                icon.className = 'fas fa-minus';
            } else {
                target.style.display = 'none';
                icon.className = 'fas fa-plus';
            }
        });
    });
});

// وظائف التحكم في شجرة الحسابات
function expandAll() {
    const childrenContainers = document.querySelectorAll('.children-container');
    const toggleButtons = document.querySelectorAll('.toggle-children');

    childrenContainers.forEach(container => {
        container.style.display = 'block';
    });

    toggleButtons.forEach(button => {
        const icon = button.querySelector('i');
        if (icon) {
            icon.className = 'fas fa-minus';
        }
    });
}

function collapseAll() {
    const childrenContainers = document.querySelectorAll('.children-container');
    const toggleButtons = document.querySelectorAll('.toggle-children');

    childrenContainers.forEach(container => {
        container.style.display = 'none';
    });

    toggleButtons.forEach(button => {
        const icon = button.querySelector('i');
        if (icon) {
            icon.className = 'fas fa-plus';
        }
    });
}

function toggleSearch() {
    const searchBar = document.getElementById('search-bar');
    const searchInput = document.getElementById('account-search');

    if (searchBar.classList.contains('d-none')) {
        searchBar.classList.remove('d-none');
        searchInput.focus();
    } else {
        searchBar.classList.add('d-none');
        clearSearch();
    }
}

function searchAccounts() {
    const searchTerm = document.getElementById('account-search').value.toLowerCase();
    const accountItems = document.querySelectorAll('.account-item');

    accountItems.forEach(item => {
        const accountName = item.querySelector('.account-name').textContent.toLowerCase();
        const accountCode = item.querySelector('.account-code');
        const accountCodeText = accountCode ? accountCode.textContent.toLowerCase() : '';

        if (accountName.includes(searchTerm) || accountCodeText.includes(searchTerm)) {
            item.style.display = 'block';
            // إظهار الحساب الأب إذا كان مخفي
            let parent = item.closest('.children-container');
            while (parent) {
                parent.style.display = 'block';
                const parentToggle = parent.previousElementSibling?.querySelector('.toggle-children i');
                if (parentToggle) {
                    parentToggle.className = 'fas fa-minus';
                }
                parent = parent.parentElement.closest('.children-container');
            }
        } else {
            item.style.display = 'none';
        }
    });
}

function clearSearch() {
    document.getElementById('account-search').value = '';
    const accountItems = document.querySelectorAll('.account-item');

    accountItems.forEach(item => {
        item.style.display = 'block';
    });
}

// إدارة مدخلات الفلترة
function toggleDateInputs() {
    const filterType = document.getElementById('filter_type').value;
    const monthInput = document.getElementById('month-input');
    const yearInput = document.getElementById('year-input');
    const startDateInput = document.getElementById('start-date-input');
    const endDateInput = document.getElementById('end-date-input');

    // إخفاء جميع المدخلات
    monthInput.style.display = 'none';
    yearInput.style.display = 'none';
    startDateInput.style.display = 'none';
    endDateInput.style.display = 'none';

    // إظهار المدخلات المناسبة
    switch(filterType) {
        case 'month':
            monthInput.style.display = 'block';
            break;
        case 'year':
            yearInput.style.display = 'block';
            break;
        case 'range':
            startDateInput.style.display = 'block';
            endDateInput.style.display = 'block';
            break;
    }
}

// إدارة توسيع وطي المجلدات
document.addEventListener('DOMContentLoaded', function() {
    toggleDateInputs();

    // إدارة أزرار توسيع/طي المجلدات
    document.querySelectorAll('.toggle-folder').forEach(button => {
        button.addEventListener('click', function() {
            const targetId = this.getAttribute('data-target');
            const targetElement = document.getElementById(targetId);
            const icon = this.querySelector('i');

            if (targetElement.classList.contains('d-none')) {
                targetElement.classList.remove('d-none');
                icon.classList.remove('fa-plus');
                icon.classList.add('fa-minus');
            } else {
                targetElement.classList.add('d-none');
                icon.classList.remove('fa-minus');
                icon.classList.add('fa-plus');
            }
        });
    });
});
</script>
{% endblock %}
