{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
        <a href="{{ url_for('finance.accounts') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i>العودة إلى شجرة الحسابات
        </a>
    </div>

    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>تعديل الحساب: {{ account.name }}
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">اسم الحساب <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name" 
                                           value="{{ account.name }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="code" class="form-label">رمز الحساب</label>
                                    <input type="text" class="form-control" id="code" name="code" 
                                           value="{{ account.code or '' }}" placeholder="مثال: ACC001">
                                    <div class="form-text">رمز فريد للحساب (اختياري)</div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">وصف الحساب</label>
                            <textarea class="form-control" id="description" name="description" rows="3"
                                      placeholder="وصف مختصر للحساب وغرضه">{{ account.description or '' }}</textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="account_type" class="form-label">نوع الحساب</label>
                                    <select class="form-select" id="account_type" name="account_type">
                                        <option value="general" {% if account.account_type == 'general' %}selected{% endif %}>💼 عام</option>
                                        <option value="cash" {% if account.account_type == 'cash' %}selected{% endif %}>💵 نقدي</option>
                                        <option value="bank" {% if account.account_type == 'bank' %}selected{% endif %}>🏦 بنك</option>
                                        <option value="income" {% if account.account_type == 'income' %}selected{% endif %}>📈 إيرادات</option>
                                        <option value="expense" {% if account.account_type == 'expense' %}selected{% endif %}>📉 مصروفات</option>
                                        <option value="pending" {% if account.account_type == 'pending' %}selected{% endif %}>⏳ معلق</option>
                                        <option value="overdue" {% if account.account_type == 'overdue' %}selected{% endif %}>⚠️ متأخر</option>
                                        <option value="investment" {% if account.account_type == 'investment' %}selected{% endif %}>📊 استثمارات</option>
                                        <option value="asset" {% if account.account_type == 'asset' %}selected{% endif %}>🏢 أصول</option>
                                        <option value="liability" {% if account.account_type == 'liability' %}selected{% endif %}>📋 التزامات</option>
                                        <option value="equity" {% if account.account_type == 'equity' %}selected{% endif %}>💎 حقوق الملكية</option>
                                        <option value="receivable" {% if account.account_type == 'receivable' %}selected{% endif %}>📥 مدينون</option>
                                        <option value="payable" {% if account.account_type == 'payable' %}selected{% endif %}>📤 دائنون</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="parent_id" class="form-label">الحساب الأب</label>
                                    <select class="form-select" id="parent_id" name="parent_id">
                                        <option value="">حساب رئيسي</option>
                                        {% for acc in all_accounts %}
                                        <option value="{{ acc.id }}" 
                                                {% if account.parent_id == acc.id %}selected{% endif %}>
                                            {{ acc.full_path }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                    <div class="form-text">اختر الحساب الأب لجعل هذا حساباً فرعياً</div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                       {% if account.is_active %}checked{% endif %}>
                                <label class="form-check-label" for="is_active">
                                    الحساب نشط
                                </label>
                                <div class="form-text">إلغاء تفعيل الحساب يخفيه من القوائم دون حذفه</div>
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <strong>معلومات الحساب:</strong><br>
                            <small>
                                <i class="fas fa-calendar me-1"></i>تاريخ الإنشاء: {{ account.created_at.strftime('%Y-%m-%d %H:%M') }}<br>
                                <i class="fas fa-edit me-1"></i>آخر تحديث: {{ account.updated_at.strftime('%Y-%m-%d %H:%M') }}<br>
                                <i class="fas fa-wallet me-1"></i>الرصيد الحالي: {{ "{:,.2f}".format(account.balance) }} ر.س
                            </small>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="currency_id" class="form-label">العملة</label>
                                    <select class="form-select" id="currency_id" name="currency_id">
                                        {% for currency in currencies %}
                                        <option value="{{ currency.id }}" {% if account.currency_id == currency.id %}selected{% endif %}>
                                            {{ currency.symbol }} - {{ currency.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="account_color" class="form-label">لون خلفية الحساب</label>
                                    <div class="input-group">
                                        <input type="color" class="form-control form-control-color" id="account_color" name="account_color" value="{{ account.account_color or '#007bff' }}">
                                        <input type="text" class="form-control" id="color_text" value="{{ account.account_color or '#007bff' }}" readonly>
                                    </div>
                                    <div class="form-text">اختر لون خلفية الحساب</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="text_color" class="form-label">لون النص</label>
                                    <div class="input-group">
                                        <input type="color" class="form-control form-control-color" id="text_color" name="text_color" value="{{ account.text_color or '#ffffff' }}">
                                        <input type="text" class="form-control" id="text_color_text" value="{{ account.text_color or '#ffffff' }}" readonly>
                                    </div>
                                    <div class="form-text">اختر لون النص للحساب</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="folder_id" class="form-label">المجلد</label>
                                    <select class="form-select" id="folder_id" name="folder_id">
                                        <option value="">بدون مجلد</option>
                                        {% for folder in folders %}
                                        <option value="{{ folder.id }}" {% if account.folder_id == folder.id %}selected{% endif %}>
                                            <i class="{{ folder.icon }}"></i> {{ folder.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                    <div class="form-text">اختر مجلداً لتنظيم الحساب</div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('finance.accounts') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-save me-1"></i>حفظ التغييرات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Color picker synchronization
    const colorPicker = document.getElementById('account_color');
    const colorText = document.getElementById('color_text');
    const textColorPicker = document.getElementById('text_color');
    const textColorText = document.getElementById('text_color_text');

    colorPicker.addEventListener('change', function() {
        colorText.value = this.value;
    });

    textColorPicker.addEventListener('change', function() {
        textColorText.value = this.value;
    });

    // Form validation
    const form = document.querySelector('form');
    const nameInput = document.getElementById('name');
    
    form.addEventListener('submit', function(e) {
        const name = nameInput.value.trim();
        
        if (!name) {
            e.preventDefault();
            alert('اسم الحساب مطلوب');
            nameInput.focus();
            return false;
        }
    });
});
</script>
{% endblock %}
