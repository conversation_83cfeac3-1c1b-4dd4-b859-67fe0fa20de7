#!/usr/bin/env python3
"""
Migration script to create the new finance system
This script creates all the new tables for the enhanced finance system
"""

import sqlite3
import os
from datetime import datetime

def run_migration():
    """Run the migration to create new finance system tables"""
    
    # Get database path
    db_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'app', 'sparkle.db')
    
    if not os.path.exists(db_path):
        print(f"Database not found at {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("Creating new finance system tables...")
        
        # Create accounts table (شجرة الحسابات)
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS accounts (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(255) NOT NULL,
            code VARCHAR(50) UNIQUE,
            description TEXT,
            balance FLOAT DEFAULT 0.0,
            account_type VARCHAR(50) DEFAULT 'general',
            is_active BOOLEAN DEFAULT 1,
            parent_id INTEGER,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (parent_id) REFERENCES accounts (id)
        )
        """)
        
        # Create employee_salaries table (رواتب الموظفين)
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS employee_salaries (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            employee_name VARCHAR(255) NOT NULL,
            employee_manual VARCHAR(255),
            amount FLOAT NOT NULL,
            status VARCHAR(20) DEFAULT 'draft',
            transfer_date DATETIME,
            notes TEXT,
            employee_id INTEGER,
            supervisor_id INTEGER,
            created_by_id INTEGER,
            pending_account_id INTEGER,
            deduction_account_id INTEGER,
            paid_account_id INTEGER,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (employee_id) REFERENCES users (id),
            FOREIGN KEY (supervisor_id) REFERENCES users (id),
            FOREIGN KEY (created_by_id) REFERENCES users (id),
            FOREIGN KEY (pending_account_id) REFERENCES accounts (id),
            FOREIGN KEY (deduction_account_id) REFERENCES accounts (id),
            FOREIGN KEY (paid_account_id) REFERENCES accounts (id)
        )
        """)
        
        # Create salary_projects table (ربط الرواتب بالمشاريع)
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS salary_projects (
            salary_id INTEGER,
            project_id INTEGER,
            PRIMARY KEY (salary_id, project_id),
            FOREIGN KEY (salary_id) REFERENCES employee_salaries (id),
            FOREIGN KEY (project_id) REFERENCES projects (id)
        )
        """)
        
        # Create salary_tasks table (ربط الرواتب بالمهام)
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS salary_tasks (
            salary_id INTEGER,
            task_id INTEGER,
            PRIMARY KEY (salary_id, task_id),
            FOREIGN KEY (salary_id) REFERENCES employee_salaries (id),
            FOREIGN KEY (task_id) REFERENCES tasks (id)
        )
        """)
        
        # Create salary_attachments table (مرفقات الرواتب)
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS salary_attachments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            filename VARCHAR(255) NOT NULL,
            filepath VARCHAR(255) NOT NULL,
            file_type VARCHAR(50),
            file_size INTEGER,
            salary_id INTEGER,
            uploaded_by_id INTEGER,
            uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (salary_id) REFERENCES employee_salaries (id),
            FOREIGN KEY (uploaded_by_id) REFERENCES users (id)
        )
        """)
        
        # Create salary_links table (روابط الرواتب)
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS salary_links (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            url VARCHAR(500) NOT NULL,
            description VARCHAR(255),
            salary_id INTEGER,
            added_by_id INTEGER,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (salary_id) REFERENCES employee_salaries (id),
            FOREIGN KEY (added_by_id) REFERENCES users (id)
        )
        """)
        
        # Create financial_transactions table (المعاملات المالية)
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS financial_transactions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            transaction_number VARCHAR(50) UNIQUE NOT NULL,
            transaction_name VARCHAR(255) NOT NULL,
            status VARCHAR(20) DEFAULT 'draft',
            notes TEXT,
            created_by_id INTEGER,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (created_by_id) REFERENCES users (id)
        )
        """)
        
        # Create financial_transaction_items table (بنود المعاملات المالية)
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS financial_transaction_items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            amount FLOAT NOT NULL,
            action VARCHAR(20) NOT NULL,
            transaction_date DATETIME DEFAULT CURRENT_TIMESTAMP,
            notes TEXT,
            transaction_id INTEGER,
            account_id INTEGER,
            FOREIGN KEY (transaction_id) REFERENCES financial_transactions (id),
            FOREIGN KEY (account_id) REFERENCES accounts (id)
        )
        """)
        
        # Create financial_transaction_attachments table (مرفقات المعاملات المالية)
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS financial_transaction_attachments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            filename VARCHAR(255) NOT NULL,
            filepath VARCHAR(255) NOT NULL,
            file_type VARCHAR(50),
            file_size INTEGER,
            transaction_id INTEGER,
            uploaded_by_id INTEGER,
            uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (transaction_id) REFERENCES financial_transactions (id),
            FOREIGN KEY (uploaded_by_id) REFERENCES users (id)
        )
        """)
        
        # Create financial_transaction_links table (روابط المعاملات المالية)
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS financial_transaction_links (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            url VARCHAR(500) NOT NULL,
            description VARCHAR(255),
            transaction_id INTEGER,
            added_by_id INTEGER,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (transaction_id) REFERENCES financial_transactions (id),
            FOREIGN KEY (added_by_id) REFERENCES users (id)
        )
        """)
        
        # Update invoices table to add new account fields (with error handling)
        try:
            cursor.execute("""
            ALTER TABLE invoices ADD COLUMN pending_account_id INTEGER REFERENCES accounts(id)
            """)
        except sqlite3.OperationalError:
            pass  # Column already exists

        try:
            cursor.execute("""
            ALTER TABLE invoices ADD COLUMN paid_account_id INTEGER REFERENCES accounts(id)
            """)
        except sqlite3.OperationalError:
            pass  # Column already exists

        try:
            cursor.execute("""
            ALTER TABLE invoices ADD COLUMN overdue_account_id INTEGER REFERENCES accounts(id)
            """)
        except sqlite3.OperationalError:
            pass  # Column already exists

        # Update invoice_items table to add supervisor fields (with error handling)
        try:
            cursor.execute("""
            ALTER TABLE invoice_items ADD COLUMN supervisor_name VARCHAR(255)
            """)
        except sqlite3.OperationalError:
            pass  # Column already exists

        try:
            cursor.execute("""
            ALTER TABLE invoice_items ADD COLUMN supervisor_manual VARCHAR(255)
            """)
        except sqlite3.OperationalError:
            pass  # Column already exists
        
        print("Creating default accounts...")
        
        # Create some default accounts
        default_accounts = [
            ('الخزينة الرئيسية', 'MAIN_CASH', 'الحساب الرئيسي للخزينة', 'cash'),
            ('البنك', 'BANK', 'حساب البنك الرئيسي', 'bank'),
            ('المال المعلق', 'PENDING', 'حساب المبالغ المعلقة', 'pending'),
            ('الرواتب المدفوعة', 'PAID_SALARIES', 'حساب الرواتب المدفوعة', 'expense'),
            ('الفواتير المدفوعة', 'PAID_INVOICES', 'حساب الفواتير المدفوعة', 'income'),
            ('المبالغ المتأخرة', 'OVERDUE', 'حساب المبالغ المتأخرة', 'overdue'),
        ]
        
        for name, code, description, account_type in default_accounts:
            cursor.execute("""
            INSERT OR IGNORE INTO accounts (name, code, description, account_type)
            VALUES (?, ?, ?, ?)
            """, (name, code, description, account_type))
        
        conn.commit()
        print("Migration completed successfully!")
        return True
        
    except Exception as e:
        print(f"Error during migration: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

if __name__ == "__main__":
    success = run_migration()
    if success:
        print("New finance system tables created successfully!")
    else:
        print("Migration failed!")
