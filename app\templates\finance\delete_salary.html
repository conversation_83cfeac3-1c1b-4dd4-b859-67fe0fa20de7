{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
        <a href="{{ url_for('finance.salaries') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i>العودة إلى الرواتب
        </a>
    </div>

    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow border-danger">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>تأكيد حذف الراتب
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-danger" role="alert">
                        <h5 class="alert-heading">
                            <i class="fas fa-warning me-2"></i>تحذير!
                        </h5>
                        <p>أنت على وشك حذف راتب الموظف نهائياً. هذا الإجراء لا يمكن التراجع عنه.</p>
                        <hr>
                        <p class="mb-0">سيتم حذف جميع البيانات المرتبطة بهذا الراتب بما في ذلك المرفقات والروابط.</p>
                    </div>

                    <!-- تفاصيل الراتب المراد حذفه -->
                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h6 class="mb-0">تفاصيل الراتب المراد حذفه</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>اسم الموظف:</strong></td>
                                            <td>{{ salary.employee_name }}</td>
                                        </tr>
                                        {% if salary.employee_manual %}
                                        <tr>
                                            <td><strong>تفاصيل إضافية:</strong></td>
                                            <td>{{ salary.employee_manual }}</td>
                                        </tr>
                                        {% endif %}
                                        {% if salary.employee %}
                                        <tr>
                                            <td><strong>الموظف في النظام:</strong></td>
                                            <td>{{ salary.employee.first_name }} {{ salary.employee.last_name }}</td>
                                        </tr>
                                        {% endif %}
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>مبلغ الراتب:</strong></td>
                                            <td><span class="h6 text-success">${{ "{:,.2f}".format(salary.amount) }}</span></td>
                                        </tr>
                                        <tr>
                                            <td><strong>الحالة:</strong></td>
                                            <td>
                                                {% if salary.status == 'draft' %}
                                                <span class="badge bg-secondary">مسودة</span>
                                                {% elif salary.status == 'pending' %}
                                                <span class="badge bg-warning">معلق</span>
                                                {% elif salary.status == 'paid' %}
                                                <span class="badge bg-success">مدفوع</span>
                                                {% elif salary.status == 'cancelled' %}
                                                <span class="badge bg-danger">ملغي</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>تاريخ الإنشاء:</strong></td>
                                            <td>{{ salary.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>

                            {% if salary.notes %}
                            <div class="mt-3">
                                <h6 class="text-muted">الملاحظات:</h6>
                                <div class="alert alert-info">
                                    {{ salary.notes }}
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- البيانات المرتبطة -->
                    {% if salary.projects or salary.tasks or salary.attachments.count() > 0 or salary.links.count() > 0 %}
                    <div class="card mb-4">
                        <div class="card-header bg-warning text-dark">
                            <h6 class="mb-0">
                                <i class="fas fa-link me-2"></i>البيانات المرتبطة التي سيتم حذفها
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                {% if salary.projects %}
                                <div class="col-md-6">
                                    <h6 class="text-muted">المشاريع المرتبطة ({{ salary.projects|length }})</h6>
                                    <ul class="list-group list-group-flush">
                                        {% for project in salary.projects %}
                                        <li class="list-group-item px-0">{{ project.name }}</li>
                                        {% endfor %}
                                    </ul>
                                </div>
                                {% endif %}
                                {% if salary.tasks %}
                                <div class="col-md-6">
                                    <h6 class="text-muted">المهام المرتبطة ({{ salary.tasks|length }})</h6>
                                    <ul class="list-group list-group-flush">
                                        {% for task in salary.tasks %}
                                        <li class="list-group-item px-0">{{ task.title }}</li>
                                        {% endfor %}
                                    </ul>
                                </div>
                                {% endif %}
                            </div>
                            <div class="row mt-3">
                                {% if salary.attachments.count() > 0 %}
                                <div class="col-md-6">
                                    <h6 class="text-muted">المرفقات ({{ salary.attachments.count() }})</h6>
                                    <ul class="list-group list-group-flush">
                                        {% for attachment in salary.attachments %}
                                        <li class="list-group-item px-0">{{ attachment.filename }}</li>
                                        {% endfor %}
                                    </ul>
                                </div>
                                {% endif %}
                                {% if salary.links.count() > 0 %}
                                <div class="col-md-6">
                                    <h6 class="text-muted">الروابط ({{ salary.links.count() }})</h6>
                                    <ul class="list-group list-group-flush">
                                        {% for link in salary.links %}
                                        <li class="list-group-item px-0">
                                            {% if link.description %}
                                            {{ link.description }}
                                            {% else %}
                                            {{ link.url }}
                                            {% endif %}
                                        </li>
                                        {% endfor %}
                                    </ul>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- أزرار التأكيد -->
                    <div class="d-flex justify-content-between align-items-center">
                        <a href="{{ url_for('finance.view_salary', id=salary.id) }}" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i>إلغاء
                        </a>
                        
                        <div>
                            <a href="{{ url_for('finance.salaries') }}" class="btn btn-outline-secondary me-2">
                                <i class="fas fa-arrow-right me-1"></i>العودة للقائمة
                            </a>
                            <form method="POST" style="display: inline;" onsubmit="return confirmDelete()">
                                {{ csrf_token() }}
                                <button type="submit" class="btn btn-danger">
                                    <i class="fas fa-trash me-1"></i>تأكيد الحذف
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function confirmDelete() {
    return confirm('هل أنت متأكد تماماً من حذف هذا الراتب؟ لا يمكن التراجع عن هذا الإجراء.');
}

// Auto focus on cancel button for safety
document.addEventListener('DOMContentLoaded', function() {
    const cancelButton = document.querySelector('a[href*="view_salary"]');
    if (cancelButton) {
        cancelButton.focus();
    }
});
</script>
{% endblock %}
