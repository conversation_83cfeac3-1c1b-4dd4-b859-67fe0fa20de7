from flask import Blueprint, render_template, redirect, url_for, flash, request
from flask_login import login_required, current_user
from datetime import datetime
import os
from werkzeug.utils import secure_filename

from app import db
from app.models.leave import LeaveRequest, LeaveAttachment
from app.models.user import User
from app.utils.notifications import send_notification
from app.utils.activity_logger import log_activity

leave_bp = Blueprint('leave', __name__, url_prefix='/leave')

@leave_bp.route('/')
@login_required
def index():
    # Get query parameters for pagination and search
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 25, type=int)
    search_query = request.args.get('search', '')
    status_filter = request.args.get('status', '')
    date_from = request.args.get('date_from', '')
    date_to = request.args.get('date_to', '')
    employee_id = request.args.get('employee_id', '')

    # Start building the query based on user role
    if current_user.has_role('admin') or current_user.has_role('manager'):
        # Admins and managers see all leave requests
        query = LeaveRequest.query
    else:
        # Regular employees see only their own leave requests
        query = LeaveRequest.query.filter_by(user_id=current_user.id)

    # Apply search filter if provided
    if search_query:
        # Join with User model to search by employee name
        query = query.join(User, LeaveRequest.user_id == User.id).filter(
            (User.first_name.like(f"%{search_query}%")) |
            (User.last_name.like(f"%{search_query}%")) |
            (User.username.like(f"%{search_query}%")) |
            (LeaveRequest.reason.like(f"%{search_query}%"))
        )

    # Apply status filter if provided
    if status_filter and status_filter != 'all':
        query = query.filter(LeaveRequest.status == status_filter)

    # Apply date range filters if provided
    if date_from:
        try:
            from_date = datetime.strptime(date_from, '%Y-%m-%d').date()
            query = query.filter(LeaveRequest.start_date >= from_date)
        except ValueError:
            pass

    if date_to:
        try:
            to_date = datetime.strptime(date_to, '%Y-%m-%d').date()
            query = query.filter(LeaveRequest.end_date <= to_date)
        except ValueError:
            pass

    # Apply employee filter if provided (only for admins and managers)
    if employee_id and (current_user.has_role('admin') or current_user.has_role('manager')):
        query = query.filter(LeaveRequest.user_id == employee_id)

    # Order by created_at (newest first)
    query = query.order_by(LeaveRequest.created_at.desc())

    # Paginate the results
    leave_requests = query.paginate(page=page, per_page=per_page, error_out=False)

    # Get all employees for filter (only for admins and managers)
    employees = []
    if current_user.has_role('admin') or current_user.has_role('manager'):
        employees = User.query.order_by(User.first_name).all()

    return render_template('leave/index.html',
                          title='طلبات الإجازة',
                          leave_requests=leave_requests,
                          employees=employees,
                          search_query=search_query,
                          status_filter=status_filter,
                          date_from=date_from,
                          date_to=date_to,
                          employee_id=employee_id,
                          current_per_page=per_page)

@leave_bp.route('/create', methods=['GET', 'POST'])
@login_required
def create():
    # Get all users (for admin/manager to create leave requests for others)
    users = []
    if current_user.has_role('admin') or current_user.has_role('manager'):
        users = User.query.all()

    if request.method == 'POST':
        start_date = request.form.get('start_date')
        end_date = request.form.get('end_date')
        reason = request.form.get('reason')
        user_id = request.form.get('user_id')

        # Validate dates
        try:
            start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date, '%Y-%m-%d').date()

            if start_date > end_date:
                flash('تاريخ البداية يجب أن يكون قبل تاريخ النهاية', 'danger')
                return redirect(url_for('leave.create'))
        except ValueError:
            flash('تنسيق التاريخ غير صحيح', 'danger')
            return redirect(url_for('leave.create'))

        # Determine the user for the leave request
        if current_user.has_role('admin') or current_user.has_role('manager'):
            # Admin/manager can create leave requests for others
            if user_id and user_id != str(current_user.id):
                target_user_id = int(user_id)
                created_by_id = current_user.id
            else:
                target_user_id = current_user.id
                created_by_id = None
        else:
            # Regular employees can only create leave requests for themselves
            target_user_id = current_user.id
            created_by_id = None

        # Create leave request
        leave_request = LeaveRequest(
            user_id=target_user_id,
            start_date=start_date,
            end_date=end_date,
            reason=reason,
            created_by_id=created_by_id
        )

        db.session.add(leave_request)
        db.session.commit()

        # Log the activity
        log_activity(
            action='create',
            entity_type='leave_request',
            entity_id=leave_request.id,
            description=f'تم إنشاء طلب إجازة جديد من {leave_request.start_date} إلى {leave_request.end_date}'
        )

        # إرسال إشعار للمدراء والإداريين
        from app.models.notification import Notification
        from app.models.user import User

        # الحصول على جميع المدراء والإداريين
        admins_and_managers = User.query.filter(
            (User.roles.any(name='admin')) | (User.roles.any(name='manager'))
        ).all()

        # إنشاء إشعار لكل مدير وإداري
        for admin_manager in admins_and_managers:
            if admin_manager.id != current_user.id:  # لا نرسل إشعار للشخص نفسه
                notification = Notification(
                    title='طلب إجازة جديد',
                    message=f'تم تقديم طلب إجازة جديد من {leave_request.user.get_full_name()} من {leave_request.start_date} إلى {leave_request.end_date}',
                    notification_type='leave_request',
                    user_id=admin_manager.id,
                    related_entity_type='leave_request',
                    related_entity_id=leave_request.id,
                    sender_id=current_user.id
                )
                db.session.add(notification)

        db.session.commit()

        # Process attachments
        if 'attachments' in request.files:
            attachments = request.files.getlist('attachments')

            # Create directory for files if it doesn't exist
            upload_dir = os.path.join('app', 'static', 'uploads', 'leave', str(leave_request.id))
            os.makedirs(upload_dir, exist_ok=True)

            for attachment in attachments:
                if attachment and attachment.filename:
                    filename = secure_filename(attachment.filename)
                    file_path = os.path.join(upload_dir, filename)
                    attachment.save(file_path)

                    # Save file information to database
                    file_attachment = LeaveAttachment(
                        filename=filename,
                        file_path=os.path.join('uploads', 'leave', str(leave_request.id), filename),
                        file_type=attachment.content_type if hasattr(attachment, 'content_type') else None,
                        file_size=len(attachment.read()) if hasattr(attachment, 'read') else 0,
                        leave_request_id=leave_request.id,
                        uploaded_by_id=current_user.id
                    )
                    db.session.add(file_attachment)

            db.session.commit()

        # Send notification to managers/admins about the new leave request
        if not current_user.has_role('admin') and not current_user.has_role('manager'):
            # Get all admins and managers
            admins_managers = User.query.filter(
                User.roles.any(name='admin') | User.roles.any(name='manager')
            ).all()

            for admin_manager in admins_managers:
                send_notification(
                    user_id=admin_manager.id,
                    title='طلب إجازة جديد',
                    message=f'تم تقديم طلب إجازة جديد من قبل {leave_request.user.get_full_name()} من {start_date} إلى {end_date}',
                    notification_type='leave'
                )

        flash('تم إنشاء طلب الإجازة بنجاح', 'success')
        return redirect(url_for('leave.index'))

    return render_template('leave/create.html', title='إنشاء طلب إجازة', users=users)

@leave_bp.route('/view/<int:id>')
@login_required
def view(id):
    leave_request = LeaveRequest.query.get_or_404(id)

    # Check if user has permission to view this leave request
    if not (current_user.has_role('admin') or current_user.has_role('manager') or
            current_user.id == leave_request.user_id or current_user.id == leave_request.created_by_id):
        flash('ليس لديك صلاحية لعرض طلب الإجازة هذا', 'danger')
        return redirect(url_for('leave.index'))

    return render_template('leave/view.html', title='عرض طلب الإجازة', leave_request=leave_request)

@leave_bp.route('/approve/<int:id>', methods=['POST'])
@login_required
def approve(id):
    # Only admin and manager can approve leave requests
    if not (current_user.has_role('admin') or current_user.has_role('manager')):
        flash('ليس لديك صلاحية للموافقة على طلبات الإجازة', 'danger')
        return redirect(url_for('leave.index'))

    leave_request = LeaveRequest.query.get_or_404(id)

    # Cannot approve already approved or rejected requests
    if leave_request.status != 'pending':
        flash('لا يمكن الموافقة على طلب إجازة تمت معالجته بالفعل', 'warning')
        return redirect(url_for('leave.view', id=id))

    # Get approval reason if provided
    approval_reason = request.form.get('approval_reason')

    leave_request.status = 'approved'
    leave_request.reviewed_by_id = current_user.id
    leave_request.updated_at = datetime.utcnow()

    # Save approval reason if provided
    if approval_reason:
        leave_request.approval_reason = approval_reason

    db.session.commit()

    # Log the activity
    log_activity(
        action='update',
        entity_type='leave_request',
        entity_id=leave_request.id,
        description=f'تمت الموافقة على طلب الإجازة من {leave_request.start_date} إلى {leave_request.end_date}'
    )

    # Send notification to the user
    notification_message = f'تمت الموافقة على طلب إجازتك من {leave_request.start_date} إلى {leave_request.end_date}'
    if approval_reason:
        notification_message += f'. سبب الموافقة: {approval_reason}'

    send_notification(
        user_id=leave_request.user_id,
        title='تمت الموافقة على طلب الإجازة',
        message=notification_message,
        notification_type='leave'
    )

    flash('تمت الموافقة على طلب الإجازة بنجاح', 'success')
    return redirect(url_for('leave.view', id=id))

@leave_bp.route('/reject/<int:id>', methods=['POST'])
@login_required
def reject(id):
    # Only admin and manager can reject leave requests
    if not (current_user.has_role('admin') or current_user.has_role('manager')):
        flash('ليس لديك صلاحية لرفض طلبات الإجازة', 'danger')
        return redirect(url_for('leave.index'))

    leave_request = LeaveRequest.query.get_or_404(id)

    # Cannot reject already approved or rejected requests
    if leave_request.status != 'pending':
        flash('لا يمكن رفض طلب إجازة تمت معالجته بالفعل', 'warning')
        return redirect(url_for('leave.view', id=id))

    rejection_reason = request.form.get('rejection_reason')

    leave_request.status = 'rejected'
    leave_request.rejection_reason = rejection_reason
    leave_request.reviewed_by_id = current_user.id
    leave_request.updated_at = datetime.utcnow()

    db.session.commit()

    # Log the activity
    log_activity(
        action='update',
        entity_type='leave_request',
        entity_id=leave_request.id,
        description=f'تم رفض طلب الإجازة من {leave_request.start_date} إلى {leave_request.end_date}'
    )

    # Send notification to the user
    send_notification(
        user_id=leave_request.user_id,
        title='تم رفض طلب الإجازة',
        message=f'تم رفض طلب إجازتك من {leave_request.start_date} إلى {leave_request.end_date}. السبب: {rejection_reason}',
        notification_type='leave'
    )

    flash('تم رفض طلب الإجازة بنجاح', 'success')
    return redirect(url_for('leave.view', id=id))

@leave_bp.route('/delete/<int:id>', methods=['POST'])
@login_required
def delete(id):
    leave_request = LeaveRequest.query.get_or_404(id)

    # Check if user has permission to delete this leave request
    if not (current_user.has_role('admin') or current_user.has_role('manager') or
            (current_user.id == leave_request.user_id and leave_request.status == 'pending')):
        flash('ليس لديك صلاحية لحذف طلب الإجازة هذا', 'danger')
        return redirect(url_for('leave.index'))

    # Delete attachments from filesystem
    for attachment in leave_request.attachments:
        file_path = os.path.join('app', 'static', attachment.file_path)
        if os.path.exists(file_path):
            os.remove(file_path)

    # Store leave request info before deletion for logging
    leave_id = leave_request.id
    leave_start_date = leave_request.start_date
    leave_end_date = leave_request.end_date

    # Delete leave request
    db.session.delete(leave_request)
    db.session.commit()

    # Log the activity
    log_activity(
        action='delete',
        entity_type='leave_request',
        entity_id=leave_id,
        description=f'تم حذف طلب الإجازة من {leave_start_date} إلى {leave_end_date}'
    )

    flash('تم حذف طلب الإجازة بنجاح', 'success')
    return redirect(url_for('leave.index'))

@leave_bp.route('/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def edit(id):
    leave_request = LeaveRequest.query.get_or_404(id)

    # Check if user has permission to edit this leave request
    if not (current_user.has_role('admin') or current_user.has_role('manager') or
            current_user.id == leave_request.user_id):
        flash('ليس لديك صلاحية لتعديل طلب الإجازة هذا', 'danger')
        return redirect(url_for('leave.index'))

    # Regular users can only edit pending requests
    if not current_user.has_role('admin') and leave_request.status != 'pending':
        flash('لا يمكن تعديل طلب إجازة تمت معالجته بالفعل', 'warning')
        return redirect(url_for('leave.view', id=id))

    if request.method == 'POST':
        start_date = request.form.get('start_date')
        end_date = request.form.get('end_date')
        reason = request.form.get('reason')
        status = request.form.get('status')
        rejection_reason = request.form.get('rejection_reason')

        # Validate dates
        try:
            start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date, '%Y-%m-%d').date()

            if start_date > end_date:
                flash('تاريخ البداية يجب أن يكون قبل تاريخ النهاية', 'danger')
                return redirect(url_for('leave.edit', id=id))
        except ValueError:
            flash('تنسيق التاريخ غير صحيح', 'danger')
            return redirect(url_for('leave.edit', id=id))

        # Update leave request
        leave_request.start_date = start_date
        leave_request.end_date = end_date
        leave_request.reason = reason
        leave_request.updated_at = datetime.utcnow()

        # Only admin can change status directly
        if current_user.has_role('admin') and status:
            old_status = leave_request.status
            leave_request.status = status

            # Update rejection reason if provided for rejected status
            if status == 'rejected' and rejection_reason:
                leave_request.rejection_reason = rejection_reason

            # If status changed, set reviewed_by to current admin
            if old_status != status:
                leave_request.reviewed_by_id = current_user.id

                # Send notification to the user about status change
                if old_status != status:
                    if status == 'approved':
                        send_notification(
                            user_id=leave_request.user_id,
                            title='تمت الموافقة على طلب الإجازة',
                            message=f'تمت الموافقة على طلب إجازتك من {leave_request.start_date} إلى {leave_request.end_date}',
                            notification_type='leave'
                        )
                    elif status == 'rejected':
                        send_notification(
                            user_id=leave_request.user_id,
                            title='تم رفض طلب الإجازة',
                            message=f'تم رفض طلب إجازتك من {leave_request.start_date} إلى {leave_request.end_date}. السبب: {leave_request.rejection_reason}',
                            notification_type='leave'
                        )

        # Process attachments
        if 'attachments' in request.files:
            attachments = request.files.getlist('attachments')

            # Create directory for files if it doesn't exist
            upload_dir = os.path.join('app', 'static', 'uploads', 'leave', str(leave_request.id))
            os.makedirs(upload_dir, exist_ok=True)

            for attachment in attachments:
                if attachment and attachment.filename:
                    filename = secure_filename(attachment.filename)
                    file_path = os.path.join(upload_dir, filename)
                    attachment.save(file_path)

                    # Save file information to database
                    file_attachment = LeaveAttachment(
                        filename=filename,
                        file_path=os.path.join('uploads', 'leave', str(leave_request.id), filename),
                        file_type=attachment.content_type if hasattr(attachment, 'content_type') else None,
                        file_size=len(attachment.read()) if hasattr(attachment, 'read') else 0,
                        leave_request_id=leave_request.id,
                        uploaded_by_id=current_user.id
                    )
                    db.session.add(file_attachment)

        db.session.commit()

        # Log the activity
        log_activity(
            action='update',
            entity_type='leave_request',
            entity_id=leave_request.id,
            description=f'تم تحديث طلب الإجازة من {leave_request.start_date} إلى {leave_request.end_date}'
        )

        flash('تم تحديث طلب الإجازة بنجاح', 'success')
        return redirect(url_for('leave.view', id=id))

    return render_template('leave/edit.html', title='تعديل طلب الإجازة', leave_request=leave_request)

@leave_bp.route('/delete_attachment/<int:attachment_id>', methods=['POST'])
@login_required
def delete_attachment(attachment_id):
    attachment = LeaveAttachment.query.get_or_404(attachment_id)
    leave_request_id = attachment.leave_request_id
    leave_request = LeaveRequest.query.get(leave_request_id)

    # Check if user has permission to delete this attachment
    if not (current_user.has_role('admin') or current_user.has_role('manager') or
            current_user.id == leave_request.user_id or current_user.id == attachment.uploaded_by_id):
        flash('ليس لديك صلاحية لحذف هذا المرفق', 'danger')
        return redirect(url_for('leave.view', id=leave_request_id))

    # Regular users can only delete attachments from pending requests
    if not current_user.has_role('admin') and leave_request.status != 'pending':
        flash('لا يمكن تعديل طلب إجازة تمت معالجته بالفعل', 'warning')
        return redirect(url_for('leave.view', id=leave_request_id))

    # Delete file from filesystem
    file_path = os.path.join('app', 'static', attachment.file_path)
    if os.path.exists(file_path):
        os.remove(file_path)

    # Delete from database
    db.session.delete(attachment)
    db.session.commit()

    flash('تم حذف المرفق بنجاح', 'success')
    return redirect(url_for('leave.view', id=leave_request_id))
