{% extends 'base.html' %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>إدارة الموظفين</h1>
    {% if current_user.has_role('admin') or current_user.has_role('manager') %}
    <a href="{{ url_for('employee.create') }}" class="btn btn-primary">
        <i class="fas fa-plus me-1"></i>إضافة موظف جديد
    </a>
    {% endif %}
</div>

<!-- Search and Filter Form -->
<div class="card shadow mb-4">
    <div class="card-header bg-light">
        <h5 class="mb-0">بحث وتصفية</h5>
    </div>
    <div class="card-body">
        <form method="GET" action="{{ url_for('employee.index') }}" class="row g-3">
            <div class="col-md-6">
                <div class="input-group">
                    <input type="text" class="form-control" id="search" name="search" placeholder="ابحث باسم الموظف أو اسم المستخدم أو البريد الإلكتروني" value="{{ search_query }}">
                    <button class="btn btn-primary" type="submit">
                        <i class="fas fa-search"></i> بحث
                    </button>
                    {% if search_query %}
                    <a href="{{ url_for('employee.index') }}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> مسح
                    </a>
                    {% endif %}
                </div>
            </div>
            <div class="col-md-3">
                <select class="form-select" id="department_filter" name="department_filter" onchange="this.form.submit()">
                    <option value="">جميع الأقسام</option>
                    {% for department in departments %}
                    <option value="{{ department.id }}" {% if request.args.get('department_filter') == department.id|string %}selected{% endif %}>
                        {{ department.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <select class="form-select" id="per_page" name="per_page" onchange="this.form.submit()">
                    <option value="25" {% if current_per_page == 25 %}selected{% endif %}>عرض 25 موظف</option>
                    <option value="50" {% if current_per_page == 50 %}selected{% endif %}>عرض 50 موظف</option>
                    <option value="100" {% if current_per_page == 100 %}selected{% endif %}>عرض 100 موظف</option>
                </select>
            </div>
        </form>
    </div>
</div>

<div class="card shadow">
    <div class="card-header bg-light">
        <h5 class="mb-0">قائمة الموظفين</h5>
    </div>
    <div class="card-body">
        {% if employees.items %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>الاسم</th>
                        <th>اسم المستخدم</th>
                        <th>البريد الإلكتروني</th>
                        <th>القسم</th>
                        <th>الصلاحيات</th>
                        <th>حالة الحساب</th>
                        <th>حالة الاتصال</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for employee in employees.items %}
                    <tr class="employee-list-item">
                        <td>{{ employee.get_full_name() }}</td>
                        <td>{{ employee.username }}</td>
                        <td>{{ employee.email }}</td>
                        <td>
                            {% set all_departments = employee.get_all_departments() %}
                            {% if all_departments %}
                                {% for dept in all_departments %}
                                    <span class="badge bg-primary me-1">{{ dept.name }}</span>
                                    {% if dept.id == employee.primary_department_id %}
                                        <small class="text-muted">(رئيسي)</small>
                                    {% endif %}
                                    {% if not loop.last %}<br>{% endif %}
                                {% endfor %}
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>
                            {% for role in employee.roles %}
                            <span class="badge bg-secondary me-1">{{ role.name }}</span>
                            {% endfor %}
                        </td>
                        <td>
                            {% if employee.is_active %}
                            <span class="badge bg-success">نشط</span>
                            {% else %}
                            <span class="badge bg-danger">غير نشط</span>
                            {% endif %}
                        </td>
                        <td>
                            <i class="fas fa-circle me-1 text-{{ employee.get_status_color() }}" style="font-size: 0.7em;"></i>
                            <span class="text-{{ employee.get_status_color() }}">{{ employee.get_status_display() }}</span>
                        </td>
                        <td>
                            <a href="{{ url_for('employee.view', id=employee.id) }}" class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="عرض">
                                <i class="fas fa-eye"></i>
                            </a>
                            {% if current_user.has_role('admin') or current_user.has_role('manager') or
                                current_user.id == employee.id %}
                            <a href="{{ url_for('employee.edit', id=employee.id) }}" class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </a>
                            {% endif %}
                            {% if current_user.has_role('admin') and employee.id != current_user.id %}
                            <a href="{{ url_for('employee.delete', id=employee.id) }}" class="btn btn-sm btn-danger" data-bs-toggle="tooltip" title="حذف">
                                <i class="fas fa-trash"></i>
                            </a>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>

            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center mt-4">
                <div>
                    عرض {{ employees.items|length }} من {{ employees.total }} موظف
                    {% if search_query %}
                    <span class="text-muted">(تصفية بواسطة: {{ search_query }})</span>
                    {% endif %}
                </div>
                <nav aria-label="Page navigation">
                    <ul class="pagination">
                        {% if employees.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('employee.index', page=employees.prev_num, per_page=current_per_page, search=search_query) }}" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item disabled">
                            <a class="page-link" href="#" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        {% endif %}

                        {% set start_page = employees.page - 2 if employees.page > 2 else 1 %}
                        {% set end_page = start_page + 4 if start_page + 4 <= employees.pages else employees.pages %}
                        {% set start_page = end_page - 4 if end_page - 4 >= 1 else 1 %}

                        {% for page_num in range(start_page, end_page + 1) %}
                        <li class="page-item {% if page_num == employees.page %}active{% endif %}">
                            <a class="page-link" href="{{ url_for('employee.index', page=page_num, per_page=current_per_page, search=search_query) }}">{{ page_num }}</a>
                        </li>
                        {% endfor %}

                        {% if employees.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('employee.index', page=employees.next_num, per_page=current_per_page, search=search_query) }}" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item disabled">
                            <a class="page-link" href="#" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
        </div>
        {% else %}
        <div class="alert alert-info">
            {% if search_query %}
            لا توجد نتائج مطابقة لـ "{{ search_query }}".
            <a href="{{ url_for('employee.index') }}" class="alert-link">عرض جميع الموظفين</a>
            {% else %}
            لا يوجد موظفين حاليًا.
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
