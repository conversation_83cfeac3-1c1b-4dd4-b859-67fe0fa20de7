from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify
from flask_login import login_required, current_user
from datetime import datetime
from app import db
from app.models.notification import Notification

notification_bp = Blueprint('notification', __name__, url_prefix='/notifications')

@notification_bp.route('/')
@login_required
def index():
    # Get query parameters for pagination and search
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 25, type=int)
    search_query = request.args.get('search', '')
    notification_type = request.args.get('notification_type', '')
    is_read = request.args.get('is_read', '')
    date_from = request.args.get('date_from', '')
    date_to = request.args.get('date_to', '')

    # Build the query
    query = Notification.query.filter_by(user_id=current_user.id)

    # Apply search filter if provided
    if search_query:
        search_term = f"%{search_query}%"
        query = query.filter(
            (Notification.title.like(search_term)) |
            (Notification.message.like(search_term))
        )

    # Apply notification type filter if provided
    if notification_type:
        query = query.filter(Notification.notification_type == notification_type)

    # Apply read status filter if provided
    if is_read:
        if is_read == 'read':
            query = query.filter(Notification.is_read == True)
        elif is_read == 'unread':
            query = query.filter(Notification.is_read == False)

    # Apply date range filters if provided
    if date_from:
        try:
            from_date = datetime.strptime(date_from, '%Y-%m-%d')
            query = query.filter(Notification.created_at >= from_date)
        except ValueError:
            pass

    if date_to:
        try:
            to_date = datetime.strptime(date_to, '%Y-%m-%d')
            to_date = to_date.replace(hour=23, minute=59, second=59)
            query = query.filter(Notification.created_at <= to_date)
        except ValueError:
            pass

    # Order by created_at (newest first)
    query = query.order_by(Notification.created_at.desc())

    # Paginate the results
    notifications = query.paginate(page=page, per_page=per_page, error_out=False)

    # Get unique notification types for filter
    notification_types = db.session.query(Notification.notification_type).filter(
        Notification.user_id == current_user.id,
        Notification.notification_type != None
    ).distinct().all()
    notification_types = [nt[0] for nt in notification_types if nt[0]]

    return render_template('notification/index.html',
                          title='الإشعارات',
                          notifications=notifications,
                          notification_types=notification_types,
                          search_query=search_query,
                          notification_type=notification_type,
                          is_read=is_read,
                          date_from=date_from,
                          date_to=date_to,
                          current_per_page=per_page)

@notification_bp.route('/unread')
@login_required
def unread():
    notifications = Notification.query.filter_by(user_id=current_user.id, is_read=False).order_by(Notification.created_at.desc()).all()
    return render_template('notification/unread.html', title='Unread Notifications', notifications=notifications)

@notification_bp.route('/mark_read/<int:id>', methods=['POST'])
@login_required
def mark_read(id):
    notification = Notification.query.get_or_404(id)

    # Check if notification belongs to current user
    if notification.user_id != current_user.id:
        flash('You do not have permission to access this notification', 'danger')
        return redirect(url_for('notification.index'))

    notification.mark_as_read()
    db.session.commit()

    # If AJAX request, return JSON response
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return jsonify({'success': True})

    flash('Notification marked as read', 'success')
    return redirect(url_for('notification.index'))

@notification_bp.route('/mark_all_read', methods=['POST'])
@login_required
def mark_all_read():
    unread_notifications = Notification.query.filter_by(user_id=current_user.id, is_read=False).all()

    for notification in unread_notifications:
        notification.mark_as_read()

    db.session.commit()

    # If AJAX request, return JSON response
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return jsonify({'success': True, 'count': len(unread_notifications)})

    flash(f'{len(unread_notifications)} notifications marked as read', 'success')
    return redirect(url_for('notification.index'))

@notification_bp.route('/delete/<int:id>', methods=['POST'])
@login_required
def delete(id):
    notification = Notification.query.get_or_404(id)

    # Check if notification belongs to current user
    if notification.user_id != current_user.id:
        flash('You do not have permission to delete this notification', 'danger')
        return redirect(url_for('notification.index'))

    db.session.delete(notification)
    db.session.commit()

    # If AJAX request, return JSON response
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return jsonify({'success': True})

    flash('Notification deleted', 'success')
    return redirect(url_for('notification.index'))

@notification_bp.route('/delete_all', methods=['POST'])
@login_required
def delete_all():
    notifications = Notification.query.filter_by(user_id=current_user.id).all()

    for notification in notifications:
        db.session.delete(notification)

    db.session.commit()

    # If AJAX request, return JSON response
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return jsonify({'success': True, 'count': len(notifications)})

    flash(f'{len(notifications)} notifications deleted', 'success')
    return redirect(url_for('notification.index'))

@notification_bp.route('/get_unread_count')
@login_required
def get_unread_count():
    count = Notification.query.filter_by(user_id=current_user.id, is_read=False).count()
    return jsonify({'count': count})

@notification_bp.route('/send', methods=['GET', 'POST'])
@login_required
def send():
    # Check if user has permission to send notifications
    if not (current_user.has_role('admin') or current_user.has_role('manager')):
        flash('لا تملك صلاحية إرسال إشعارات', 'danger')
        return redirect(url_for('notification.index'))

    # Get all users (filter inactive users for non-admin/manager)
    from app.models.user import User
    if current_user.has_role('admin') or current_user.has_role('manager'):
        users = User.query.all()
    else:
        users = User.query.filter(User.is_active == True).all()

    # Get all projects
    from app.models.project import Project
    projects_query = Project.query.all()
    projects = [{'id': p.id, 'name': p.name} for p in projects_query]

    # Get all tasks
    from app.models.task import Task
    tasks_query = Task.query.all()
    tasks = [{'id': t.id, 'title': t.title} for t in tasks_query]

    if request.method == 'POST':
        # Get form data
        title = request.form.get('title')
        message = request.form.get('message')
        notification_type = request.form.get('notification_type')
        related_project_id = request.form.get('related_project_id')
        related_task_id = request.form.get('related_task_id')
        link_url = request.form.get('link_url')
        link_text = request.form.get('link_text')
        select_all_users = 'select_all_users' in request.form

        # Get user IDs
        if select_all_users:
            # Send to all users (filter inactive users for non-admin/manager)
            from app.models.user import User
            if current_user.has_role('admin') or current_user.has_role('manager'):
                user_ids = [user.id for user in User.query.all()]
            else:
                user_ids = [user.id for user in User.query.filter(User.is_active == True).all()]
        else:
            # Send to selected users
            user_ids = request.form.getlist('user_ids')

        # Create notifications for each user
        for user_id in user_ids:
            notification = Notification(
                user_id=user_id,
                title=title,
                message=message,
                notification_type=notification_type,
                related_project_id=related_project_id if related_project_id else None,
                related_task_id=related_task_id if related_task_id else None,
                link_url=link_url if link_url else None,
                link_text=link_text if link_text else None
            )
            db.session.add(notification)

        db.session.commit()

        flash(f'تم إرسال الإشعار بنجاح إلى {len(user_ids)} مستخدم', 'success')
        return redirect(url_for('notification.index'))

    return render_template('notification/send.html', title='إرسال إشعار',
                          users=users, projects=projects, tasks=tasks)
