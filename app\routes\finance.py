from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify, send_file
from flask_login import login_required, current_user
from datetime import datetime, timezone, timedelta
import os
from werkzeug.utils import secure_filename

from app import db
from app.models.finance import (
    Transaction, Invoice, InvoiceItem, InvoiceAttachment, TransactionAttachment,
    TransactionVerificationLink, InvoiceVerificationLink, Account, EmployeeSalary,
    FinancialTransaction, FinancialTransactionItem, AccountFolder, SalaryItem
)
from app.models.project import Project
from app.models.task import Task
from app.models.user import User
from app.models.client import Client
from app.models.client import Client
from app.models.project import Project
from app.models.department import Department
from app.models.user import User
from app.models.currency import Currency
finance_bp = Blueprint('finance', __name__, url_prefix='/finance')

@finance_bp.route('/')
@login_required
def index():
    # Check if user has permission to view finance
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance')):
        flash('لا تملك صلاحية الوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard.index'))

    # إحصائيات النظام المالي الجديد
    # عدد الحسابات
    total_accounts = Account.query.count()
    active_accounts = Account.query.filter_by(is_active=True).count()

    # عدد الرواتب
    total_salaries = EmployeeSalary.query.count()
    pending_salaries = EmployeeSalary.query.filter_by(status='pending').count()
    paid_salaries = EmployeeSalary.query.filter_by(status='paid').count()

    # عدد المعاملات المالية
    total_financial_transactions = FinancialTransaction.query.count()
    pending_financial_transactions = FinancialTransaction.query.filter_by(status='pending').count()

    # عدد الفواتير
    total_invoices = Invoice.query.count()
    pending_invoices = Invoice.query.filter_by(status='unpaid').count()
    paid_invoices = Invoice.query.filter_by(status='paid').count()
    overdue_invoices = Invoice.query.filter_by(status='overdue').count()

    # أحدث الأنشطة
    recent_salaries = EmployeeSalary.query.order_by(EmployeeSalary.created_at.desc()).limit(5).all()
    recent_financial_transactions = FinancialTransaction.query.order_by(FinancialTransaction.created_at.desc()).limit(5).all()
    recent_invoices = Invoice.query.order_by(Invoice.created_at.desc()).limit(5).all()

    return render_template('finance/index.html', title='النظام المالي المتقدم',
                          # إحصائيات الحسابات
                          total_accounts=total_accounts,
                          active_accounts=active_accounts,
                          # إحصائيات الرواتب
                          total_salaries=total_salaries,
                          pending_salaries=pending_salaries,
                          paid_salaries=paid_salaries,
                          # إحصائيات المعاملات المالية
                          total_financial_transactions=total_financial_transactions,
                          pending_financial_transactions=pending_financial_transactions,
                          # إحصائيات الفواتير
                          total_invoices=total_invoices,
                          pending_invoices=pending_invoices,
                          paid_invoices=paid_invoices,
                          overdue_invoices=overdue_invoices,
                          # الأنشطة الحديثة
                          recent_salaries=recent_salaries,
                          recent_financial_transactions=recent_financial_transactions,
                          recent_invoices=recent_invoices)

@finance_bp.route('/transactions')
@login_required
def transactions():
    # Check if user has permission to view transactions
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance')):
        flash('لا تملك صلاحية الوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard.index'))

    # Get query parameters for pagination and search
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 25, type=int)
    search_query = request.args.get('search', '')
    transaction_type = request.args.get('type', '')
    category = request.args.get('category', '')
    date_from = request.args.get('date_from', '')
    date_to = request.args.get('date_to', '')
    project_id = request.args.get('project_id', '')

    # Build the query
    query = Transaction.query

    # Apply search filter if provided
    if search_query:
        search_term = f"%{search_query}%"
        query = query.filter(
            (Transaction.description.like(search_term)) |
            (Transaction.category.like(search_term))
        )

    # Apply transaction type filter if provided
    if transaction_type and transaction_type != 'all':
        query = query.filter(Transaction.transaction_type == transaction_type)

    # Apply category filter if provided
    if category and category != 'all':
        query = query.filter(Transaction.category == category)

    # Apply date range filters if provided
    if date_from:
        try:
            from_date = datetime.strptime(date_from, '%Y-%m-%d')
            query = query.filter(Transaction.date >= from_date)
        except ValueError:
            pass

    if date_to:
        try:
            to_date = datetime.strptime(date_to, '%Y-%m-%d')
            to_date = to_date.replace(hour=23, minute=59, second=59)
            query = query.filter(Transaction.date <= to_date)
        except ValueError:
            pass

    # Apply project filter if provided
    if project_id:
        query = query.filter(Transaction.project_id == project_id)

    # Order by date (newest first)
    query = query.order_by(Transaction.date.desc())

    # Paginate the results
    transactions = query.paginate(page=page, per_page=per_page, error_out=False)

    # Get projects for filter and form
    projects = Project.query.all()

    # Get unique categories for filter
    categories = db.session.query(Transaction.category).distinct().all()
    categories = [c[0] for c in categories if c[0]]

    return render_template('finance/transactions.html',
                          title='Transactions',
                          transactions=transactions,
                          projects=projects,
                          categories=categories,
                          search_query=search_query,
                          transaction_type=transaction_type,
                          category=category,
                          date_from=date_from,
                          date_to=date_to,
                          project_id=project_id,
                          current_per_page=per_page)

@finance_bp.route('/add_transaction', methods=['GET', 'POST'])
@login_required
def add_transaction():
    # Check if user has permission to add transactions
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance')):
        flash('لا تملك صلاحية إضافة معاملات مالية', 'danger')
        return redirect(url_for('finance.transactions'))

    # Get projects for the form
    projects = Project.query.all()

    # Get users for supervisor selection
    users = User.query.all()

    # Get currencies for the form
    currencies = Currency.query.all()

    # Get today's date for default issue date
    today = datetime.now().strftime('%Y-%m-%d')

    if request.method == 'POST':
        amount = float(request.form.get('amount'))
        transaction_type = request.form.get('type')  # Changed from transaction_type to type
        category = request.form.get('category')
        description = request.form.get('description')
        date_str = request.form.get('date')
        project_id = request.form.get('project_id')
        notes = request.form.get('notes')

        # Get new fields
        supervisor_id = request.form.get('supervisor_id')
        recipient = request.form.get('recipient')

        # Get alternative currency fields
        alt_amount = request.form.get('alt_amount')
        currency_id = request.form.get('currency_id')

        # Parse date
        date = datetime.strptime(date_str, '%Y-%m-%d') if date_str else datetime.now(timezone.utc)

        # Create new transaction
        transaction = Transaction(
            amount=amount,
            transaction_type=transaction_type,
            category=category,
            description=description,
            date=date,
            recorded_by_id=current_user.id,
            supervisor_id=supervisor_id if supervisor_id else None,
            recipient=recipient,
            alt_amount=float(alt_amount) if alt_amount else None,
            currency_id=currency_id if currency_id else None
        )

        # Set project if provided
        if project_id:
            transaction.project_id = project_id

        db.session.add(transaction)
        db.session.commit()

        # Process attachments
        if 'attachments' in request.files:
            attachments = request.files.getlist('attachments')

            # Create directory for files if it doesn't exist
            upload_dir = os.path.join('app', 'static', 'uploads', 'transactions', str(transaction.id))
            os.makedirs(upload_dir, exist_ok=True)

            for attachment in attachments:
                if attachment and attachment.filename:
                    filename = secure_filename(attachment.filename)
                    file_path = os.path.join(upload_dir, filename)
                    attachment.save(file_path)

                    # Obtener el tamaño del archivo
                    file_size = os.path.getsize(file_path)

                    # Save file information to database
                    file_attachment = TransactionAttachment(
                        filename=filename,
                        file_path=os.path.join('uploads', 'transactions', str(transaction.id), filename),
                        file_type=attachment.content_type if hasattr(attachment, 'content_type') else None,
                        file_size=file_size,
                        transaction_id=transaction.id,
                        uploaded_by_id=current_user.id
                    )
                    db.session.add(file_attachment)

        # Process verification links
        if 'verification_links[]' in request.form:
            links = request.form.getlist('verification_links[]')
            descriptions = request.form.getlist('verification_descriptions[]')

            for i in range(len(links)):
                if links[i].strip():  # Only add if URL is not empty
                    link = TransactionVerificationLink(
                        url=links[i],
                        description=descriptions[i] if i < len(descriptions) else None,
                        transaction_id=transaction.id,
                        added_by_id=current_user.id
                    )
                    db.session.add(link)

        db.session.commit()

        flash('تمت إضافة المعاملة بنجاح', 'success')
        return redirect(url_for('finance.transactions'))

    return render_template('finance/add_transaction.html', title='إضافة معاملة جديدة',
                          projects=projects, users=users, currencies=currencies, today=today)

@finance_bp.route('/edit_transaction/<int:id>', methods=['GET', 'POST'])
@login_required
def edit_transaction(id):
    # Check if user has permission to edit transactions
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance')):
        flash('لا تملك صلاحية تعديل المعاملات المالية', 'danger')
        return redirect(url_for('finance.transactions'))

    transaction = Transaction.query.get_or_404(id)

    # Get projects for the form
    projects = Project.query.all()

    # Get users for supervisor selection
    users = User.query.all()

    # Get currencies for the form
    currencies = Currency.query.all()

    if request.method == 'POST':
        transaction.amount = float(request.form.get('amount'))
        transaction.transaction_type = request.form.get('type')  # Changed from transaction_type to type
        transaction.category = request.form.get('category')
        transaction.description = request.form.get('description')
        project_id = request.form.get('project_id')
        notes = request.form.get('notes')

        # Get new fields
        supervisor_id = request.form.get('supervisor_id')
        recipient = request.form.get('recipient')

        # Get alternative currency fields
        alt_amount = request.form.get('alt_amount')
        currency_id = request.form.get('currency_id')

        # Parse date
        date_str = request.form.get('date')
        if date_str:
            transaction.date = datetime.strptime(date_str, '%Y-%m-%d')

        # Set project if provided
        if project_id:
            transaction.project_id = project_id
        else:
            transaction.project_id = None

        # Set notes if provided
        if notes:
            transaction.notes = notes

        # Set new fields
        transaction.supervisor_id = supervisor_id if supervisor_id else None
        transaction.recipient = recipient
        transaction.alt_amount = float(alt_amount) if alt_amount else None
        transaction.currency_id = currency_id if currency_id else None

        db.session.commit()

        # Process attachments
        if 'attachments' in request.files:
            attachments = request.files.getlist('attachments')

            # Create directory for files if it doesn't exist
            upload_dir = os.path.join('app', 'static', 'uploads', 'transactions', str(transaction.id))
            os.makedirs(upload_dir, exist_ok=True)

            for attachment in attachments:
                if attachment and attachment.filename:
                    filename = secure_filename(attachment.filename)
                    file_path = os.path.join(upload_dir, filename)
                    attachment.save(file_path)

                    # Obtener el tamaño del archivo
                    file_size = os.path.getsize(file_path)

                    # Save file information to database
                    file_attachment = TransactionAttachment(
                        filename=filename,
                        file_path=os.path.join('uploads', 'transactions', str(transaction.id), filename),
                        file_type=attachment.content_type if hasattr(attachment, 'content_type') else None,
                        file_size=file_size,
                        transaction_id=transaction.id,
                        uploaded_by_id=current_user.id
                    )
                    db.session.add(file_attachment)

        # Process verification links
        if 'verification_links[]' in request.form:
            # First, get existing link IDs to determine which ones to keep
            existing_link_ids = set()
            if 'verification_link_ids[]' in request.form:
                existing_link_ids = set(map(int, request.form.getlist('verification_link_ids[]')))

            # Delete links that are not in the form
            for link in transaction.verification_links.all():
                if link.id not in existing_link_ids:
                    db.session.delete(link)

            # Update or add links
            links = request.form.getlist('verification_links[]')
            descriptions = request.form.getlist('verification_descriptions[]')
            link_ids = request.form.getlist('verification_link_ids[]') if 'verification_link_ids[]' in request.form else []

            for i in range(len(links)):
                if links[i].strip():  # Only process if URL is not empty
                    if i < len(link_ids) and link_ids[i]:
                        # Update existing link
                        link_id = int(link_ids[i])
                        link = TransactionVerificationLink.query.get(link_id)
                        if link and link.transaction_id == transaction.id:
                            link.url = links[i]
                            link.description = descriptions[i] if i < len(descriptions) else None
                    else:
                        # Add new link
                        link = TransactionVerificationLink(
                            url=links[i],
                            description=descriptions[i] if i < len(descriptions) else None,
                            transaction_id=transaction.id,
                            added_by_id=current_user.id
                        )
                        db.session.add(link)

        db.session.commit()

        flash('تم تحديث المعاملة بنجاح', 'success')
        return redirect(url_for('finance.transactions'))

    return render_template('finance/edit_transaction.html', title='تعديل المعاملة',
                          transaction=transaction, projects=projects, users=users, currencies=currencies)

@finance_bp.route('/delete_transaction/<int:id>', methods=['GET', 'POST'])
@login_required
def delete_transaction(id):
    # Only admin can delete transactions
    if not current_user.has_role('admin'):
        flash('لا تملك صلاحية حذف المعاملات المالية', 'danger')
        return redirect(url_for('finance.transactions'))

    transaction = Transaction.query.get_or_404(id)

    if request.method == 'POST':
        # Delete attachments from filesystem
        for attachment in transaction.attachments:
            file_path = os.path.join('app', 'static', attachment.file_path)
            try:
                if os.path.exists(file_path) and os.path.isfile(file_path):
                    os.remove(file_path)
            except Exception as e:
                print(f"Error deleting file {file_path}: {str(e)}")
                # Continue with deletion even if file removal fails

        db.session.delete(transaction)
        db.session.commit()

        flash('تم حذف المعاملة بنجاح', 'success')
        return redirect(url_for('finance.transactions'))

    return render_template('finance/delete_transaction.html', title='حذف المعاملة', transaction=transaction)

@finance_bp.route('/invoices')
@login_required
def invoices():
    # Check if user has permission to view invoices
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance')):
        flash('لا تملك صلاحية الوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard.index'))

    # Get query parameters for pagination and search
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 25, type=int)
    search_query = request.args.get('search', '')
    status_filter = request.args.get('status', '')
    date_from = request.args.get('date_from', '')
    date_to = request.args.get('date_to', '')
    client_id = request.args.get('client_id', '')
    project_id = request.args.get('project_id', '')

    # Build the query
    query = Invoice.query

    # Apply search filter if provided
    if search_query:
        search_term = f"%{search_query}%"
        query = query.filter(
            (Invoice.invoice_number.like(search_term)) |
            (Invoice.notes.like(search_term))
        )

    # Apply status filter if provided
    if status_filter and status_filter != 'all':
        query = query.filter(Invoice.status == status_filter)

    # Apply date range filters if provided
    if date_from:
        try:
            from_date = datetime.strptime(date_from, '%Y-%m-%d')
            query = query.filter(Invoice.issue_date >= from_date)
        except ValueError:
            pass

    if date_to:
        try:
            to_date = datetime.strptime(date_to, '%Y-%m-%d')
            to_date = to_date.replace(hour=23, minute=59, second=59)
            query = query.filter(Invoice.issue_date <= to_date)
        except ValueError:
            pass

    # Apply client filter if provided
    if client_id:
        query = query.filter(Invoice.client_id == client_id)

    # Apply project filter if provided
    if project_id:
        query = query.filter(Invoice.project_id == project_id)

    # Order by issue date (newest first)
    query = query.order_by(Invoice.issue_date.desc())

    # Paginate the results
    invoices = query.paginate(page=page, per_page=per_page, error_out=False)

    # Get clients and projects for filters and modal
    clients = Client.query.all()
    projects = Project.query.all()

    return render_template('finance/invoices.html',
                          title='Invoices',
                          invoices=invoices,
                          clients=clients,
                          projects=projects,
                          search_query=search_query,
                          status_filter=status_filter,
                          date_from=date_from,
                          date_to=date_to,
                          client_id=client_id,
                          project_id=project_id,
                          current_per_page=per_page)

@finance_bp.route('/add_invoice', methods=['GET', 'POST'])
@login_required
def add_invoice():
    # Check if user has permission to create invoices
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance')):
        flash('لا تملك صلاحية إنشاء فواتير', 'danger')
        return redirect(url_for('finance.invoices'))

    # Get clients, projects, employees and accounts for the form
    clients = Client.query.all()
    projects = Project.query.all()
    employees = User.query.all()
    accounts = Account.query.filter_by(is_active=True).order_by(Account.name).all()

    # For GET request, show the create invoice form
    if request.method == 'GET':
        return render_template('finance/create_invoice.html', title='إنشاء فاتورة جديدة',
                              clients=clients, projects=projects, employees=employees,
                              accounts=accounts, now=datetime.now(timezone.utc))

    if request.method == 'POST':
        invoice_number = request.form.get('invoice_number')
        client_id = request.form.get('client_id')
        project_id = request.form.get('project_id')

        # Fechas
        issue_date_str = request.form.get('issue_date')
        due_date_str = request.form.get('due_date')
        approval_date_str = request.form.get('approval_date')

        # Comisiones, impuestos y descuentos
        transfer_fee_type = request.form.get('transfer_fee_type', 'percentage')
        transfer_fee_value = float(request.form.get('transfer_fee_value', 0))
        tax_type = request.form.get('tax_type', 'percentage')
        tax_value = float(request.form.get('tax_value', 0))
        discount_type = request.form.get('discount_type', 'percentage')
        discount_value = float(request.form.get('discount_value', 0))

        status = request.form.get('status')
        notes = request.form.get('notes')

        # Account settings
        pending_account_id = request.form.get('pending_account_id')
        paid_account_id = request.form.get('paid_account_id')
        overdue_account_id = request.form.get('overdue_account_id')

        # تحويل القيم الفارغة إلى None
        pending_account_id = int(pending_account_id) if pending_account_id and pending_account_id != '' else None
        paid_account_id = int(paid_account_id) if paid_account_id and paid_account_id != '' else None
        overdue_account_id = int(overdue_account_id) if overdue_account_id and overdue_account_id != '' else None

        # Parse dates
        issue_date = datetime.strptime(issue_date_str, '%Y-%m-%d') if issue_date_str else datetime.now(timezone.utc)
        due_date = datetime.strptime(due_date_str, '%Y-%m-%d') if due_date_str else None
        approval_date = datetime.strptime(approval_date_str, '%Y-%m-%d') if approval_date_str else None

        # Create new invoice
        invoice = Invoice(
            invoice_number=invoice_number,
            issue_date=issue_date,
            due_date=due_date,
            approval_date=approval_date,
            transfer_fee_type=transfer_fee_type,
            transfer_fee_value=transfer_fee_value,
            tax_type=tax_type,
            tax_value=tax_value,
            discount_type=discount_type,
            discount_value=discount_value,
            status=status,
            notes=notes,
            client_id=client_id,
            project_id=project_id if project_id else None,
            pending_account_id=pending_account_id,
            paid_account_id=paid_account_id,
            overdue_account_id=overdue_account_id,
            created_by_id=current_user.id
        )

        db.session.add(invoice)
        db.session.commit()

        # Procesar elementos de línea de factura
        item_descriptions = request.form.getlist('item_description[]')
        item_quantities = request.form.getlist('item_quantity[]')
        item_unit_prices = request.form.getlist('item_unit_price[]')
        item_supervisor_names = request.form.getlist('item_supervisor_name[]')
        item_supervisor_manuals = request.form.getlist('item_supervisor_manual[]')

        for i in range(len(item_descriptions)):
            if item_descriptions[i].strip():  # Solo agregar si hay descripción
                item = InvoiceItem(
                    description=item_descriptions[i],
                    quantity=float(item_quantities[i]) if item_quantities[i] else 1,
                    unit_price=float(item_unit_prices[i]) if item_unit_prices[i] else 0,
                    supervisor_name=item_supervisor_names[i] if i < len(item_supervisor_names) else None,
                    supervisor_manual=item_supervisor_manuals[i] if i < len(item_supervisor_manuals) else None,
                    invoice_id=invoice.id
                )
                db.session.add(item)

        # Procesar archivos adjuntos
        if 'attachments' in request.files:
            attachments = request.files.getlist('attachments')

            # Crear directorio para archivos si no existe
            upload_dir = os.path.join('app', 'static', 'uploads', 'invoices', str(invoice.id))
            os.makedirs(upload_dir, exist_ok=True)

            for attachment in attachments:
                if attachment and attachment.filename:
                    filename = secure_filename(attachment.filename)
                    file_path = os.path.join(upload_dir, filename)
                    attachment.save(file_path)

                    # Obtener el tamaño del archivo
                    file_size = os.path.getsize(file_path)

                    # Guardar información del archivo en la base de datos
                    file_attachment = InvoiceAttachment(
                        filename=filename,
                        file_path=os.path.join('uploads', 'invoices', str(invoice.id), filename),
                        file_type=attachment.content_type if hasattr(attachment, 'content_type') else None,
                        file_size=file_size,
                        invoice_id=invoice.id,
                        uploaded_by_id=current_user.id
                    )
                    db.session.add(file_attachment)

        # Process verification links
        if 'verification_links[]' in request.form:
            links = request.form.getlist('verification_links[]')
            descriptions = request.form.getlist('verification_descriptions[]')

            for i in range(len(links)):
                if links[i].strip():  # Only add if URL is not empty
                    link = InvoiceVerificationLink(
                        url=links[i],
                        description=descriptions[i] if i < len(descriptions) else None,
                        invoice_id=invoice.id,
                        added_by_id=current_user.id
                    )
                    db.session.add(link)

        # Actualizar totales
        invoice.update_total()

        # تحديث أرصدة الحسابات بناءً على حالة الفاتورة
        invoice.update_account_balances()

        db.session.commit()

        flash('تم إنشاء الفاتورة بنجاح', 'success')
        return redirect(url_for('finance.view_invoice', id=invoice.id))

    return redirect(url_for('finance.invoices'))

@finance_bp.route('/view_invoice/<int:id>')
@login_required
def view_invoice(id):
    # Check if user has permission to view invoices
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance')):
        flash('لا تملك صلاحية الوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard.index'))

    invoice = Invoice.query.get_or_404(id)
    clients = Client.query.all()
    projects = Project.query.all()

    # Calcular totales
    subtotal = invoice.calculate_subtotal()
    transfer_fee = invoice.calculate_transfer_fee()
    tax = invoice.calculate_tax()
    discount = invoice.calculate_discount()
    total = invoice.calculate_total()

    return render_template('finance/view_invoice.html', title=f'Invoice: {invoice.invoice_number}',
                          invoice=invoice, clients=clients, projects=projects,
                          subtotal=subtotal, transfer_fee=transfer_fee, tax=tax,
                          discount=discount, total=total)

@finance_bp.route('/edit_invoice/<int:id>', methods=['GET', 'POST'])
@login_required
def edit_invoice(id):
    # Check if user has permission to edit invoices
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance')):
        flash('لا تملك صلاحية تعديل الفواتير', 'danger')
        return redirect(url_for('finance.invoices'))

    invoice = Invoice.query.get_or_404(id)

    # Only admin can edit paid invoices
    if invoice.status == 'paid' and not current_user.has_role('admin'):
        flash('لا يمكن تعديل الفواتير المدفوعة إلا بواسطة المسؤول', 'warning')
        return redirect(url_for('finance.invoices'))

    # Get clients, projects, employees and accounts for the form
    clients = Client.query.all()
    projects = Project.query.all()
    employees = User.query.all()
    accounts = Account.query.filter_by(is_active=True).order_by(Account.name).all()

    if request.method == 'POST':
        invoice_number = request.form.get('invoice_number')
        client_id = request.form.get('client_id')
        project_id = request.form.get('project_id')

        # Fechas
        issue_date_str = request.form.get('issue_date')
        due_date_str = request.form.get('due_date')
        approval_date_str = request.form.get('approval_date')

        # Comisiones, impuestos y descuentos
        transfer_fee_type = request.form.get('transfer_fee_type', 'percentage')
        transfer_fee_value = float(request.form.get('transfer_fee_value', 0))
        tax_type = request.form.get('tax_type', 'percentage')
        tax_value = float(request.form.get('tax_value', 0))
        discount_type = request.form.get('discount_type', 'percentage')
        discount_value = float(request.form.get('discount_value', 0))

        status = request.form.get('status')
        notes = request.form.get('notes')

        # Account settings
        pending_account_id = request.form.get('pending_account_id')
        paid_account_id = request.form.get('paid_account_id')
        overdue_account_id = request.form.get('overdue_account_id')

        # تحويل القيم الفارغة إلى None
        print(f"DEBUG EDIT - Raw values: pending={pending_account_id}, paid={paid_account_id}, overdue={overdue_account_id}")
        pending_account_id = int(pending_account_id) if pending_account_id and pending_account_id != '' else None
        paid_account_id = int(paid_account_id) if paid_account_id and paid_account_id != '' else None
        overdue_account_id = int(overdue_account_id) if overdue_account_id and overdue_account_id != '' else None
        print(f"DEBUG EDIT - Converted values: pending={pending_account_id}, paid={paid_account_id}, overdue={overdue_account_id}")

        # حفظ الحالة القديمة لتحديث الحسابات
        old_status = invoice.status
        old_pending_account_id = invoice.pending_account_id
        old_paid_account_id = invoice.paid_account_id
        old_overdue_account_id = invoice.overdue_account_id
        old_total = invoice.total_amount

        # Update invoice
        invoice.invoice_number = invoice_number
        invoice.client_id = client_id
        invoice.project_id = project_id if project_id else None
        invoice.transfer_fee_type = transfer_fee_type
        invoice.transfer_fee_value = transfer_fee_value
        invoice.tax_type = tax_type
        invoice.tax_value = tax_value
        invoice.discount_type = discount_type
        invoice.discount_value = discount_value
        invoice.status = status
        invoice.notes = notes
        invoice.pending_account_id = pending_account_id
        invoice.paid_account_id = paid_account_id
        invoice.overdue_account_id = overdue_account_id

        print(f"DEBUG EDIT - After assignment: pending={invoice.pending_account_id}, paid={invoice.paid_account_id}, overdue={invoice.overdue_account_id}")

        # Parse dates
        if issue_date_str:
            invoice.issue_date = datetime.strptime(issue_date_str, '%Y-%m-%d')

        if due_date_str:
            invoice.due_date = datetime.strptime(due_date_str, '%Y-%m-%d')
        else:
            invoice.due_date = None

        if approval_date_str:
            invoice.approval_date = datetime.strptime(approval_date_str, '%Y-%m-%d')
        else:
            invoice.approval_date = None

        # Procesar elementos de línea de factura
        # Primero, eliminar los elementos existentes
        for item in invoice.items.all():
            db.session.delete(item)

        # Luego, agregar los nuevos elementos
        item_descriptions = request.form.getlist('item_description[]')
        item_quantities = request.form.getlist('item_quantity[]')
        item_unit_prices = request.form.getlist('item_unit_price[]')
        item_supervisor_names = request.form.getlist('item_supervisor_name[]')
        item_supervisor_manuals = request.form.getlist('item_supervisor_manual[]')

        for i in range(len(item_descriptions)):
            if item_descriptions[i].strip():  # Solo agregar si hay descripción
                item = InvoiceItem(
                    description=item_descriptions[i],
                    quantity=float(item_quantities[i]) if item_quantities[i] else 1,
                    unit_price=float(item_unit_prices[i]) if item_unit_prices[i] else 0,
                    supervisor_name=item_supervisor_names[i] if i < len(item_supervisor_names) else None,
                    supervisor_manual=item_supervisor_manuals[i] if i < len(item_supervisor_manuals) else None,
                    invoice_id=invoice.id
                )
                db.session.add(item)

        # Procesar archivos adjuntos
        if 'attachments' in request.files:
            attachments = request.files.getlist('attachments')

            # Crear directorio para archivos si no existe
            upload_dir = os.path.join('app', 'static', 'uploads', 'invoices', str(invoice.id))
            os.makedirs(upload_dir, exist_ok=True)

            for attachment in attachments:
                if attachment and attachment.filename:
                    filename = secure_filename(attachment.filename)
                    file_path = os.path.join(upload_dir, filename)
                    attachment.save(file_path)

                    # Obtener el tamaño del archivo
                    file_size = os.path.getsize(file_path)

                    # Guardar información del archivo en la base de datos
                    file_attachment = InvoiceAttachment(
                        filename=filename,
                        file_path=os.path.join('uploads', 'invoices', str(invoice.id), filename),
                        file_type=attachment.content_type if hasattr(attachment, 'content_type') else None,
                        file_size=file_size,
                        invoice_id=invoice.id,
                        uploaded_by_id=current_user.id
                    )
                    db.session.add(file_attachment)

        # Process verification links
        if 'verification_links[]' in request.form:
            # First, get existing link IDs to determine which ones to keep
            existing_link_ids = set()
            if 'verification_link_ids[]' in request.form:
                existing_link_ids = set(map(int, request.form.getlist('verification_link_ids[]')))

            # Delete links that are not in the form
            for link in invoice.verification_links.all():
                if link.id not in existing_link_ids:
                    db.session.delete(link)

            # Update or add links
            links = request.form.getlist('verification_links[]')
            descriptions = request.form.getlist('verification_descriptions[]')
            link_ids = request.form.getlist('verification_link_ids[]') if 'verification_link_ids[]' in request.form else []

            for i in range(len(links)):
                if links[i].strip():  # Only process if URL is not empty
                    if i < len(link_ids) and link_ids[i]:
                        # Update existing link
                        link_id = int(link_ids[i])
                        link = InvoiceVerificationLink.query.get(link_id)
                        if link and link.invoice_id == invoice.id:
                            link.url = links[i]
                            link.description = descriptions[i] if i < len(descriptions) else None
                    else:
                        # Add new link
                        link = InvoiceVerificationLink(
                            url=links[i],
                            description=descriptions[i] if i < len(descriptions) else None,
                            invoice_id=invoice.id,
                            added_by_id=current_user.id
                        )
                        db.session.add(link)

        # Actualizar totales
        invoice.update_total()

        # تحديث أرصدة الحسابات إذا تغيرت الحالة أو الحسابات أو المبلغ
        if (old_status != invoice.status or
            old_pending_account_id != invoice.pending_account_id or
            old_paid_account_id != invoice.paid_account_id or
            old_overdue_account_id != invoice.overdue_account_id or
            old_total != invoice.total_amount):

            # إزالة التأثير السابق
            if old_status == 'pending' and old_pending_account_id:
                old_pending_account = Account.query.get(old_pending_account_id)
                if old_pending_account:
                    old_pending_account.update_balance(old_total, 'subtract')
            elif old_status == 'paid' and old_paid_account_id:
                old_paid_account = Account.query.get(old_paid_account_id)
                if old_paid_account:
                    old_paid_account.update_balance(old_total, 'subtract')
            elif old_status == 'overdue' and old_overdue_account_id:
                old_overdue_account = Account.query.get(old_overdue_account_id)
                if old_overdue_account:
                    old_overdue_account.update_balance(old_total, 'subtract')

            # تطبيق التأثير الجديد
            invoice.update_account_balances()

        db.session.commit()

        flash('تم تحديث الفاتورة بنجاح', 'success')
        return redirect(url_for('finance.view_invoice', id=invoice.id))

    return render_template('finance/edit_invoice.html', title=f'تعديل الفاتورة: {invoice.invoice_number}',
                          invoice=invoice, clients=clients, projects=projects, employees=employees,
                          accounts=accounts)

@finance_bp.route('/delete_invoice/<int:id>', methods=['GET', 'POST'])
@login_required
def delete_invoice(id):
    # Only admin or finance can delete invoices
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance')):
        flash('لا تملك صلاحية حذف الفواتير', 'danger')
        return redirect(url_for('finance.invoices'))

    invoice = Invoice.query.get_or_404(id)

    if request.method == 'POST':
        # Only admin can delete paid invoices
        if invoice.status == 'paid' and not current_user.has_role('admin'):
            flash('لا يمكن حذف الفواتير المدفوعة إلا بواسطة المسؤول', 'warning')
            return redirect(url_for('finance.view_invoice', id=id))

        # إلغاء تأثير الفاتورة على أرصدة الحسابات
        if invoice.status == 'pending' and invoice.pending_account_id:
            pending_account = Account.query.get(invoice.pending_account_id)
            if pending_account:
                pending_account.update_balance(invoice.total_amount, 'subtract')
        elif invoice.status == 'paid' and invoice.paid_account_id:
            paid_account = Account.query.get(invoice.paid_account_id)
            if paid_account:
                paid_account.update_balance(invoice.total_amount, 'subtract')
        elif invoice.status == 'overdue' and invoice.overdue_account_id:
            overdue_account = Account.query.get(invoice.overdue_account_id)
            if overdue_account:
                overdue_account.update_balance(invoice.total_amount, 'subtract')

        # Delete attachments from filesystem
        for attachment in invoice.attachments:
            file_path = os.path.join('app', 'static', attachment.file_path)
            try:
                if os.path.exists(file_path) and os.path.isfile(file_path):
                    os.remove(file_path)
            except Exception as e:
                print(f"Error deleting file {file_path}: {str(e)}")
                # Continue with deletion even if file removal fails

        db.session.delete(invoice)
        db.session.commit()

        flash('تم حذف الفاتورة بنجاح', 'success')
        return redirect(url_for('finance.invoices'))

    return render_template('finance/delete_invoice.html', title='حذف الفاتورة', invoice=invoice)

@finance_bp.route('/add_invoice_item/<int:invoice_id>', methods=['GET', 'POST'])
@login_required
def add_invoice_item(invoice_id):
    # Check if user has permission to edit invoices
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance')):
        flash('لا تملك صلاحية تعديل الفواتير', 'danger')
        return redirect(url_for('finance.invoices'))

    invoice = Invoice.query.get_or_404(invoice_id)

    # Cannot edit paid invoices unless admin
    if invoice.status == 'paid' and not current_user.has_role('admin'):
        flash('لا يمكن تعديل الفواتير المدفوعة إلا بواسطة المسؤول', 'warning')
        return redirect(url_for('finance.view_invoice', id=invoice_id))

    # Get employees for supervisor selection
    employees = User.query.all()

    if request.method == 'POST':
        description = request.form.get('description')
        quantity = float(request.form.get('quantity'))
        unit_price = float(request.form.get('unit_price'))
        supervisor_id = request.form.get('supervisor_id')
        company_profit_type = request.form.get('company_profit_type', 'percentage')
        company_profit_value = float(request.form.get('company_profit_value', 0))
        status = request.form.get('status', 'غير مستلم')
        receipt_date_str = request.form.get('receipt_date')

        # Procesar fecha de recepción si existe
        receipt_date = None
        if receipt_date_str:
            receipt_date = datetime.strptime(receipt_date_str, '%Y-%m-%d')

        # Create new invoice item
        item = InvoiceItem(
            description=description,
            quantity=quantity,
            unit_price=unit_price,
            supervisor_id=supervisor_id if supervisor_id else None,
            company_profit_type=company_profit_type,
            company_profit_value=company_profit_value,
            status=status,
            receipt_date=receipt_date,
            invoice_id=invoice_id
        )

        db.session.add(item)

        # Update invoice total
        invoice.update_total()
        db.session.commit()

        flash('تم إضافة عنصر الفاتورة بنجاح', 'success')
        return redirect(url_for('finance.edit_invoice', id=invoice_id))

    return render_template('finance/add_invoice_item.html', title=f'إضافة عنصر للفاتورة: {invoice.invoice_number}',
                          invoice=invoice, employees=employees)

@finance_bp.route('/delete_invoice_item/<int:item_id>', methods=['POST'])
@login_required
def delete_invoice_item(item_id):
    # Check if user has permission to edit invoices
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance')):
        flash('لا تملك صلاحية تعديل الفواتير', 'danger')
        return redirect(url_for('finance.invoices'))

    item = InvoiceItem.query.get_or_404(item_id)
    invoice_id = item.invoice_id
    invoice = Invoice.query.get(invoice_id)

    # Cannot edit paid invoices unless admin
    if invoice.status == 'paid' and not current_user.has_role('admin'):
        flash('لا يمكن تعديل الفواتير المدفوعة إلا بواسطة المسؤول', 'warning')
        return redirect(url_for('finance.view_invoice', id=invoice_id))

    db.session.delete(item)

    # Update invoice total
    invoice.update_total()
    db.session.commit()

    flash('تم حذف عنصر الفاتورة بنجاح', 'success')
    return redirect(url_for('finance.edit_invoice', id=invoice_id))

@finance_bp.route('/delete_invoice_attachment/<int:attachment_id>', methods=['POST'])
@login_required
def delete_invoice_attachment(attachment_id):
    # Check if user has permission to edit invoices
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance')):
        flash('لا تملك صلاحية تعديل الفواتير', 'danger')
        return redirect(url_for('finance.invoices'))

    # Get the attachment with a fresh query to ensure it's attached to the session
    attachment = InvoiceAttachment.query.get_or_404(attachment_id)

    # Store invoice_id before deleting the attachment
    invoice_id = attachment.invoice_id

    # Get the invoice with a fresh query
    invoice = Invoice.query.get_or_404(invoice_id)

    # Cannot edit paid invoices unless admin
    if invoice.status == 'paid' and not current_user.has_role('admin'):
        flash('لا يمكن تعديل الفواتير المدفوعة إلا بواسطة المسؤول', 'warning')
        return redirect(url_for('finance.view_invoice', id=invoice_id))

    # Get file path before deleting from database
    file_path = os.path.join('app', 'static', attachment.file_path)

    try:
        # Delete from database first
        db.session.delete(attachment)
        db.session.commit()

        # Then delete file from disk if it exists
        if os.path.exists(file_path):
            os.remove(file_path)

        flash('تم حذف المرفق بنجاح', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف المرفق: {str(e)}', 'danger')

    return redirect(url_for('finance.edit_invoice', id=invoice_id))

@finance_bp.route('/mark_invoice_paid/<int:id>', methods=['POST'])
@login_required
def mark_invoice_paid(id):
    # Check if user has permission to manage invoices
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance')):
        flash('لا تملك صلاحية إدارة الفواتير', 'danger')
        return redirect(url_for('finance.invoices'))

    invoice = Invoice.query.get_or_404(id)

    # Already paid
    if invoice.status == 'paid':
        flash('الفاتورة مدفوعة بالفعل', 'info')
        return redirect(url_for('finance.view_invoice', id=id))

    # Mark as paid and create transaction
    invoice.mark_as_paid()
    db.session.commit()

    flash('تم تحديد الفاتورة كمدفوعة بنجاح', 'success')
    return redirect(url_for('finance.view_invoice', id=id))

@finance_bp.route('/reports')
@login_required
def reports():
    # Check if user has permission to view reports
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance')):
        flash('لا تملك صلاحية الوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard.index'))

    # Get clients and departments for filters
    clients = Client.query.all()
    departments = Department.query.all()

    # Get expense categories for filters
    categories = set()
    for transaction in Transaction.query.all():
        if transaction.category:
            categories.add(transaction.category)

    return render_template('finance/reports.html', title='Financial Reports',
                          clients=clients, departments=departments, categories=list(categories))


@finance_bp.route('/view_transaction/<int:transaction_id>')
@login_required
def view_transaction(transaction_id):
    # Get the transaction
    transaction = Transaction.query.get_or_404(transaction_id)

    return render_template('finance/view_transaction.html', title='عرض المعاملة', transaction=transaction)


@finance_bp.route('/attachments')
@login_required
def attachments():
    # Check if user has permission to view attachments
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance')):
        flash('لا تملك صلاحية عرض المرفقات', 'danger')
        return redirect(url_for('finance.index'))

    # Get all attachments
    attachments = TransactionAttachment.query.order_by(TransactionAttachment.uploaded_at.desc()).all()
    return render_template('finance/attachments.html', title='إدارة المرفقات', attachments=attachments)


@finance_bp.route('/download_attachment/<int:attachment_id>')
@login_required
def download_attachment(attachment_id):
    # Get the attachment
    attachment = TransactionAttachment.query.get_or_404(attachment_id)

    # Check if user has permission to download the attachment
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance')):
        flash('لا تملك صلاحية تنزيل المرفقات', 'danger')
        return redirect(url_for('finance.index'))

    # Get the file path
    file_path = os.path.join('app', 'static', attachment.file_path)

    # Check if file exists
    if not os.path.exists(file_path):
        flash('الملف غير موجود', 'danger')
        return redirect(url_for('finance.attachments'))

    # Return the file as an attachment
    return send_file(file_path, as_attachment=True, download_name=attachment.filename)


@finance_bp.route('/download_invoice_attachment/<int:attachment_id>')
@login_required
def download_invoice_attachment(attachment_id):
    # Get the attachment
    attachment = InvoiceAttachment.query.get_or_404(attachment_id)

    # Check if user has permission to download the attachment
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance')):
        flash('لا تملك صلاحية تنزيل المرفقات', 'danger')
        return redirect(url_for('finance.index'))

    # Get the file path
    file_path = os.path.join('app', 'static', attachment.file_path)

    # Check if file exists
    if not os.path.exists(file_path):
        flash('الملف غير موجود', 'danger')
        return redirect(url_for('finance.invoices'))

    # Return the file as an attachment
    return send_file(file_path, as_attachment=True, download_name=attachment.filename)


@finance_bp.route('/delete_attachment/<int:attachment_id>')
@login_required
def delete_attachment(attachment_id):
    # Check if user has permission to delete attachments
    if not (current_user.has_role('admin') or current_user.has_role('finance')):
        flash('لا تملك صلاحية حذف المرفقات', 'danger')
        return redirect(url_for('finance.attachments'))

    # Get the attachment
    attachment = TransactionAttachment.query.get_or_404(attachment_id)

    # Get the file path
    file_path = os.path.join('app', 'static', attachment.file_path)

    # Delete the file if it exists
    if os.path.exists(file_path):
        os.remove(file_path)

    # Delete the attachment from the database
    db.session.delete(attachment)
    db.session.commit()

    flash('تم حذف المرفق بنجاح', 'success')
    return redirect(url_for('finance.attachments'))


@finance_bp.route('/delete_transaction_verification_link/<int:link_id>', methods=['GET', 'POST'])
@login_required
def delete_transaction_verification_link(link_id):
    # Check if user has permission to edit transactions
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance')):
        flash('لا تملك صلاحية تعديل المعاملات', 'danger')
        return redirect(url_for('finance.transactions'))

    # Get the link
    link = TransactionVerificationLink.query.get_or_404(link_id)
    transaction_id = link.transaction_id

    # Delete the link
    db.session.delete(link)
    db.session.commit()

    flash('تم حذف رابط الإثبات بنجاح', 'success')
    return redirect(url_for('finance.view_transaction', transaction_id=transaction_id))


@finance_bp.route('/delete_invoice_verification_link/<int:link_id>', methods=['GET', 'POST'])
@login_required
def delete_invoice_verification_link(link_id):
    # Check if user has permission to edit invoices
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance')):
        flash('لا تملك صلاحية تعديل الفواتير', 'danger')
        return redirect(url_for('finance.invoices'))

    # Get the link
    link = InvoiceVerificationLink.query.get_or_404(link_id)
    invoice_id = link.invoice_id

    # Delete the link
    db.session.delete(link)
    db.session.commit()

    flash('تم حذف رابط الإثبات بنجاح', 'success')
    return redirect(url_for('finance.view_invoice', id=invoice_id))


# ==================== شجرة الحسابات ====================

@finance_bp.route('/accounts')
@login_required
def accounts():
    """صفحة شجرة الحسابات مع الفلترة"""
    # Check if user has permission to view accounts
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance')):
        flash('لا تملك صلاحية الوصول إلى شجرة الحسابات', 'danger')
        return redirect(url_for('finance.index'))

    # الحصول على معاملات الفلترة
    filter_type = request.args.get('filter_type')
    filter_month = request.args.get('filter_month')
    filter_year = request.args.get('filter_year')
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')

    # Get all folders and accounts organized in tree structure
    folders = AccountFolder.query.filter_by(parent_id=None).order_by(AccountFolder.sort_order, AccountFolder.name).all()
    root_accounts = Account.query.filter_by(parent_id=None, folder_id=None).order_by(Account.name).all()

    # تنظيم الحسابات حسب المجلدات
    for folder in folders:
        folder.accounts = Account.query.filter_by(folder_id=folder.id, parent_id=None).order_by(Account.name).all()

    # تطبيق الفلترة إذا تم تحديدها
    if filter_type:
        # حساب أرصدة الحسابات للفترة المحددة
        for account in root_accounts:
            account.filtered_balance = calculate_account_balance_for_period(
                account, filter_type, filter_month, filter_year, start_date, end_date
            )
            # تطبيق نفس الحساب على الحسابات الفرعية
            calculate_children_balances_for_period(
                account, filter_type, filter_month, filter_year, start_date, end_date
            )

        # تطبيق الفلترة على حسابات المجلدات
        for folder in folders:
            for account in folder.accounts:
                account.filtered_balance = calculate_account_balance_for_period(
                    account, filter_type, filter_month, filter_year, start_date, end_date
                )
                calculate_children_balances_for_period(
                    account, filter_type, filter_month, filter_year, start_date, end_date
                )

    return render_template('finance/accounts.html', title='شجرة الحسابات',
                          folders=folders,
                          root_accounts=root_accounts,
                          filter_applied=bool(filter_type))

def calculate_account_balance_for_period(account, filter_type, filter_month, filter_year, start_date, end_date):
    """حساب رصيد الحساب للفترة المحددة"""
    from datetime import datetime

    # تحديد الفترة الزمنية
    start_dt = None
    end_dt = None

    if filter_type == 'month' and filter_month:
        year, month = map(int, filter_month.split('-'))
        start_dt = datetime(year, month, 1)
        if month == 12:
            end_dt = datetime(year + 1, 1, 1)
        else:
            end_dt = datetime(year, month + 1, 1)
    elif filter_type == 'year' and filter_year:
        year = int(filter_year)
        start_dt = datetime(year, 1, 1)
        end_dt = datetime(year + 1, 1, 1)
    elif filter_type == 'range' and start_date and end_date:
        start_dt = datetime.strptime(start_date, '%Y-%m-%d')
        end_dt = datetime.strptime(end_date, '%Y-%m-%d')
        end_dt = end_dt.replace(hour=23, minute=59, second=59)

    if not start_dt or not end_dt:
        return 0

    total_balance = 0

    # حساب من الرواتب
    salaries = EmployeeSalary.query.filter(
        EmployeeSalary.created_at >= start_dt,
        EmployeeSalary.created_at < end_dt
    ).all()

    for salary in salaries:
        if salary.pending_account_id == account.id and salary.status == 'pending':
            total_balance += salary.amount
        elif salary.paid_account_id == account.id and salary.status == 'paid':
            total_balance += salary.amount
        elif salary.deduction_account_id == account.id:
            total_balance -= salary.amount  # الخصومات تقلل من الرصيد

    # حساب من المعاملات المالية
    from app.models.finance import FinancialTransactionItem
    transaction_items = FinancialTransactionItem.query.filter(
        FinancialTransactionItem.account_id == account.id,
        FinancialTransactionItem.transaction_date >= start_dt,
        FinancialTransactionItem.transaction_date < end_dt
    ).all()

    for item in transaction_items:
        if item.action == 'add':
            total_balance += item.amount
        elif item.action == 'subtract':
            total_balance -= item.amount

    # حساب من الفواتير
    from app.models.finance import Invoice
    invoices = Invoice.query.filter(
        Invoice.created_at >= start_dt,
        Invoice.created_at < end_dt
    ).all()

    for invoice in invoices:
        if invoice.pending_account_id == account.id and invoice.status == 'pending':
            total_balance += invoice.total_amount
        elif invoice.paid_account_id == account.id and invoice.status == 'paid':
            total_balance += invoice.total_amount
        elif invoice.overdue_account_id == account.id and invoice.status == 'overdue':
            total_balance += invoice.total_amount

    return total_balance

def calculate_children_balances_for_period(account, filter_type, filter_month, filter_year, start_date, end_date):
    """حساب أرصدة الحسابات الفرعية للفترة المحددة"""
    for child in account.children:
        child.filtered_balance = calculate_account_balance_for_period(
            child, filter_type, filter_month, filter_year, start_date, end_date
        )
        # تطبيق نفس الحساب على الحسابات الفرعية للطفل
        calculate_children_balances_for_period(
            child, filter_type, filter_month, filter_year, start_date, end_date
        )

@finance_bp.route('/accounts/add', methods=['GET', 'POST'])
@login_required
def add_account():
    """إضافة حساب جديد"""
    # Check if user has permission to add accounts
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance')):
        flash('لا تملك صلاحية إضافة الحسابات', 'danger')
        return redirect(url_for('finance.accounts'))

    if request.method == 'POST':
        name = request.form.get('name')
        code = request.form.get('code')
        description = request.form.get('description')
        account_type = request.form.get('account_type', 'general')
        parent_id = request.form.get('parent_id')
        currency_id = request.form.get('currency_id', 1)
        account_color = request.form.get('account_color', '#007bff')
        text_color = request.form.get('text_color', '#ffffff')
        folder_id = request.form.get('folder_id')

        if not name:
            flash('اسم الحساب مطلوب', 'danger')
            return redirect(url_for('finance.add_account'))

        # Check if code is unique
        if code and Account.query.filter_by(code=code).first():
            flash('رمز الحساب موجود بالفعل', 'danger')
            return redirect(url_for('finance.add_account'))

        # Create new account
        account = Account(
            name=name,
            code=code if code else None,
            description=description,
            account_type=account_type,
            parent_id=int(parent_id) if parent_id else None,
            currency_id=int(currency_id),
            account_color=account_color,
            text_color=text_color,
            folder_id=int(folder_id) if folder_id else None
        )

        db.session.add(account)
        db.session.commit()

        flash('تم إضافة الحساب بنجاح', 'success')
        return redirect(url_for('finance.accounts'))

    # Get all accounts for parent selection
    all_accounts = Account.query.order_by(Account.name).all()

    # Get all currencies
    from app.models.currency import Currency
    currencies = Currency.query.order_by(Currency.name).all()

    # Get all folders
    folders = AccountFolder.query.order_by(AccountFolder.sort_order, AccountFolder.name).all()

    return render_template('finance/add_account.html', title='إضافة حساب جديد',
                          all_accounts=all_accounts, currencies=currencies, folders=folders)

@finance_bp.route('/accounts/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def edit_account(id):
    """تعديل حساب"""
    # Check if user has permission to edit accounts
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance')):
        flash('لا تملك صلاحية تعديل الحسابات', 'danger')
        return redirect(url_for('finance.accounts'))

    account = Account.query.get_or_404(id)

    if request.method == 'POST':
        name = request.form.get('name')
        code = request.form.get('code')
        description = request.form.get('description')
        account_type = request.form.get('account_type', 'general')
        parent_id = request.form.get('parent_id')
        currency_id = request.form.get('currency_id', 1)
        account_color = request.form.get('account_color', '#007bff')
        text_color = request.form.get('text_color', '#ffffff')
        folder_id = request.form.get('folder_id')
        is_active = 'is_active' in request.form

        if not name:
            flash('اسم الحساب مطلوب', 'danger')
            return redirect(url_for('finance.edit_account', id=id))

        # Check if code is unique (excluding current account)
        if code:
            existing_account = Account.query.filter_by(code=code).first()
            if existing_account and existing_account.id != account.id:
                flash('رمز الحساب موجود بالفعل', 'danger')
                return redirect(url_for('finance.edit_account', id=id))

        # Update account
        account.name = name
        account.code = code if code else None
        account.description = description
        account.account_type = account_type
        account.parent_id = int(parent_id) if parent_id else None
        account.currency_id = int(currency_id)
        account.account_color = account_color
        account.text_color = text_color
        account.folder_id = int(folder_id) if folder_id else None
        account.is_active = is_active
        account.updated_at = datetime.utcnow()

        db.session.commit()

        flash('تم تحديث الحساب بنجاح', 'success')
        return redirect(url_for('finance.accounts'))

    # Get all accounts for parent selection (excluding current account and its children)
    all_accounts = Account.query.filter(Account.id != id).order_by(Account.name).all()
    # Remove children of current account from the list
    children_ids = [child.id for child in account.get_all_children()]
    all_accounts = [acc for acc in all_accounts if acc.id not in children_ids]

    # Get all currencies
    from app.models.currency import Currency
    currencies = Currency.query.order_by(Currency.name).all()

    # Get all folders
    folders = AccountFolder.query.order_by(AccountFolder.sort_order, AccountFolder.name).all()

    return render_template('finance/edit_account.html', title='تعديل الحساب',
                          account=account, all_accounts=all_accounts, currencies=currencies, folders=folders)

@finance_bp.route('/accounts/delete/<int:id>', methods=['GET', 'POST'])
@login_required
def delete_account(id):
    """حذف حساب"""
    # Check if user has permission to delete accounts
    if not (current_user.has_role('admin') or current_user.has_role('finance')):
        flash('لا تملك صلاحية حذف الحسابات', 'danger')
        return redirect(url_for('finance.accounts'))

    account = Account.query.get_or_404(id)

    if request.method == 'GET':
        # Get child accounts
        child_accounts = Account.query.filter_by(parent_id=id).all()
        return render_template('finance/delete_account.html', title='حذف الحساب',
                             account=account, child_accounts=child_accounts)

    account = Account.query.get_or_404(id)

    # Check if account has children
    if account.children:
        flash('لا يمكن حذف حساب يحتوي على حسابات فرعية', 'danger')
        return redirect(url_for('finance.accounts'))

    # Check if account is being used
    if (account.salary_pending_account or account.salary_deduction_account or
        account.salary_paid_account or account.transaction_items):
        flash('لا يمكن حذف حساب مستخدم في معاملات أخرى', 'danger')
        return redirect(url_for('finance.accounts'))

    db.session.delete(account)
    db.session.commit()

    flash('تم حذف الحساب بنجاح', 'success')
    return redirect(url_for('finance.accounts'))

# ==================== إدارة المجلدات ====================

@finance_bp.route('/folders')
@login_required
def folders():
    """صفحة إدارة المجلدات"""
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance')):
        flash('لا تملك صلاحية إدارة المجلدات', 'danger')
        return redirect(url_for('finance.accounts'))

    folders = AccountFolder.query.order_by(AccountFolder.sort_order, AccountFolder.name).all()
    return render_template('finance/folders.html', title='إدارة المجلدات', folders=folders)

@finance_bp.route('/folders/add', methods=['GET', 'POST'])
@login_required
def add_folder():
    """إضافة مجلد جديد"""
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance')):
        flash('لا تملك صلاحية إضافة المجلدات', 'danger')
        return redirect(url_for('finance.folders'))

    if request.method == 'POST':
        name = request.form.get('name')
        description = request.form.get('description')
        icon = request.form.get('icon', 'fas fa-folder')
        color = request.form.get('color', '#6c757d')
        parent_id = request.form.get('parent_id')
        sort_order = request.form.get('sort_order', 0)

        if not name:
            flash('اسم المجلد مطلوب', 'danger')
            return redirect(url_for('finance.add_folder'))

        folder = AccountFolder(
            name=name,
            description=description,
            icon=icon,
            color=color,
            parent_id=int(parent_id) if parent_id else None,
            sort_order=int(sort_order)
        )

        db.session.add(folder)
        db.session.commit()

        flash('تم إضافة المجلد بنجاح', 'success')
        return redirect(url_for('finance.folders'))

    # Get all folders for parent selection
    all_folders = AccountFolder.query.order_by(AccountFolder.name).all()
    return render_template('finance/add_folder.html', title='إضافة مجلد جديد', all_folders=all_folders)

@finance_bp.route('/folders/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def edit_folder(id):
    """تعديل مجلد"""
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance')):
        flash('لا تملك صلاحية تعديل المجلدات', 'danger')
        return redirect(url_for('finance.folders'))

    folder = AccountFolder.query.get_or_404(id)

    if request.method == 'POST':
        name = request.form.get('name')
        description = request.form.get('description')
        icon = request.form.get('icon', 'fas fa-folder')
        color = request.form.get('color', '#6c757d')
        parent_id = request.form.get('parent_id')
        sort_order = request.form.get('sort_order', 0)
        is_expanded = 'is_expanded' in request.form

        if not name:
            flash('اسم المجلد مطلوب', 'danger')
            return redirect(url_for('finance.edit_folder', id=id))

        folder.name = name
        folder.description = description
        folder.icon = icon
        folder.color = color
        folder.parent_id = int(parent_id) if parent_id else None
        folder.sort_order = int(sort_order)
        folder.is_expanded = is_expanded
        folder.updated_at = datetime.utcnow()

        db.session.commit()

        flash('تم تحديث المجلد بنجاح', 'success')
        return redirect(url_for('finance.folders'))

    # Get all folders for parent selection (excluding current folder)
    all_folders = AccountFolder.query.filter(AccountFolder.id != id).order_by(AccountFolder.name).all()
    return render_template('finance/edit_folder.html', title='تعديل المجلد', folder=folder, all_folders=all_folders)

@finance_bp.route('/folders/delete/<int:id>', methods=['GET', 'POST'])
@login_required
def delete_folder(id):
    """حذف مجلد"""
    if not (current_user.has_role('admin') or current_user.has_role('finance')):
        flash('لا تملك صلاحية حذف المجلدات', 'danger')
        return redirect(url_for('finance.folders'))

    folder = AccountFolder.query.get_or_404(id)

    if request.method == 'GET':
        return render_template('finance/delete_folder.html', title='حذف المجلد', folder=folder)

    # Check if folder has accounts
    if folder.accounts:
        flash('لا يمكن حذف مجلد يحتوي على حسابات', 'danger')
        return redirect(url_for('finance.folders'))

    # Check if folder has subfolders
    if folder.children:
        flash('لا يمكن حذف مجلد يحتوي على مجلدات فرعية', 'danger')
        return redirect(url_for('finance.folders'))

    db.session.delete(folder)
    db.session.commit()

    flash('تم حذف المجلد بنجاح', 'success')
    return redirect(url_for('finance.folders'))

@finance_bp.route('/accounts/view/<int:id>')
@login_required
def view_account(id):
    """عرض تفاصيل الحساب والمعاملات المرتبطة به مع الفلترة"""
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance')):
        flash('لا تملك صلاحية عرض تفاصيل الحسابات', 'danger')
        return redirect(url_for('finance.accounts'))

    account = Account.query.get_or_404(id)

    # الحصول على معاملات الفلترة
    filter_type = request.args.get('filter_type')
    filter_month = request.args.get('filter_month')
    filter_year = request.args.get('filter_year')
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')

    # تحديد الفترة الزمنية للفلترة
    start_dt = None
    end_dt = None

    if filter_type:
        from datetime import datetime
        if filter_type == 'month' and filter_month:
            year, month = map(int, filter_month.split('-'))
            start_dt = datetime(year, month, 1)
            if month == 12:
                end_dt = datetime(year + 1, 1, 1)
            else:
                end_dt = datetime(year, month + 1, 1)
        elif filter_type == 'year' and filter_year:
            year = int(filter_year)
            start_dt = datetime(year, 1, 1)
            end_dt = datetime(year + 1, 1, 1)
        elif filter_type == 'range' and start_date and end_date:
            start_dt = datetime.strptime(start_date, '%Y-%m-%d')
            end_dt = datetime.strptime(end_date, '%Y-%m-%d')
            end_dt = end_dt.replace(hour=23, minute=59, second=59)

    # البحث عن المعاملات المرتبطة بهذا الحساب مع الفلترة
    financial_query = FinancialTransactionItem.query.filter_by(account_id=id)

    # فلترة عامة بالتاريخ
    if start_dt and end_dt:
        financial_query = financial_query.filter(
            FinancialTransactionItem.transaction_date >= start_dt,
            FinancialTransactionItem.transaction_date < end_dt
        )

    # فلترة خاصة بالمعاملات المالية
    trans_action = request.args.get('trans_action')
    trans_status = request.args.get('trans_status')
    trans_date_from = request.args.get('trans_date_from')
    trans_date_to = request.args.get('trans_date_to')

    if trans_action:
        financial_query = financial_query.filter(FinancialTransactionItem.action == trans_action)

    if trans_status:
        financial_query = financial_query.join(FinancialTransaction).filter(FinancialTransaction.status == trans_status)

    if trans_date_from:
        trans_start_dt = datetime.strptime(trans_date_from, '%Y-%m-%d')
        financial_query = financial_query.filter(FinancialTransactionItem.transaction_date >= trans_start_dt)

    if trans_date_to:
        trans_end_dt = datetime.strptime(trans_date_to, '%Y-%m-%d')
        trans_end_dt = trans_end_dt.replace(hour=23, minute=59, second=59)
        financial_query = financial_query.filter(FinancialTransactionItem.transaction_date <= trans_end_dt)

    # تقسيم الصفحات للمعاملات المالية
    trans_page = request.args.get('trans_page', 1, type=int)
    trans_per_page = request.args.get('trans_per_page', 10, type=int)

    financial_transactions = financial_query.order_by(FinancialTransactionItem.transaction_date.desc()).paginate(
        page=trans_page, per_page=trans_per_page, error_out=False
    )

    # البحث عن الرواتب المرتبطة بهذا الحساب
    salary_query = EmployeeSalary.query.filter(
        (EmployeeSalary.pending_account_id == id) |
        (EmployeeSalary.paid_account_id == id) |
        (EmployeeSalary.deduction_account_id == id)
    )
    if start_dt and end_dt:
        salary_query = salary_query.filter(
            EmployeeSalary.created_at >= start_dt,
            EmployeeSalary.created_at < end_dt
        )
    salaries = salary_query.order_by(EmployeeSalary.created_at.desc()).all()

    # البحث عن الفواتير المرتبطة بهذا الحساب
    from app.models.finance import Invoice
    invoice_query = Invoice.query.filter(
        (Invoice.pending_account_id == id) |
        (Invoice.paid_account_id == id) |
        (Invoice.overdue_account_id == id)
    )
    if start_dt and end_dt:
        invoice_query = invoice_query.filter(
            Invoice.created_at >= start_dt,
            Invoice.created_at < end_dt
        )
    invoices = invoice_query.order_by(Invoice.created_at.desc()).all()

    # حساب الرصيد للفترة المحددة
    filtered_balance = None
    if filter_type:
        filtered_balance = calculate_account_balance_for_period(
            account, filter_type, filter_month, filter_year, start_date, end_date
        )

    return render_template('finance/view_account.html',
                          title=f'تفاصيل الحساب: {account.name}',
                          account=account,
                          financial_transactions=financial_transactions,
                          salaries=salaries,
                          invoices=invoices,
                          filtered_balance=filtered_balance,
                          filter_applied=bool(filter_type))

@finance_bp.route('/accounts/reset/<int:id>', methods=['POST'])
@login_required
def reset_account(id):
    """تصفير الحساب وحذف جميع المعاملات المرتبطة به"""
    if not (current_user.has_role('admin') or current_user.has_role('finance')):
        return jsonify({'success': False, 'message': 'لا تملك صلاحية تصفير الحسابات'})

    try:
        account = Account.query.get_or_404(id)

        # حذف جميع المعاملات المالية المرتبطة بهذا الحساب
        financial_transactions = FinancialTransactionItem.query.filter_by(account_id=id).all()
        for item in financial_transactions:
            # حذف المعاملة الكاملة إذا كانت تحتوي على هذا البند فقط
            transaction = item.transaction
            if transaction.items.count() == 1:
                db.session.delete(transaction)
            else:
                db.session.delete(item)

        # حذف جميع الرواتب المرتبطة بهذا الحساب
        salaries_pending = EmployeeSalary.query.filter_by(pending_account_id=id).all()
        salaries_paid = EmployeeSalary.query.filter_by(paid_account_id=id).all()
        salaries_deduction = EmployeeSalary.query.filter_by(deduction_account_id=id).all()

        for salary in salaries_pending + salaries_paid + salaries_deduction:
            db.session.delete(salary)

        # تصفير رصيد الحساب
        account.balance = 0.0
        account.updated_at = datetime.utcnow()

        db.session.commit()

        return jsonify({'success': True, 'message': 'تم تصفير الحساب بنجاح'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})


# ==================== رواتب الموظفين ====================

@finance_bp.route('/salaries')
@login_required
def salaries():
    """صفحة رواتب الموظفين"""
    # Check if user has permission to view salaries
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance')):
        flash('لا تملك صلاحية الوصول إلى رواتب الموظفين', 'danger')
        return redirect(url_for('finance.index'))

    # Get filter parameters
    status_filter = request.args.get('status', '')
    employee_filter = request.args.get('employee', '')
    date_from = request.args.get('date_from', '')
    date_to = request.args.get('date_to', '')
    amount_min = request.args.get('amount_min', type=float)
    amount_max = request.args.get('amount_max', type=float)
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)

    # Build query
    query = EmployeeSalary.query

    if status_filter:
        query = query.filter(EmployeeSalary.status == status_filter)

    if employee_filter:
        query = query.filter(
            db.or_(
                EmployeeSalary.employee_name.contains(employee_filter),
                EmployeeSalary.employee_manual.contains(employee_filter)
            )
        )

    if date_from:
        try:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
            query = query.filter(EmployeeSalary.created_at >= date_from_obj)
        except ValueError:
            pass

    if date_to:
        try:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d')
            # Add one day to include the entire day
            date_to_obj = date_to_obj.replace(hour=23, minute=59, second=59)
            query = query.filter(EmployeeSalary.created_at <= date_to_obj)
        except ValueError:
            pass

    if amount_min is not None:
        # Filter by total amount (calculated from items)
        query = query.join(SalaryItem).group_by(EmployeeSalary.id).having(
            db.func.sum(SalaryItem.amount) >= amount_min
        )

    if amount_max is not None:
        if amount_min is None:
            query = query.join(SalaryItem).group_by(EmployeeSalary.id)
        query = query.having(db.func.sum(SalaryItem.amount) <= amount_max)

    # Get paginated results
    salaries = query.order_by(EmployeeSalary.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )

    # Get all employees for filter
    employees = User.query.filter_by(is_active=True).all()

    return render_template('finance/salaries.html', title='رواتب الموظفين',
                          salaries=salaries, employees=employees,
                          status_filter=status_filter, employee_filter=employee_filter,
                          date_from=date_from, date_to=date_to,
                          amount_min=amount_min, amount_max=amount_max,
                          per_page=per_page)

@finance_bp.route('/salaries/settings', methods=['GET', 'POST'])
@login_required
def salary_settings():
    """إعدادات رواتب الموظفين"""
    # Check if user has permission to manage salary settings
    if not (current_user.has_role('admin') or current_user.has_role('manager')):
        flash('لا تملك صلاحية إدارة إعدادات الرواتب', 'danger')
        return redirect(url_for('finance.salaries'))

    if request.method == 'POST':
        # This would typically save settings to a configuration table
        # For now, we'll just show a success message
        flash('تم حفظ الإعدادات بنجاح', 'success')
        return redirect(url_for('finance.salaries'))

    # Get all accounts for settings
    accounts = Account.query.filter_by(is_active=True).order_by(Account.name).all()

    return render_template('finance/salary_settings.html', title='إعدادات الرواتب',
                          accounts=accounts)

@finance_bp.route('/salaries/add', methods=['GET', 'POST'])
@login_required
def add_salary():
    """إضافة راتب جديد"""
    # Check if user has permission to add salaries
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance')):
        flash('لا تملك صلاحية إضافة الرواتب', 'danger')
        return redirect(url_for('finance.salaries'))

    if request.method == 'POST':
        employee_type = request.form.get('employee_type', 'manual')
        employee_id = request.form.get('employee_id')
        employee_name = request.form.get('employee_name')
        employee_manual = request.form.get('employee_manual')
        status = request.form.get('status', 'draft')
        transfer_date = request.form.get('transfer_date')
        supervisor_id = request.form.get('supervisor_id')
        notes = request.form.get('notes')

        # Account settings
        pending_account_id = request.form.get('pending_account_id')
        deduction_account_id = request.form.get('deduction_account_id')
        paid_account_id = request.form.get('paid_account_id')

        # Commission settings
        commission_type = request.form.get('commission_type', 'percentage')
        commission_value = request.form.get('commission_value', type=float) or 0
        commission_account_id = request.form.get('commission_account_id')

        # Validate salary items
        items_data = []
        item_index = 0
        while f'items[{item_index}][amount]' in request.form:
            amount = request.form.get(f'items[{item_index}][amount]', type=float)
            if amount and amount > 0:
                items_data.append({
                    'project_id': request.form.get(f'items[{item_index}][project_id]') or None,
                    'task_id': request.form.get(f'items[{item_index}][task_id]') or None,
                    'amount': amount,
                    'description': request.form.get(f'items[{item_index}][description]') or ''
                })
            item_index += 1

        if not items_data:
            flash('يجب إضافة بند واحد على الأقل', 'danger')
            return redirect(url_for('finance.add_salary'))

        if not employee_name and not employee_id:
            flash('اسم الموظف مطلوب', 'danger')
            return redirect(url_for('finance.add_salary'))

        # Create new salary
        if employee_type == 'system':
            salary = EmployeeSalary(
                employee_id=int(employee_id) if employee_id else None,
                employee_name=None,  # لا نحفظ اسم يدوي للموظف من النظام
                employee_manual=None,  # لا نحفظ تفاصيل يدوية للموظف من النظام
                status=status,
                transfer_date=datetime.strptime(transfer_date, '%Y-%m-%dT%H:%M') if transfer_date else None,
                supervisor_id=int(supervisor_id) if supervisor_id else None,
                notes=notes,
                pending_account_id=int(pending_account_id) if pending_account_id else None,
                deduction_account_id=int(deduction_account_id) if deduction_account_id else None,
                paid_account_id=int(paid_account_id) if paid_account_id else None,
                commission_type=commission_type,
                commission_value=commission_value,
                commission_account_id=int(commission_account_id) if commission_account_id else None,
                created_by_id=current_user.id
            )
        else:  # manual
            salary = EmployeeSalary(
                employee_id=None,  # لا نحفظ موظف من النظام للموظف اليدوي
                employee_name=employee_name,
                employee_manual=employee_manual,
                status=status,
                transfer_date=datetime.strptime(transfer_date, '%Y-%m-%dT%H:%M') if transfer_date else None,
                supervisor_id=int(supervisor_id) if supervisor_id else None,
                notes=notes,
                pending_account_id=int(pending_account_id) if pending_account_id else None,
                deduction_account_id=int(deduction_account_id) if deduction_account_id else None,
                paid_account_id=int(paid_account_id) if paid_account_id else None,
                commission_type=commission_type,
                commission_value=commission_value,
                commission_account_id=int(commission_account_id) if commission_account_id else None,
                created_by_id=current_user.id
            )

        db.session.add(salary)
        db.session.flush()  # Get the ID

        # Handle file attachments
        if 'attachments' in request.files:
            files = request.files.getlist('attachments')
            for file in files:
                if file and file.filename:
                    # Save file to uploads directory
                    filename = secure_filename(file.filename)
                    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_')
                    filename = timestamp + filename

                    # Create uploads directory if it doesn't exist
                    upload_dir = os.path.join('app', 'static', 'uploads', 'salary_attachments')
                    os.makedirs(upload_dir, exist_ok=True)

                    file_path = os.path.join(upload_dir, filename)
                    file.save(file_path)

                    # Store file info in salary notes
                    if salary.notes:
                        salary.notes += f"\n\nملف مرفق: {filename}"
                    else:
                        salary.notes = f"ملف مرفق: {filename}"

        # Handle links - تنظيف الروابط وحفظها
        links = request.form.getlist('links[]')
        valid_links = [link.strip() for link in links if link.strip()]
        if valid_links:
            salary.links = '\n'.join(valid_links)
        else:
            salary.links = None  # لا توجد روابط

        # Add salary items
        for item_data in items_data:
            salary_item = SalaryItem(
                salary_id=salary.id,
                project_id=int(item_data['project_id']) if item_data['project_id'] else None,
                task_id=int(item_data['task_id']) if item_data['task_id'] else None,
                amount=item_data['amount'],
                description=item_data['description']
            )
            db.session.add(salary_item)

        # Update account balances if status is not draft
        if status != 'draft':
            salary.update_account_balances()

        db.session.commit()

        flash('تم إضافة الراتب بنجاح', 'success')
        return redirect(url_for('finance.view_salary', id=salary.id))

    # Get data for form
    employees = User.query.filter_by(is_active=True).order_by(User.first_name).all()
    accounts = Account.query.filter_by(is_active=True).order_by(Account.name).all()
    projects_query = Project.query.order_by(Project.name).all()
    tasks_query = Task.query.order_by(Task.title).all()

    # Convert to dictionaries for JSON serialization
    projects = [{'id': p.id, 'name': p.name} for p in projects_query]
    tasks = [{'id': t.id, 'title': t.title, 'project_id': t.project_id} for t in tasks_query]

    return render_template('finance/add_salary.html', title='إضافة راتب جديد',
                          employees=employees, accounts=accounts, projects=projects, tasks=tasks)

@finance_bp.route('/salaries/view/<int:id>')
@login_required
def view_salary(id):
    """عرض تفاصيل الراتب"""
    # Check if user has permission to view salaries
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance')):
        flash('لا تملك صلاحية عرض الرواتب', 'danger')
        return redirect(url_for('finance.salaries'))

    salary = EmployeeSalary.query.get_or_404(id)

    return render_template('finance/view_salary.html', title=f'راتب: {salary.employee_name}',
                          salary=salary)

@finance_bp.route('/salaries/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def edit_salary(id):
    """تعديل راتب"""
    # Check if user has permission to edit salaries
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance')):
        flash('لا تملك صلاحية تعديل الرواتب', 'danger')
        return redirect(url_for('finance.salaries'))

    salary = EmployeeSalary.query.get_or_404(id)
    old_status = salary.status

    if request.method == 'POST':
        employee_type = request.form.get('employee_type', 'system')
        employee_id = request.form.get('employee_id')
        employee_name = request.form.get('employee_name')
        employee_manual = request.form.get('employee_manual')
        status = request.form.get('status', 'draft')
        transfer_date = request.form.get('transfer_date')
        supervisor_id = request.form.get('supervisor_id')
        notes = request.form.get('notes')

        # Account settings
        pending_account_id = request.form.get('pending_account_id')
        deduction_account_id = request.form.get('deduction_account_id')
        paid_account_id = request.form.get('paid_account_id')

        # Commission settings
        commission_type = request.form.get('commission_type', 'percentage')
        commission_value = request.form.get('commission_value', type=float) or 0
        commission_account_id = request.form.get('commission_account_id')

        # Validate employee
        if employee_type == 'system':
            if not employee_id:
                flash('يجب اختيار موظف من النظام', 'danger')
                return redirect(url_for('finance.edit_salary', id=id))
        else:
            if not employee_name:
                flash('اسم الموظف مطلوب', 'danger')
                return redirect(url_for('finance.edit_salary', id=id))

        # Process salary items
        items_data = []
        item_keys = [key for key in request.form.keys() if key.startswith('items[') and key.endswith('][amount]')]

        if not item_keys:
            flash('يجب إضافة بند واحد على الأقل', 'danger')
            return redirect(url_for('finance.edit_salary', id=id))

        total_amount = 0
        for key in item_keys:
            index = key.split('[')[1].split(']')[0]
            amount = request.form.get(f'items[{index}][amount]', type=float)
            project_id = request.form.get(f'items[{index}][project_id]')
            task_id = request.form.get(f'items[{index}][task_id]')
            description = request.form.get(f'items[{index}][description]', '')

            if amount and amount > 0:
                items_data.append({
                    'amount': amount,
                    'project_id': int(project_id) if project_id else None,
                    'task_id': int(task_id) if task_id else None,
                    'description': description
                })
                total_amount += amount

        if total_amount <= 0:
            flash('إجمالي مبلغ البنود يجب أن يكون أكبر من صفر', 'danger')
            return redirect(url_for('finance.edit_salary', id=id))

        # Calculate commission
        commission_amount = 0
        if commission_type == 'percentage':
            commission_amount = total_amount * (commission_value / 100)
        else:
            commission_amount = commission_value

        net_amount = total_amount - commission_amount

        # Update salary
        if employee_type == 'system':
            salary.employee_id = int(employee_id) if employee_id else None
            salary.employee_name = None  # مسح اسم الموظف اليدوي
            salary.employee_manual = None  # مسح تفاصيل الموظف اليدوي
        else:  # manual
            salary.employee_id = None  # مسح موظف النظام
            salary.employee_name = employee_name
            salary.employee_manual = employee_manual
        salary.commission_type = commission_type
        salary.commission_value = commission_value
        salary.commission_account_id = int(commission_account_id) if commission_account_id else None
        salary.status = status
        salary.transfer_date = datetime.strptime(transfer_date, '%Y-%m-%dT%H:%M') if transfer_date else None
        salary.supervisor_id = int(supervisor_id) if supervisor_id else None
        salary.notes = notes
        salary.pending_account_id = int(pending_account_id) if pending_account_id else None
        salary.deduction_account_id = int(deduction_account_id) if deduction_account_id else None
        salary.paid_account_id = int(paid_account_id) if paid_account_id else None
        salary.updated_at = datetime.utcnow()

        # Clear existing items
        from app.models.finance import SalaryItem
        SalaryItem.query.filter_by(salary_id=salary.id).delete()

        # Add new items
        for item_data in items_data:
            item = SalaryItem(
                salary_id=salary.id,
                amount=item_data['amount'],
                project_id=item_data['project_id'],
                task_id=item_data['task_id'],
                description=item_data['description']
            )
            db.session.add(item)

        # Handle links - حذف الروابط القديمة وحفظ الجديدة
        links = request.form.getlist('links[]')
        # تنظيف الروابط وحفظها (حتى لو كانت فارغة لحذف الروابط القديمة)
        valid_links = [link.strip() for link in links if link.strip()]
        if valid_links:
            salary.links = '\n'.join(valid_links)
        else:
            salary.links = None  # حذف جميع الروابط إذا لم تكن هناك روابط صالحة

        # Handle file attachments
        if 'attachments' in request.files:
            files = request.files.getlist('attachments')
            for file in files:
                if file and file.filename:
                    # Save file to uploads directory
                    filename = secure_filename(file.filename)
                    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_')
                    filename = timestamp + filename

                    # Create uploads directory if it doesn't exist
                    upload_dir = os.path.join('app', 'static', 'uploads', 'salary_attachments')
                    os.makedirs(upload_dir, exist_ok=True)

                    file_path = os.path.join(upload_dir, filename)
                    file.save(file_path)

                    # Store file info in salary notes or create a simple attachment record
                    if salary.notes:
                        salary.notes += f"\n\nملف مرفق: {filename}"
                    else:
                        salary.notes = f"ملف مرفق: {filename}"

        # Update account balances if status changed
        if old_status != status:
            salary.update_account_balances(old_status)

        db.session.commit()

        flash('تم تحديث الراتب بنجاح', 'success')
        return redirect(url_for('finance.view_salary', id=id))

    # Get data for form
    employees = User.query.filter_by(is_active=True).order_by(User.first_name).all()
    accounts = Account.query.filter_by(is_active=True).order_by(Account.name).all()
    projects_query = Project.query.order_by(Project.name).all()
    tasks_query = Task.query.order_by(Task.title).all()

    # Convert to dictionaries for JSON serialization
    projects = [{'id': p.id, 'name': p.name} for p in projects_query]
    tasks = [{'id': t.id, 'title': t.title, 'project_id': t.project_id} for t in tasks_query]

    return render_template('finance/edit_salary.html', title='تعديل الراتب',
                          salary=salary, employees=employees, accounts=accounts,
                          projects=projects, tasks=tasks)

@finance_bp.route('/salaries/delete/<int:id>', methods=['GET', 'POST'])
@login_required
def delete_salary(id):
    """حذف راتب"""
    # Check if user has permission to delete salaries
    if not (current_user.has_role('admin') or current_user.has_role('finance')):
        flash('لا تملك صلاحية حذف الرواتب', 'danger')
        return redirect(url_for('finance.salaries'))

    salary = EmployeeSalary.query.get_or_404(id)

    if request.method == 'GET':
        return render_template('finance/delete_salary.html', title='حذف الراتب', salary=salary)

    salary = EmployeeSalary.query.get_or_404(id)

    # إلغاء تأثير الراتب على أرصدة الحسابات
    salary.revert_account_balances()

    db.session.delete(salary)
    db.session.commit()

    flash('تم حذف الراتب بنجاح', 'success')
    return redirect(url_for('finance.salaries'))


# ==================== المعاملات المالية ====================

@finance_bp.route('/financial-transactions')
@login_required
def financial_transactions():
    """صفحة المعاملات المالية"""
    # Check if user has permission to view financial transactions
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance')):
        flash('لا تملك صلاحية الوصول إلى المعاملات المالية', 'danger')
        return redirect(url_for('finance.index'))

    # Get filter parameters
    status_filter = request.args.get('status', '')
    search_filter = request.args.get('search', '')
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 25, type=int)

    # التأكد من أن per_page ضمن القيم المسموحة
    if per_page not in [25, 50, 100, 200]:
        per_page = 25

    # Build query
    query = FinancialTransaction.query

    if status_filter:
        query = query.filter(FinancialTransaction.status == status_filter)

    if search_filter:
        query = query.filter(
            db.or_(
                FinancialTransaction.transaction_number.contains(search_filter),
                FinancialTransaction.transaction_name.contains(search_filter)
            )
        )

    # Get paginated results
    transactions = query.order_by(FinancialTransaction.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )

    return render_template('finance/financial_transactions.html', title='المعاملات المالية',
                          transactions=transactions, status_filter=status_filter,
                          search_filter=search_filter, per_page=per_page)

@finance_bp.route('/financial-transactions/add', methods=['GET', 'POST'])
@login_required
def add_financial_transaction():
    """إضافة معاملة مالية جديدة"""
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance')):
        flash('لا تملك صلاحية إضافة المعاملات المالية', 'danger')
        return redirect(url_for('finance.financial_transactions'))

    if request.method == 'POST':
        transaction_name = request.form.get('transaction_name')
        status = request.form.get('status', 'draft')
        notes = request.form.get('notes')

        # Generate transaction number
        last_transaction = FinancialTransaction.query.order_by(FinancialTransaction.id.desc()).first()
        if last_transaction:
            last_number = int(last_transaction.transaction_number.split('-')[-1])
            transaction_number = f"FT-{last_number + 1:06d}"
        else:
            transaction_number = "FT-000001"

        # Create transaction
        transaction = FinancialTransaction(
            transaction_number=transaction_number,
            transaction_name=transaction_name,
            status=status,
            notes=notes,
            created_by_id=current_user.id
        )

        db.session.add(transaction)
        db.session.flush()  # Get the ID

        # Process transaction items
        item_accounts = request.form.getlist('item_account_id[]')
        item_actions = request.form.getlist('item_action[]')
        item_amounts = request.form.getlist('item_amount[]')
        item_dates = request.form.getlist('item_date[]')
        item_notes = request.form.getlist('item_notes[]')
        item_currencies = request.form.getlist('item_currency_id[]')

        for i in range(len(item_accounts)):
            if item_accounts[i] and item_amounts[i]:
                item_date = datetime.strptime(item_dates[i], '%Y-%m-%dT%H:%M') if item_dates[i] else datetime.utcnow()
                currency_id = int(item_currencies[i]) if item_currencies[i] else 1  # Default to USD

                item = FinancialTransactionItem(
                    account_id=int(item_accounts[i]),
                    action=item_actions[i],
                    amount=float(item_amounts[i]),
                    transaction_date=item_date,
                    item_notes=item_notes[i] if i < len(item_notes) else None,
                    currency_id=currency_id,
                    transaction_id=transaction.id
                )
                db.session.add(item)

        # Update account balances if status is 'paid'
        if status == 'paid':
            transaction.update_account_balances()

        db.session.commit()
        flash('تم إنشاء المعاملة المالية بنجاح', 'success')
        return redirect(url_for('finance.view_financial_transaction', id=transaction.id))

    # Get accounts and currencies for form
    accounts = Account.query.filter_by(is_active=True).order_by(Account.name).all()
    from app.models.currency import Currency
    currencies = Currency.query.filter_by(is_active=True).order_by(Currency.name).all()

    return render_template('finance/add_financial_transaction.html', title='إضافة معاملة مالية جديدة',
                          accounts=accounts, currencies=currencies)

@finance_bp.route('/financial-transactions/view/<int:id>')
@login_required
def view_financial_transaction(id):
    """عرض تفاصيل المعاملة المالية"""
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance')):
        flash('لا تملك صلاحية عرض المعاملات المالية', 'danger')
        return redirect(url_for('finance.financial_transactions'))

    transaction = FinancialTransaction.query.get_or_404(id)

    return render_template('finance/view_financial_transaction.html',
                          title=f'المعاملة المالية: {transaction.transaction_number}',
                          transaction=transaction)

@finance_bp.route('/financial-transactions/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def edit_financial_transaction(id):
    """تعديل المعاملة المالية"""
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance')):
        flash('لا تملك صلاحية تعديل المعاملات المالية', 'danger')
        return redirect(url_for('finance.financial_transactions'))

    transaction = FinancialTransaction.query.get_or_404(id)
    old_status = transaction.status

    if request.method == 'POST':
        transaction.transaction_number = request.form.get('transaction_number')
        transaction.transaction_name = request.form.get('transaction_name')
        transaction.status = request.form.get('status', 'draft')
        transaction.notes = request.form.get('notes')
        transaction.updated_at = datetime.utcnow()

        # Remove existing items
        FinancialTransactionItem.query.filter_by(transaction_id=id).delete()

        # Process new transaction items
        item_accounts = request.form.getlist('item_account_id[]')
        item_actions = request.form.getlist('item_action[]')
        item_amounts = request.form.getlist('item_amount[]')
        item_dates = request.form.getlist('item_date[]')
        item_notes = request.form.getlist('item_notes[]')
        item_currencies = request.form.getlist('item_currency_id[]')

        for i in range(len(item_accounts)):
            if item_accounts[i] and item_amounts[i]:
                item_date = datetime.strptime(item_dates[i], '%Y-%m-%dT%H:%M') if item_dates[i] else datetime.utcnow()
                currency_id = int(item_currencies[i]) if item_currencies[i] else 1  # Default to USD

                item = FinancialTransactionItem(
                    account_id=int(item_accounts[i]),
                    action=item_actions[i],
                    amount=float(item_amounts[i]),
                    transaction_date=item_date,
                    item_notes=item_notes[i] if i < len(item_notes) else None,
                    currency_id=currency_id,
                    transaction_id=transaction.id
                )
                db.session.add(item)

        # Update account balances if status changed
        if old_status != transaction.status:
            transaction.update_account_balances(old_status)

        db.session.commit()
        flash('تم تحديث المعاملة المالية بنجاح', 'success')
        return redirect(url_for('finance.view_financial_transaction', id=id))

    # Get accounts and currencies for form
    accounts = Account.query.filter_by(is_active=True).order_by(Account.name).all()
    from app.models.currency import Currency
    currencies = Currency.query.filter_by(is_active=True).order_by(Currency.name).all()

    return render_template('finance/edit_financial_transaction.html', title='تعديل المعاملة المالية',
                          transaction=transaction, accounts=accounts, currencies=currencies)

@finance_bp.route('/financial-transactions/delete/<int:id>', methods=['GET', 'POST'])
@login_required
def delete_financial_transaction(id):
    """حذف المعاملة المالية"""
    if not (current_user.has_role('admin') or current_user.has_role('finance')):
        flash('لا تملك صلاحية حذف المعاملات المالية', 'danger')
        return redirect(url_for('finance.financial_transactions'))

    transaction = FinancialTransaction.query.get_or_404(id)

    if request.method == 'GET':
        return render_template('finance/delete_financial_transaction.html',
                             title='حذف المعاملة المالية', transaction=transaction)

    # Revert account balances if transaction was paid
    if transaction.status == 'paid':
        transaction.revert_account_balances()

    # Delete transaction and all related items
    FinancialTransactionItem.query.filter_by(transaction_id=id).delete()
    db.session.delete(transaction)
    db.session.commit()

    flash('تم حذف المعاملة المالية بنجاح', 'success')
    return redirect(url_for('finance.financial_transactions'))

@finance_bp.route('/download-salary-attachment/<filename>')
@login_required
def download_salary_attachment(filename):
    """تحميل ملف مرفق بالراتب"""
    try:
        upload_dir = os.path.join('app', 'static', 'uploads', 'salary_attachments')
        return send_file(os.path.join(upload_dir, filename), as_attachment=True)
    except Exception as e:
        flash(f'خطأ في تحميل الملف: {str(e)}', 'danger')
        return redirect(url_for('finance.salaries'))
