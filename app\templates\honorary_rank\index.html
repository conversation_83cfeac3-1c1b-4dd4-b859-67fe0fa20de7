{% extends 'base.html' %}

{% block styles %}
<style>
.animated-gradient {
    background-size: 200% 200% !important;
    animation: gradientShift 3s ease infinite !important;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">الرتب الشرفية</h1>
        <div>
            {% if current_user.has_role('admin') or current_user.has_role('manager') %}
            <a href="{{ url_for('honorary_rank.assign') }}" class="btn btn-success me-2">
                <i class="fas fa-user-tag me-1"></i>تعيين رتبة لموظف
            </a>
            {% endif %}
            
            {% if current_user.has_role('admin') %}
            <a href="{{ url_for('honorary_rank.create') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>إنشاء رتبة جديدة
            </a>
            {% endif %}
        </div>
    </div>

    <div class="row">
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">قائمة الرتب الشرفية</h6>
                </div>
                <div class="card-body">
                    {% if ranks %}
                    <div class="table-responsive">
                        <table class="table table-bordered" id="ranksTable" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>الرتبة</th>
                                    <th>الوصف</th>
                                    <th>اللون</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for rank in ranks %}
                                <tr>
                                    <td>
                                        <span class="badge" style="background-color: {{ rank.color }}; color: {{ '#000' if rank.color == '#ffffff' else '#fff' }}">
                                            <i class="fas {{ rank.icon }} me-1"></i>{{ rank.name }}
                                        </span>
                                    </td>
                                    <td>{{ rank.description }}</td>
                                    <td>
                                        <div class="color-preview" style="width: 20px; height: 20px; background-color: {{ rank.color }}; border-radius: 50%; display: inline-block;"></div>
                                        {{ rank.color }}
                                    </td>
                                    <td>
                                        {% if current_user.has_role('admin') %}
                                        <a href="{{ url_for('honorary_rank.edit', id=rank.id) }}" class="btn btn-sm btn-primary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form action="{{ url_for('honorary_rank.delete', id=rank.id) }}" method="POST" class="d-inline" onsubmit="return confirm('هل أنت متأكد من رغبتك في حذف هذه الرتبة؟');">
                                            <button type="submit" class="btn btn-sm btn-danger">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <p class="text-muted mb-0">لا توجد رتب شرفية حالياً</p>
                        {% if current_user.has_role('admin') %}
                        <a href="{{ url_for('honorary_rank.create') }}" class="btn btn-primary mt-3">
                            <i class="fas fa-plus me-1"></i>إنشاء رتبة جديدة
                        </a>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">الموظفون الحاصلون على رتب شرفية</h6>
                </div>
                <div class="card-body">
                    {% if users_with_ranks %}
                    <div class="table-responsive">
                        <table class="table table-bordered" id="usersTable" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>الموظف</th>
                                    <th>الرتبة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user, rank in users_with_ranks %}
                                <tr>
                                    <td>
                                        {% if user.managed_projects %}
                                        <i class="fas fa-crown text-warning me-1" data-bs-toggle="tooltip" title="مدير مشروع رئيسي"></i>
                                        {% endif %}
                                        {% if user.managed_projects_as_participant %}
                                        <i class="fas fa-crown text-info me-1" data-bs-toggle="tooltip" title="مدير مشارك"></i>
                                        {% endif %}
                                        {% if user.is_currently_on_leave %}
                                        <i class="fas fa-calendar-times text-danger me-1" data-bs-toggle="tooltip" title="في إجازة"></i>
                                        {% endif %}
                                        <a href="{{ url_for('employee.view', id=user.id) }}">{{ user.get_full_name() }}</a>
                                    </td>
                                    <td>
                                        <span class="badge {{ rank.get_css_class() }}" style="{{ rank.get_style() }}">
                                            <i class="fas {{ rank.icon }} me-1"></i>{{ rank.name }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if current_user.has_role('admin') or current_user.has_role('manager') %}
                                        <form action="{{ url_for('honorary_rank.revoke', user_id=user.id, rank_id=rank.id) }}" method="POST" class="d-inline" onsubmit="return confirm('هل أنت متأكد من رغبتك في سحب هذه الرتبة من الموظف؟');">
                                            <button type="submit" class="btn btn-sm btn-danger">
                                                <i class="fas fa-times"></i> سحب
                                            </button>
                                        </form>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <p class="text-muted mb-0">لا يوجد موظفون حاصلون على رتب شرفية حالياً</p>
                        {% if current_user.has_role('admin') or current_user.has_role('manager') %}
                        <a href="{{ url_for('honorary_rank.assign') }}" class="btn btn-success mt-3">
                            <i class="fas fa-user-tag me-1"></i>تعيين رتبة لموظف
                        </a>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        $('#ranksTable, #usersTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json"
            },
            "pageLength": 10
        });
    });
</script>
{% endblock %}
