from flask import Blueprint, render_template, redirect, url_for, flash, request, current_app, send_file, jsonify, Response
from flask_login import login_required, current_user
import os
import shutil
import zipfile
import tempfile
import datetime
import sqlite3
import json
import csv
import io
from werkzeug.utils import secure_filename

from app import db
from app.models.system_config import SystemConfig
from app.models.user import User
from app.models.activity_log import ActivityLog
from app.models.finance import InvoiceAttachment, TransactionAttachment
from app.utils.activity_logger import log_activity

system_bp = Blueprint('system', __name__, url_prefix='/system')

@system_bp.route('/activity-log')
@login_required
def activity_log():
    """Display the activity log with filtering and pagination"""
    # Only admin can access activity log
    if not current_user.has_role('admin'):
        flash('ليس لديك صلاحية لعرض هذه الصفحة', 'danger')
        return redirect(url_for('dashboard.index'))

    # Get query parameters for filtering
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 25, type=int)
    action = request.args.get('action', '')
    entity_type = request.args.get('entity_type', '')
    user_id = request.args.get('user_id', '', type=str)
    start_date = request.args.get('start_date', '')
    end_date = request.args.get('end_date', '')

    # Build the query
    query = ActivityLog.query

    # Apply filters
    if action:
        query = query.filter(ActivityLog.action == action)
    if entity_type:
        query = query.filter(ActivityLog.entity_type == entity_type)
    if user_id and user_id.isdigit():
        query = query.filter(ActivityLog.user_id == int(user_id))

    # Date filtering
    if start_date:
        try:
            start_date_obj = datetime.datetime.strptime(start_date, '%Y-%m-%d')
            query = query.filter(ActivityLog.timestamp >= start_date_obj)
        except ValueError:
            flash('تنسيق تاريخ البداية غير صالح', 'warning')

    if end_date:
        try:
            end_date_obj = datetime.datetime.strptime(end_date, '%Y-%m-%d')
            # Add one day to include the end date
            end_date_obj = end_date_obj + datetime.timedelta(days=1)
            query = query.filter(ActivityLog.timestamp < end_date_obj)
        except ValueError:
            flash('تنسيق تاريخ النهاية غير صالح', 'warning')

    # Order by timestamp (newest first)
    query = query.order_by(ActivityLog.timestamp.desc())

    # Paginate the results
    logs = query.paginate(page=page, per_page=per_page, error_out=False)

    # Get unique values for filters
    actions = db.session.query(ActivityLog.action).distinct().all()
    entity_types = db.session.query(ActivityLog.entity_type).distinct().all()
    users = User.query.all()

    return render_template('system/activity_log.html',
                          title='سجل النشاطات',
                          logs=logs,
                          actions=[a[0] for a in actions],
                          entity_types=[e[0] for e in entity_types],
                          users=users,
                          current_action=action,
                          current_entity_type=entity_type,
                          current_user_id=user_id,
                          current_start_date=start_date,
                          current_end_date=end_date,
                          current_per_page=per_page)

@system_bp.route('/delete-logs', methods=['POST'])
@login_required
def delete_logs():
    """Delete selected activity logs"""
    # Only admin can delete logs
    if not current_user.has_role('admin'):
        flash('ليس لديك صلاحية لحذف سجلات النشاط', 'danger')
        return redirect(url_for('dashboard.index'))

    # Get selected log IDs from form
    selected_logs = request.form.get('selected_logs', '[]')

    try:
        # Parse JSON string to list
        log_ids = json.loads(selected_logs)

        if not log_ids:
            flash('لم يتم تحديد أي سجلات للحذف', 'warning')
            return redirect(url_for('system.activity_log'))

        # Delete selected logs
        deleted_count = ActivityLog.query.filter(ActivityLog.id.in_(log_ids)).delete(synchronize_session=False)
        db.session.commit()

        # Log the deletion
        log_activity(
            action='delete',
            entity_type='activity_log',
            entity_id=0,
            description=f'تم حذف {deleted_count} سجل من سجلات النشاط'
        )

        flash(f'تم حذف {deleted_count} سجل بنجاح', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف السجلات: {str(e)}', 'danger')

    return redirect(url_for('system.activity_log'))

@system_bp.route('/export-activity-log')
@login_required
def export_activity_log():
    """Export activity log to Excel format with proper Arabic support"""
    # Only admin can export activity log
    if not current_user.has_role('admin'):
        flash('ليس لديك صلاحية لتصدير سجل النشاطات', 'danger')
        return redirect(url_for('dashboard.index'))

    # Get query parameters for filtering (same as activity_log route)
    action = request.args.get('action', '')
    entity_type = request.args.get('entity_type', '')
    user_id = request.args.get('user_id', '', type=str)
    start_date = request.args.get('start_date', '')
    end_date = request.args.get('end_date', '')

    # Build the query
    query = ActivityLog.query

    # Apply filters
    if action:
        query = query.filter(ActivityLog.action == action)
    if entity_type:
        query = query.filter(ActivityLog.entity_type == entity_type)
    if user_id and user_id.isdigit():
        query = query.filter(ActivityLog.user_id == int(user_id))

    # Date filtering
    if start_date:
        try:
            start_date_obj = datetime.datetime.strptime(start_date, '%Y-%m-%d')
            query = query.filter(ActivityLog.timestamp >= start_date_obj)
        except ValueError:
            pass

    if end_date:
        try:
            end_date_obj = datetime.datetime.strptime(end_date, '%Y-%m-%d')
            # Add one day to include the end date
            end_date_obj = end_date_obj + datetime.timedelta(days=1)
            query = query.filter(ActivityLog.timestamp < end_date_obj)
        except ValueError:
            pass

    # Order by timestamp (newest first)
    logs = query.order_by(ActivityLog.timestamp.desc()).all()

    # Create Excel file using pandas and xlsxwriter for better Arabic support
    import pandas as pd
    from io import BytesIO

    # Prepare data for Excel
    data = {
        'ID': [],
        'التاريخ والوقت': [],
        'المستخدم': [],
        'الإجراء': [],
        'نوع الكيان': [],
        'معرف الكيان': [],
        'الوصف': [],
        'عنوان IP': []
    }

    # Fill data
    for log in logs:
        user_name = log.user.username if log.user else 'غير مسجل'
        data['ID'].append(log.id)
        data['التاريخ والوقت'].append(log.timestamp.strftime('%Y-%m-%d %H:%M:%S'))
        data['المستخدم'].append(user_name)
        data['الإجراء'].append(log.action)
        data['نوع الكيان'].append(log.entity_type)
        data['معرف الكيان'].append(log.entity_id)
        data['الوصف'].append(log.description)
        data['عنوان IP'].append(log.ip_address or 'غير معروف')

    # Create DataFrame
    df = pd.DataFrame(data)

    # Create Excel file in memory
    output = BytesIO()

    # Use xlsxwriter engine for better RTL and Arabic support
    with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
        df.to_excel(writer, sheet_name='سجل النشاطات', index=False)

        # Get workbook and worksheet objects
        workbook = writer.book
        worksheet = writer.sheets['سجل النشاطات']

        # Set RTL direction for the worksheet
        worksheet.right_to_left()

        # Add formats
        header_format = workbook.add_format({
            'bold': True,
            'text_wrap': True,
            'valign': 'top',
            'fg_color': '#D7E4BC',
            'border': 1,
            'align': 'right'
        })

        # Set column widths
        worksheet.set_column('A:A', 8)  # ID
        worksheet.set_column('B:B', 20)  # Date/Time
        worksheet.set_column('C:C', 15)  # User
        worksheet.set_column('D:D', 12)  # Action
        worksheet.set_column('E:E', 15)  # Entity Type
        worksheet.set_column('F:F', 10)  # Entity ID
        worksheet.set_column('G:G', 40)  # Description
        worksheet.set_column('H:H', 15)  # IP Address

        # Write headers with format
        for col_num, value in enumerate(df.columns.values):
            worksheet.write(0, col_num, value, header_format)

    # Prepare the response
    output.seek(0)
    timestamp = datetime.datetime.now().strftime('%Y%m%d%H%M%S')

    return send_file(
        BytesIO(output.getvalue()),
        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        as_attachment=True,
        download_name=f'activity_log_{timestamp}.xlsx'
    )

@system_bp.route('/config')
@login_required
def config():
    # Only admin can access system configuration
    if not current_user.has_role('admin'):
        flash('ليس لديك صلاحية لعرض هذه الصفحة', 'danger')
        return redirect(url_for('dashboard.index'))

    maintenance_mode = SystemConfig.is_maintenance_mode()
    announcement_enabled = SystemConfig.is_announcement_bar_enabled()
    announcement_text = SystemConfig.get_announcement_text()
    announcement_bg_color = SystemConfig.get_announcement_bg_color()
    announcement_text_color = SystemConfig.get_announcement_text_color()
    announcement_animated = SystemConfig.is_announcement_animated()

    # Check if backup/restore logs exist
    backup_log_exists = os.path.exists(os.path.join(current_app.root_path, 'static', 'uploads', 'backup_log.txt'))
    restore_log_exists = os.path.exists(os.path.join(current_app.root_path, 'static', 'uploads', 'restore_log.txt'))
    restore_error_log_exists = os.path.exists(os.path.join(current_app.root_path, 'static', 'uploads', 'restore_error_log.txt'))

    # Get activity log count
    activity_log_count = ActivityLog.query.count()

    return render_template('system/config.html', title='إعدادات النظام',
                          maintenance_mode=maintenance_mode,
                          announcement_enabled=announcement_enabled,
                          announcement_text=announcement_text,
                          announcement_bg_color=announcement_bg_color,
                          announcement_text_color=announcement_text_color,
                          announcement_animated=announcement_animated,
                          backup_log_exists=backup_log_exists,
                          restore_log_exists=restore_log_exists,
                          restore_error_log_exists=restore_error_log_exists,
                          activity_log_count=activity_log_count)

@system_bp.route('/toggle-maintenance', methods=['POST'])
@login_required
def toggle_maintenance():
    # Only admin can toggle maintenance mode
    if not current_user.has_role('admin'):
        flash('ليس لديك صلاحية للقيام بهذا الإجراء', 'danger')
        return redirect(url_for('dashboard.index'))

    new_mode = SystemConfig.toggle_maintenance_mode()

    if new_mode:
        flash('تم تفعيل وضع الصيانة. فقط المسؤولون يمكنهم تسجيل الدخول.', 'warning')
    else:
        flash('تم إلغاء وضع الصيانة. يمكن للجميع تسجيل الدخول الآن.', 'success')

    return redirect(url_for('system.config'))

@system_bp.route('/toggle-announcement', methods=['POST'])
@login_required
def toggle_announcement():
    # Only admin can toggle announcement bar
    if not current_user.has_role('admin'):
        flash('ليس لديك صلاحية للقيام بهذا الإجراء', 'danger')
        return redirect(url_for('dashboard.index'))

    new_state = SystemConfig.toggle_announcement_bar()

    if new_state:
        flash('تم تفعيل شريط الإعلانات.', 'success')
    else:
        flash('تم إلغاء تفعيل شريط الإعلانات.', 'info')

    return redirect(url_for('system.config'))

@system_bp.route('/update-announcement', methods=['POST'])
@login_required
def update_announcement():
    # Only admin can update announcement settings
    if not current_user.has_role('admin'):
        flash('ليس لديك صلاحية للقيام بهذا الإجراء', 'danger')
        return redirect(url_for('dashboard.index'))

    announcement_text = request.form.get('announcement_text', '')
    announcement_bg_color = request.form.get('announcement_bg_color', '#EABF54')
    announcement_text_color = request.form.get('announcement_text_color', '#343a40')
    announcement_animated = request.form.get('announcement_animated') == 'on'

    SystemConfig.set_announcement_text(announcement_text)
    SystemConfig.set_announcement_bg_color(announcement_bg_color)
    SystemConfig.set_announcement_text_color(announcement_text_color)
    SystemConfig.set_announcement_animated(announcement_animated)

    flash('تم تحديث إعدادات شريط الإعلانات بنجاح.', 'success')
    return redirect(url_for('system.config'))

@system_bp.route('/view-log/<log_type>')
@login_required
def view_log(log_type):
    # Only admin can view logs
    if not current_user.has_role('admin'):
        flash('ليس لديك صلاحية للقيام بهذا الإجراء', 'danger')
        return redirect(url_for('dashboard.index'))

    log_file_path = None
    log_title = ""

    if log_type == 'backup':
        log_file_path = os.path.join(current_app.root_path, 'static', 'uploads', 'backup_log.txt')
        log_title = "سجل النسخ الاحتياطي"
    elif log_type == 'restore':
        log_file_path = os.path.join(current_app.root_path, 'static', 'uploads', 'restore_log.txt')
        log_title = "سجل استعادة النسخة الاحتياطية"
    elif log_type == 'restore_error':
        log_file_path = os.path.join(current_app.root_path, 'static', 'uploads', 'restore_error_log.txt')
        log_title = "سجل أخطاء استعادة النسخة الاحتياطية"
    else:
        flash('نوع السجل غير صالح', 'danger')
        return redirect(url_for('system.config'))

    if not os.path.exists(log_file_path):
        flash('ملف السجل غير موجود', 'warning')
        return redirect(url_for('system.config'))

    try:
        with open(log_file_path, 'r', encoding='utf-8') as f:
            log_content = f.read()
    except Exception as e:
        flash(f'حدث خطأ أثناء قراءة ملف السجل: {str(e)}', 'danger')
        return redirect(url_for('system.config'))

    return render_template('system/view_log.html', title=log_title, log_content=log_content, log_type=log_type)

@system_bp.route('/backup')
@login_required
def backup():
    # Only admin can create backups
    if not current_user.has_role('admin'):
        flash('ليس لديك صلاحية للقيام بهذا الإجراء', 'danger')
        return redirect(url_for('dashboard.index'))

    # Log the backup activity
    log_activity(
        action='backup',
        entity_type='system',
        entity_id=0,
        description='قام المستخدم بإنشاء نسخة احتياطية للنظام'
    )

    # Create a temporary directory
    temp_dir = tempfile.mkdtemp()

    # Create a log file to track the backup process
    log_file_path = os.path.join(temp_dir, 'backup_log.txt')
    log_file = open(log_file_path, 'w', encoding='utf-8')

    try:
        # Get the current timestamp for the backup filename
        timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_filename = f'sparkle_backup_{timestamp}.zip'
        backup_path = os.path.join(temp_dir, backup_filename)

        log_file.write(f"=== بدء عملية النسخ الاحتياطي {timestamp} ===\n\n")

        # Create a zip file
        with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # 1. Backup the database
            db_path = os.path.join(current_app.root_path, 'sparkle.db')
            if os.path.exists(db_path):
                # Create a copy of the database to avoid "file in use" errors
                db_copy_path = os.path.join(temp_dir, 'sparkle_copy.db')
                shutil.copy2(db_path, db_copy_path)
                zipf.write(db_copy_path, 'database/sparkle.db')
                log_file.write(f"✓ تم نسخ قاعدة البيانات بنجاح\n")

                # Verify database contains image paths and check table statistics
                conn = sqlite3.connect(db_copy_path)
                cursor = conn.cursor()

                # Get all tables in the database
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cursor.fetchall()]
                log_file.write(f"  - عدد الجداول في قاعدة البيانات: {len(tables)}\n")
                log_file.write(f"  - الجداول الموجودة: {', '.join(tables)}\n\n")

                # Check statistics for each important table
                table_stats = [
                    ("users", "المستخدمين"),
                    ("departments", "الأقسام"),
                    ("projects", "المشاريع"),
                    ("tasks", "المهام"),
                    ("clients", "العملاء"),
                    ("invoices", "الفواتير"),
                    ("transactions", "المعاملات المالية"),
                    ("notifications", "الإشعارات"),
                    ("leave_requests", "طلبات الإجازة"),
                    ("meetings", "الاجتماعات"),
                    ("honorary_ranks", "الرتب الشرفية"),
                    ("penalties", "العقوبات"),
                    ("activity_logs", "سجلات النشاط"),
                    ("mail_messages", "الرسائل الداخلية")
                ]

                log_file.write("إحصائيات الجداول:\n")
                for table, label in table_stats:
                    if table in tables:
                        try:
                            cursor.execute(f"SELECT COUNT(*) FROM {table}")
                            count = cursor.fetchone()[0]
                            log_file.write(f"  - عدد {label}: {count}\n")
                        except sqlite3.OperationalError:
                            log_file.write(f"  - لا يمكن الوصول إلى جدول {label}\n")
                    else:
                        log_file.write(f"  - جدول {label} غير موجود\n")

                log_file.write("\nإحصائيات الملفات المرفقة:\n")

                # Check user profile images (both file paths and binary data)
                cursor.execute("SELECT COUNT(*) FROM users WHERE profile_image IS NOT NULL AND profile_image != 'default.jpg'")
                user_images_file_count = cursor.fetchone()[0]

                cursor.execute("SELECT COUNT(*) FROM users WHERE profile_image_data IS NOT NULL")
                user_images_data_count = cursor.fetchone()[0]

                log_file.write(f"  - عدد صور الموظفين (ملفات): {user_images_file_count}\n")
                log_file.write(f"  - عدد صور الموظفين (بيانات ثنائية): {user_images_data_count}\n")

                # Check client profile images (both file paths and binary data)
                cursor.execute("SELECT COUNT(*) FROM clients WHERE profile_image IS NOT NULL AND profile_image != 'default.jpg'")
                client_images_file_count = cursor.fetchone()[0]

                cursor.execute("SELECT COUNT(*) FROM clients WHERE profile_image_data IS NOT NULL")
                client_images_data_count = cursor.fetchone()[0]

                log_file.write(f"  - عدد صور العملاء (ملفات): {client_images_file_count}\n")
                log_file.write(f"  - عدد صور العملاء (بيانات ثنائية): {client_images_data_count}\n")

                # Check meeting attachments
                if "meeting_attachments" in tables:
                    cursor.execute("SELECT COUNT(*) FROM meeting_attachments")
                    meeting_attachments_count = cursor.fetchone()[0]
                    log_file.write(f"  - عدد مرفقات الاجتماعات: {meeting_attachments_count}\n")

                # Check leave attachments
                if "leave_attachments" in tables:
                    cursor.execute("SELECT COUNT(*) FROM leave_attachments")
                    leave_attachments_count = cursor.fetchone()[0]
                    log_file.write(f"  - عدد مرفقات طلبات الإجازة: {leave_attachments_count}\n")

                # Check penalty attachments
                if "penalty_attachments" in tables:
                    cursor.execute("SELECT COUNT(*) FROM penalty_attachments")
                    penalty_attachments_count = cursor.fetchone()[0]
                    log_file.write(f"  - عدد مرفقات العقوبات: {penalty_attachments_count}\n")

                # Check mail attachments
                if "mail_attachments" in tables:
                    cursor.execute("SELECT COUNT(*) FROM mail_attachments")
                    mail_attachments_count = cursor.fetchone()[0]
                    log_file.write(f"  - عدد مرفقات الرسائل الداخلية: {mail_attachments_count}\n")

                conn.close()
            else:
                log_file.write("✗ لم يتم العثور على ملف قاعدة البيانات\n")

            # 2. Backup uploaded files
            uploads_dir = os.path.join(current_app.root_path, 'static', 'uploads')
            if os.path.exists(uploads_dir):
                # Count files before backup
                total_files = 0
                backed_up_files = 0
                skipped_files = 0

                # Count image files specifically
                profile_images = 0
                client_images = 0
                leave_attachments = 0
                penalty_attachments = 0
                meeting_attachments = 0
                mail_attachments = 0
                id_documents = 0

                for root, dirs, files in os.walk(uploads_dir):
                    for file in files:
                        total_files += 1
                        try:
                            file_path = os.path.join(root, file)
                            arcname = os.path.relpath(file_path, current_app.root_path)
                            zipf.write(file_path, arcname)
                            backed_up_files += 1

                            # Count specific file types
                            if 'profile_images' in root:
                                profile_images += 1
                            elif 'client_images' in root:
                                client_images += 1
                            elif 'leave' in root:
                                leave_attachments += 1
                            elif 'penalties' in root:
                                penalty_attachments += 1
                            elif 'meeting_attachments' in root:
                                meeting_attachments += 1
                            elif 'mail_attachments' in root:
                                mail_attachments += 1
                            elif 'id_documents' in root:
                                id_documents += 1

                        except PermissionError:
                            # Skip files that are currently in use
                            skipped_files += 1
                            log_file.write(f"  ✗ تم تخطي الملف (قيد الاستخدام): {os.path.relpath(file_path, current_app.root_path)}\n")
                            continue

                log_file.write(f"✓ تم نسخ الملفات المرفوعة بنجاح\n")
                log_file.write(f"  - إجمالي الملفات: {total_files}\n")
                log_file.write(f"  - الملفات المنسوخة: {backed_up_files}\n")
                log_file.write(f"  - الملفات المتخطاة: {skipped_files}\n")
                log_file.write(f"  - صور الموظفين: {profile_images}\n")
                log_file.write(f"  - صور العملاء: {client_images}\n")
                log_file.write(f"  - مرفقات الإجازات: {leave_attachments}\n")
                log_file.write(f"  - مرفقات العقوبات: {penalty_attachments}\n")
                log_file.write(f"  - مرفقات الاجتماعات: {meeting_attachments}\n")
                log_file.write(f"  - مرفقات البريد الداخلي: {mail_attachments}\n")
                log_file.write(f"  - مرفقات الإثباتات الوطنية: {id_documents}\n")
            else:
                log_file.write("✗ لم يتم العثور على مجلد الملفات المرفوعة\n")

            # 3. Backup system configuration
            config_data = {}
            for config in SystemConfig.query.all():
                config_data[config.key] = config.value

            config_file = os.path.join(temp_dir, 'system_config.json')
            with open(config_file, 'w') as f:
                json.dump(config_data, f, indent=4)

            zipf.write(config_file, 'config/system_config.json')
            log_file.write(f"✓ تم نسخ إعدادات النظام بنجاح\n")

            # Add the log file to the backup
            log_file.write(f"\n=== اكتملت عملية النسخ الاحتياطي بنجاح ===\n")
            log_file.close()
            zipf.write(log_file_path, 'backup_log.txt')

        # Create a response with the file
        response = send_file(backup_path, as_attachment=True, download_name=backup_filename)

        # Set a callback to clean up the temporary directory after the response is sent
        @response.call_on_close
        def cleanup():
            try:
                if os.path.exists(temp_dir):
                    shutil.rmtree(temp_dir)
            except Exception:
                # If we can't delete the temp directory now, it will be cleaned up later
                pass

        return response

    except Exception as e:
        # Log the error
        log_file.write(f"\n=== حدث خطأ أثناء عملية النسخ الاحتياطي ===\n")
        log_file.write(f"الخطأ: {str(e)}\n")
        log_file.close()

        # Clean up in case of error
        try:
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
        except Exception:
            pass

        flash(f'حدث خطأ أثناء إنشاء النسخة الاحتياطية: {str(e)}', 'danger')
        return redirect(url_for('system.config'))

@system_bp.route('/files', methods=['GET'])
@login_required
def manage_files():
    """
    صفحة إدارة الملفات المرفوعة
    تعرض جميع الملفات المرفوعة وتسمح بحذفها
    """
    # Only admin can manage files
    if not current_user.has_role('admin'):
        flash('ليس لديك صلاحية للقيام بهذا الإجراء', 'danger')
        return redirect(url_for('dashboard.index'))

    # Get database attachments
    invoice_attachments_db = InvoiceAttachment.query.all()
    transaction_attachments_db = TransactionAttachment.query.all()

    # Add database attachments to file categories
    file_categories = {
        'profile_images': {'title': 'صور الموظفين', 'files': [], 'total_size': 0},
        'client_images': {'title': 'صور العملاء', 'files': [], 'total_size': 0},
        'meeting_attachments': {'title': 'مرفقات الاجتماعات', 'files': [], 'total_size': 0},
        'mail_attachments': {'title': 'مرفقات البريد الداخلي', 'files': [], 'total_size': 0},
        'leave_attachments': {'title': 'مرفقات الإجازات', 'files': [], 'total_size': 0},
        'penalty_attachments': {'title': 'مرفقات العقوبات', 'files': [], 'total_size': 0},
        'id_documents': {'title': 'مرفقات الإثباتات الوطنية', 'files': [], 'total_size': 0},
        'invoice_attachments': {'title': 'مرفقات الفواتير', 'files': [], 'total_size': 0, 'db_items': invoice_attachments_db},
        'transaction_attachments': {'title': 'مرفقات المعاملات المالية', 'files': [], 'total_size': 0, 'db_items': transaction_attachments_db},
        'salary_attachments': {'title': 'مرفقات الرواتب', 'files': [], 'total_size': 0},
        'other': {'title': 'ملفات أخرى', 'files': [], 'total_size': 0}
    }

    # Get all uploaded files
    uploads_dir = os.path.join(current_app.root_path, 'static', 'uploads')



    # Total statistics
    total_files = 0
    total_size = 0

    # Scan all files in uploads directory
    if os.path.exists(uploads_dir):
        for root, dirs, files in os.walk(uploads_dir):
            for file in files:
                file_path = os.path.join(root, file)
                rel_path = os.path.relpath(file_path, uploads_dir)

                # Get file size
                file_size = os.path.getsize(file_path)
                total_size += file_size

                # Format file size
                if file_size < 1024:
                    size_str = f"{file_size} بايت"
                elif file_size < 1024 * 1024:
                    size_str = f"{file_size / 1024:.1f} كيلوبايت"
                else:
                    size_str = f"{file_size / (1024 * 1024):.1f} ميجابايت"

                # Get file modification time
                mod_time = datetime.datetime.fromtimestamp(os.path.getmtime(file_path))
                mod_time_str = mod_time.strftime('%Y-%m-%d %H:%M:%S')

                # Create file info
                file_info = {
                    'name': file,
                    'path': rel_path,
                    'size': file_size,
                    'size_str': size_str,
                    'modified': mod_time_str
                }

                # Categorize file
                category = 'other'
                if 'profile_images' in rel_path:
                    category = 'profile_images'
                elif 'client_images' in rel_path:
                    category = 'client_images'
                elif 'meeting_attachments' in rel_path:
                    category = 'meeting_attachments'
                elif 'mail_attachments' in rel_path:
                    category = 'mail_attachments'
                elif 'leave' in rel_path:
                    category = 'leave_attachments'
                elif 'penalties' in rel_path:
                    category = 'penalty_attachments'
                elif 'id_documents' in rel_path:
                    category = 'id_documents'
                elif 'invoices' in rel_path:
                    category = 'invoice_attachments'
                elif 'transactions' in rel_path:
                    category = 'transaction_attachments'
                elif 'salary_attachments' in rel_path:
                    category = 'salary_attachments'

                # Add file to category
                file_categories[category]['files'].append(file_info)
                file_categories[category]['total_size'] += file_size
                total_files += 1

    # Format total size
    if total_size < 1024:
        total_size_str = f"{total_size} بايت"
    elif total_size < 1024 * 1024:
        total_size_str = f"{total_size / 1024:.1f} كيلوبايت"
    else:
        total_size_str = f"{total_size / (1024 * 1024):.1f} ميجابايت"

    # Sort files by size (largest first)
    for category in file_categories:
        file_categories[category]['files'].sort(key=lambda x: x['size'], reverse=True)

        # Format category total size
        cat_size = file_categories[category]['total_size']
        if cat_size < 1024:
            cat_size_str = f"{cat_size} بايت"
        elif cat_size < 1024 * 1024:
            cat_size_str = f"{cat_size / 1024:.1f} كيلوبايت"
        else:
            cat_size_str = f"{cat_size / (1024 * 1024):.1f} ميجابايت"

        file_categories[category]['total_size_str'] = cat_size_str

    return render_template('system/manage_files.html',
                          title='إدارة الملفات المرفوعة',
                          file_categories=file_categories,
                          total_files=total_files,
                          total_size_str=total_size_str)

@system_bp.route('/files/confirm-delete', methods=['POST'])
@login_required
def confirm_delete_files():
    """
    صفحة تأكيد حذف الملفات المرفوعة
    """
    # Only admin can delete files
    if not current_user.has_role('admin'):
        flash('ليس لديك صلاحية للقيام بهذا الإجراء', 'danger')
        return redirect(url_for('dashboard.index'))

    # Get files to delete
    files = request.form.getlist('files')

    if not files:
        flash('لم يتم تحديد أي ملفات للحذف', 'warning')
        return redirect(url_for('system.manage_files'))

    # Get file information
    uploads_dir = os.path.join(current_app.root_path, 'static', 'uploads')
    file_info_list = []
    total_size = 0

    for file_path in files:
        # Ensure the file path is within the uploads directory
        full_path = os.path.join(uploads_dir, file_path)
        if os.path.commonpath([uploads_dir]) == os.path.commonpath([uploads_dir, full_path]) and os.path.exists(full_path):
            # Get file size
            file_size = os.path.getsize(full_path)
            total_size += file_size

            # Format file size
            if file_size < 1024:
                size_str = f"{file_size} بايت"
            elif file_size < 1024 * 1024:
                size_str = f"{file_size / 1024:.1f} كيلوبايت"
            else:
                size_str = f"{file_size / (1024 * 1024):.1f} ميجابايت"

            # Add file info
            file_info = {
                'name': os.path.basename(file_path),
                'path': file_path,
                'size': file_size,
                'size_str': size_str
            }
            file_info_list.append(file_info)

    # Format total size
    if total_size < 1024:
        total_size_str = f"{total_size} بايت"
    elif total_size < 1024 * 1024:
        total_size_str = f"{total_size / 1024:.1f} كيلوبايت"
    else:
        total_size_str = f"{total_size / (1024 * 1024):.1f} ميجابايت"

    return render_template('system/confirm_delete_files.html',
                          title='تأكيد حذف الملفات',
                          files=file_info_list,
                          total_size_str=total_size_str)

@system_bp.route('/files/delete', methods=['POST'])
@login_required
def delete_files():
    """
    حذف الملفات المرفوعة
    """
    # Only admin can delete files
    if not current_user.has_role('admin'):
        flash('ليس لديك صلاحية للقيام بهذا الإجراء', 'danger')
        return redirect(url_for('dashboard.index'))

    # Get files to delete
    files = request.form.getlist('files')

    if not files:
        flash('لم يتم تحديد أي ملفات للحذف', 'warning')
        return redirect(url_for('system.manage_files'))

    # Delete files
    uploads_dir = os.path.join(current_app.root_path, 'static', 'uploads')
    deleted_count = 0
    error_count = 0

    for file_path in files:
        # Ensure the file path is within the uploads directory
        full_path = os.path.join(uploads_dir, file_path)
        if os.path.commonpath([uploads_dir]) == os.path.commonpath([uploads_dir, full_path]):
            try:
                if os.path.exists(full_path):
                    os.remove(full_path)
                    deleted_count += 1
            except Exception as e:
                error_count += 1
                print(f"Error deleting file {file_path}: {str(e)}")

    # Log activity
    log_activity(
        action='delete_files',
        entity_type='system',
        entity_id=0,
        description=f'قام المستخدم بحذف {deleted_count} ملف من الملفات المرفوعة'
    )

    if error_count > 0:
        flash(f'تم حذف {deleted_count} ملف بنجاح، وفشل حذف {error_count} ملف', 'warning')
    else:
        flash(f'تم حذف {deleted_count} ملف بنجاح', 'success')

    return redirect(url_for('system.manage_files'))

@system_bp.route('/restore', methods=['POST'])
@login_required
def restore():
    # Only admin can restore backups
    if not current_user.has_role('admin'):
        flash('ليس لديك صلاحية للقيام بهذا الإجراء', 'danger')
        return redirect(url_for('dashboard.index'))

    # Log the restore activity
    log_activity(
        action='restore',
        entity_type='system',
        entity_id=0,
        description='قام المستخدم بمحاولة استعادة النظام من نسخة احتياطية'
    )

    # Check if a file was uploaded
    if 'backup_file' not in request.files:
        error_msg = 'لم يتم تحديد ملف النسخة الاحتياطية'
        restore_log = ["✗ " + error_msg]

        # Log validation failure
        log_activity(
            action='restore_failed',
            entity_type='system',
            entity_id=0,
            description='فشلت عملية استعادة النظام: ' + error_msg
        )

        # Render error page
        return render_template('system/restore_error.html',
                              title='خطأ في استعادة النسخة الاحتياطية',
                              error_message=error_msg,
                              log_content="\n".join(restore_log))

    backup_file = request.files['backup_file']

    # Check if the file is empty
    if backup_file.filename == '':
        error_msg = 'لم يتم تحديد ملف النسخة الاحتياطية'
        restore_log = ["✗ " + error_msg]

        # Log validation failure
        log_activity(
            action='restore_failed',
            entity_type='system',
            entity_id=0,
            description='فشلت عملية استعادة النظام: ' + error_msg
        )

        # Render error page
        return render_template('system/restore_error.html',
                              title='خطأ في استعادة النسخة الاحتياطية',
                              error_message=error_msg,
                              log_content="\n".join(restore_log))

    # Create a temporary directory to store the file
    temp_dir = tempfile.mkdtemp()
    backup_path = os.path.join(temp_dir, secure_filename(backup_file.filename))

    # Save the uploaded file
    backup_file.save(backup_path)

    # Get file size in a human-readable format
    file_size = os.path.getsize(backup_path)
    if file_size < 1024:
        filesize = f"{file_size} بايت"
    elif file_size < 1024 * 1024:
        filesize = f"{file_size / 1024:.1f} كيلوبايت"
    else:
        filesize = f"{file_size / (1024 * 1024):.1f} ميجابايت"

    # Show confirmation page before proceeding with restore
    return render_template('system/restore_confirm.html',
                          title='تأكيد استعادة النسخة الاحتياطية',
                          filename=backup_file.filename,
                          filesize=filesize,
                          temp_file_path=backup_path)




















