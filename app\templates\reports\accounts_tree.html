{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
        <a href="{{ url_for('report.index') }}" class="d-none d-sm-inline-block btn btn-sm btn-primary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> العودة للتقارير
        </a>
    </div>

    <!-- تقرير شجرة الحسابات -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">خيارات التقرير</h6>
        </div>
        <div class="card-body">
            <form action="{{ url_for('report.export', report_type='accounts_tree') }}" method="POST" id="exportForm">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label for="folder_id" class="form-label">مجلد الحسابات</label>
                        <select class="form-select" id="folder_id" name="folder_id">
                            <option value="all" selected>جميع المجلدات</option>
                            {% for folder in folders %}
                            <option value="{{ folder.id }}">{{ folder.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="date_from" class="form-label">من تاريخ</label>
                        <input type="date" class="form-control" id="date_from" name="date_from" value="{{ date_from }}">
                        <small class="text-muted">فلترة الأرصدة من هذا التاريخ</small>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="date_to" class="form-label">إلى تاريخ</label>
                        <input type="date" class="form-control" id="date_to" name="date_to" value="{{ date_to }}">
                        <small class="text-muted">فلترة الأرصدة حتى هذا التاريخ</small>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="include_balances" class="form-label">تضمين الأرصدة</label>
                        <select class="form-select" id="include_balances" name="include_balances">
                            <option value="yes" {% if include_balances == 'yes' %}selected{% endif %}>نعم</option>
                            <option value="no" {% if include_balances == 'no' %}selected{% endif %}>لا</option>
                        </select>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="export_format" class="form-label">تنسيق التصدير</label>
                        <select class="form-select" id="export_format" name="export_format">
                            <option value="pdf" selected>PDF</option>
                            <option value="excel">Excel</option>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-file-export me-1"></i>تصدير التقرير
                            </button>
                            <a href="{{ url_for('report.accounts_tree') }}" class="btn btn-secondary">
                                <i class="fas fa-redo me-1"></i>إعادة تعيين
                            </a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- معاينة البيانات -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">شجرة الحسابات</h6>
        </div>
        <div class="card-body">
            {% if folders %}
            <div class="row">
                {% for folder in folders %}
                <div class="col-md-6 mb-4">
                    <div class="card border-left-primary">
                        <div class="card-header bg-primary text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-folder me-2"></i>{{ folder.name }}
                            </h6>
                        </div>
                        <div class="card-body">
                            {% if folder.accounts %}
                            <div class="table-responsive">
                                <table class="table table-sm table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>اسم الحساب</th>
                                            <th>نوع الحساب</th>
                                            <th>الرصيد الحالي</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for account in folder.accounts %}
                                        <tr>
                                            <td>
                                                <i class="fas fa-coins me-2 text-warning"></i>
                                                {{ account.name }}
                                            </td>
                                            <td>
                                                {% if account.account_type == 'asset' %}
                                                <span class="badge bg-success">أصل</span>
                                                {% elif account.account_type == 'liability' %}
                                                <span class="badge bg-warning">التزام</span>
                                                {% elif account.account_type == 'equity' %}
                                                <span class="badge bg-info">حقوق ملكية</span>
                                                {% elif account.account_type == 'revenue' %}
                                                <span class="badge bg-primary">إيراد</span>
                                                {% elif account.account_type == 'expense' %}
                                                <span class="badge bg-danger">مصروف</span>
                                                {% else %}
                                                <span class="badge bg-secondary">{{ account.account_type }}</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if date_from or date_to %}
                                                    {% set balance = account.filtered_balance if account.filtered_balance is defined else account.balance %}
                                                {% else %}
                                                    {% set balance = account.balance %}
                                                {% endif %}
                                                {% if balance >= 0 %}
                                                <span class="text-success">${{ "%.2f"|format(balance) }}</span>
                                                {% else %}
                                                <span class="text-danger">${{ "%.2f"|format(balance) }}</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% else %}
                            <p class="text-muted mb-0">لا توجد حسابات في هذا المجلد</p>
                            {% endif %}
                        </div>
                        <div class="card-footer">
                            <small class="text-muted">
                                عدد الحسابات: {{ folder.accounts|length }}
                                {% if folder.accounts %}
                                | إجمالي الأرصدة: ${{ "%.2f"|format(folder.accounts|sum(attribute='balance')) }}
                                {% endif %}
                            </small>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            
            <!-- ملخص إجمالي -->
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card border-left-info">
                        <div class="card-header bg-info text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-chart-pie me-2"></i>ملخص إجمالي
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h5 class="text-success">{{ total_accounts }}</h5>
                                        <small class="text-muted">إجمالي الحسابات</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h5 class="text-primary">{{ folders|length }}</h5>
                                        <small class="text-muted">عدد المجلدات</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h5 class="text-info">${{ "%.2f"|format(total_balance) }}</h5>
                                        <small class="text-muted">
                                            {% if date_from or date_to %}
                                                إجمالي الأرصدة (مفلترة)
                                            {% else %}
                                                إجمالي الأرصدة
                                            {% endif %}
                                        </small>
                                    </div>
                                </div>
                                {% if date_from or date_to %}
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h6 class="text-warning">
                                            {% if date_from and date_to %}
                                                {{ date_from }} إلى {{ date_to }}
                                            {% elif date_from %}
                                                من {{ date_from }}
                                            {% elif date_to %}
                                                حتى {{ date_to }}
                                            {% endif %}
                                        </h6>
                                        <small class="text-muted">فترة التقرير</small>
                                    </div>
                                </div>
                                {% endif %}
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h5 class="text-warning">{{ active_accounts }}</h5>
                                        <small class="text-muted">الحسابات النشطة</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% else %}
            <div class="text-center py-4">
                <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                <p class="text-muted mb-0">لا توجد مجلدات حسابات لعرضها</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // Initialize select2
    $('#folder_id, #include_balances, #export_format').select2({
        theme: 'bootstrap-5',
        width: '100%',
        dir: 'rtl'
    });

    // Handle filter changes to update the page
    $('#date_from, #date_to, #include_balances').on('change', function() {
        updateFilters();
    });

    function updateFilters() {
        const url = new URL(window.location);
        url.searchParams.set('date_from', $('#date_from').val());
        url.searchParams.set('date_to', $('#date_to').val());
        url.searchParams.set('include_balances', $('#include_balances').val());
        window.location.href = url.toString();
    }
});
</script>
{% endblock %}
