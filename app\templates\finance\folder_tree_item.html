<div class="folder-section">
    <div class="folder-header" style="background: linear-gradient(135deg, {{ folder.color }}, {{ folder.color }}dd);
                                     color: white; padding: 15px; border-left: 5px solid {{ folder.color }};
                                     border-radius: 10px; margin-bottom: 10px;
                                     box-shadow: 0 4px 8px rgba(0,0,0,0.15);
                                     border: 1px solid rgba(255,255,255,0.1);">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <i class="{{ folder.icon }} me-2 fa-lg"></i>
                <strong class="fs-6">{{ folder.name }}</strong>
                <span class="badge bg-light text-dark ms-2">{{ folder.accounts|length }}</span>
                {% if folder.description %}
                <div class="small mt-1 opacity-90">{{ folder.description }}</div>
                {% endif %}
            </div>
            <button class="btn btn-sm btn-outline-light toggle-folder" data-target="folder-{{ folder.id }}"
                    style="border: 1px solid rgba(255,255,255,0.4); backdrop-filter: blur(10px);">
                <i class="fas {% if folder.is_expanded %}fa-minus{% else %}fa-plus{% endif %}"></i>
            </button>
        </div>
    </div>
    
    <div class="folder-content {% if not folder.is_expanded %}d-none{% endif %}" id="folder-{{ folder.id }}">
        {% if folder.accounts %}
            {% for account in folder.accounts %}
                {% include 'finance/account_tree_item.html' %}
            {% endfor %}
        {% else %}
            <div class="text-center py-3 text-muted">
                <i class="fas fa-folder-open fa-2x mb-2"></i>
                <div>لا توجد حسابات في هذا المجلد</div>
            </div>
        {% endif %}
    </div>
</div>
