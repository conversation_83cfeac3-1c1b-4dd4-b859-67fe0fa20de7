{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
        <a href="{{ url_for('finance.view_salary', id=salary.id) }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i>العودة إلى تفاصيل الراتب
        </a>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>تعديل الراتب: {{ salary.employee_name }}
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" id="salaryForm">
                        <!-- إعدادات الحسابات -->
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">إعدادات الحسابات</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="pending_account_id" class="form-label">حساب المال المعلق</label>
                                            <select class="form-select" id="pending_account_id" name="pending_account_id">
                                                <option value="">اختر الحساب</option>
                                                {% for account in accounts %}
                                                <option value="{{ account.id }}" {% if salary.pending_account_id == account.id %}selected{% endif %}>{{ account.name }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="deduction_account_id" class="form-label">حساب الخصم</label>
                                            <select class="form-select" id="deduction_account_id" name="deduction_account_id">
                                                <option value="">اختر الحساب</option>
                                                {% for account in accounts %}
                                                <option value="{{ account.id }}" {% if salary.deduction_account_id == account.id %}selected{% endif %}>{{ account.name }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="paid_account_id" class="form-label">حساب الرواتب المدفوعة</label>
                                            <select class="form-select" id="paid_account_id" name="paid_account_id">
                                                <option value="">اختر الحساب</option>
                                                {% for account in accounts %}
                                                <option value="{{ account.id }}" {% if salary.paid_account_id == account.id %}selected{% endif %}>{{ account.name }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- بيانات الراتب -->
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">بيانات الراتب</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="employee_type" class="form-label">نوع الموظف</label>
                                            <select class="form-select" id="employee_type" name="employee_type" onchange="toggleEmployeeFields()">
                                                <option value="system" {% if salary.employee_id %}selected{% endif %}>موظف من النظام</option>
                                                <option value="manual" {% if not salary.employee_id %}selected{% endif %}>موظف يدوي</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">إجمالي المبلغ</label>
                                            <div class="input-group">
                                                <input type="text" class="form-control" id="total_amount" readonly value="{{ salary.total_amount }}">
                                                <span class="input-group-text">$</span>
                                            </div>
                                            <div class="form-text">يتم حسابه تلقائياً من البنود</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6" id="system_employee_field" {% if not salary.employee_id %}style="display: none;"{% endif %}>
                                        <div class="mb-3">
                                            <label for="employee_id" class="form-label">الموظف من النظام</label>
                                            <select class="form-select" id="employee_id" name="employee_id">
                                                <option value="">اختر الموظف</option>
                                                {% for employee in employees %}
                                                <option value="{{ employee.id }}" {% if salary.employee_id == employee.id %}selected{% endif %}>
                                                    {{ employee.first_name }} {{ employee.last_name }} ({{ employee.username }})
                                                </option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6" id="manual_employee_fields" {% if salary.employee_id %}style="display: none;"{% endif %}>
                                        <div class="mb-3">
                                            <label for="employee_name" class="form-label">اسم الموظف</label>
                                            <input type="text" class="form-control" id="employee_name" name="employee_name" value="{{ salary.employee_name or '' }}">
                                        </div>
                                        <div class="mb-3">
                                            <label for="employee_manual" class="form-label">تفاصيل إضافية</label>
                                            <input type="text" class="form-control" id="employee_manual" name="employee_manual"
                                                   placeholder="مثال: شركة أخرى" value="{{ salary.employee_manual or '' }}">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="status" class="form-label">حالة الراتب</label>
                                            <select class="form-select" id="status" name="status">
                                                <option value="draft" {% if salary.status == 'draft' %}selected{% endif %}>مسودة</option>
                                                <option value="pending" {% if salary.status == 'pending' %}selected{% endif %}>معلق</option>
                                                <option value="paid" {% if salary.status == 'paid' %}selected{% endif %}>مدفوع</option>
                                                <option value="cancelled" {% if salary.status == 'cancelled' %}selected{% endif %}>ملغي</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="transfer_date" class="form-label">موعد الحوالة (اختياري)</label>
                                            <input type="datetime-local" class="form-control" id="transfer_date" name="transfer_date"
                                                   value="{% if salary.transfer_date %}{{ salary.transfer_date.strftime('%Y-%m-%dT%H:%M') }}{% endif %}">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="supervisor_id" class="form-label">الموظف المشرف للحوالة</label>
                                            <select class="form-select" id="supervisor_id" name="supervisor_id">
                                                <option value="">اختر المشرف</option>
                                                {% for employee in employees %}
                                                <option value="{{ employee.id }}" {% if salary.supervisor_id == employee.id %}selected{% endif %}>
                                                    {{ employee.first_name }} {{ employee.last_name }}
                                                </option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="notes" class="form-label">ملاحظات</label>
                                    <textarea class="form-control" id="notes" name="notes" rows="3"
                                              placeholder="أي ملاحظات إضافية...">{{ salary.notes or '' }}</textarea>
                                </div>
                            </div>
                        </div>

                        <!-- بنود الراتب -->
                        <div class="card mb-4">
                            <div class="card-header bg-primary text-white">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0">بنود الراتب <span class="text-warning">*</span></h6>
                                    <button type="button" class="btn btn-sm btn-outline-light" onclick="addSalaryItem()">
                                        <i class="fas fa-plus me-1"></i>إضافة بند
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div id="salary-items-container">
                                    {% for item in salary.items %}
                                    <!-- البند {{ loop.index }} -->
                                    <div class="salary-item border rounded p-3 mb-3" data-index="{{ loop.index0 }}">
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <h6 class="mb-0">البند #{{ loop.index }}</h6>
                                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeSalaryItem({{ loop.index0 }})" {% if loop.length == 1 %}style="display: none;"{% endif %}>
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-4">
                                                <div class="mb-3">
                                                    <label class="form-label">المشروع (اختياري)</label>
                                                    <div class="position-relative">
                                                        <input type="text" class="form-control project-search" placeholder="ابحث عن مشروع أو انقر للعرض..." data-index="{{ loop.index0 }}" value="{% if item.project %}{{ item.project.name }}{% endif %}">
                                                        <div class="search-results project-results d-none" data-index="{{ loop.index0 }}"></div>
                                                    </div>
                                                    <select class="form-select project-select d-none" name="items[{{ loop.index0 }}][project_id]" onchange="updateTaskOptions({{ loop.index0 }})">
                                                        <option value="">اختر مشروع</option>
                                                        {% for project in projects %}
                                                        <option value="{{ project.id }}" {% if item.project_id == project.id %}selected{% endif %}>{{ project.name }}</option>
                                                        {% endfor %}
                                                    </select>
                                                    <div class="selected-project mt-2" data-index="{{ loop.index0 }}">
                                                        {% if item.project %}
                                                        <span class="badge bg-primary">
                                                            {{ item.project.name }}
                                                            <button type="button" class="btn-close btn-close-white ms-1" onclick="clearProject({{ loop.index0 }})"></button>
                                                        </span>
                                                        {% endif %}
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="mb-3">
                                                    <label class="form-label">المهمة (اختياري)</label>
                                                    <div class="position-relative">
                                                        <input type="text" class="form-control task-search" placeholder="ابحث عن مهمة..." data-index="{{ loop.index0 }}" {% if not item.project %}disabled{% endif %} value="{% if item.task %}{{ item.task.title }}{% endif %}">
                                                        <div class="search-results task-results d-none" data-index="{{ loop.index0 }}"></div>
                                                    </div>
                                                    <select class="form-select task-select d-none" name="items[{{ loop.index0 }}][task_id]" {% if not item.project %}disabled{% endif %}>
                                                        <option value="">اختر مهمة</option>
                                                        {% if item.project %}
                                                            {% for task in tasks %}
                                                                {% if task.project_id == item.project_id %}
                                                                <option value="{{ task.id }}" {% if item.task_id == task.id %}selected{% endif %}>{{ task.title }}</option>
                                                                {% endif %}
                                                            {% endfor %}
                                                        {% endif %}
                                                    </select>
                                                    <div class="selected-task mt-2" data-index="{{ loop.index0 }}">
                                                        {% if item.task %}
                                                        <span class="badge bg-success">
                                                            {{ item.task.title }}
                                                            <button type="button" class="btn-close btn-close-white ms-1" onclick="clearTask({{ loop.index0 }})"></button>
                                                        </span>
                                                        {% endif %}
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="mb-3">
                                                    <label class="form-label">المبلغ <span class="text-danger">*</span></label>
                                                    <div class="input-group">
                                                        <input type="number" class="form-control item-amount" name="items[{{ loop.index0 }}][amount]" 
                                                               step="0.01" min="0" required onchange="calculateTotal()" value="{{ item.amount }}">
                                                        <span class="input-group-text">$</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">وصف البند</label>
                                            <textarea class="form-control" name="items[{{ loop.index0 }}][description]" rows="2" 
                                                      placeholder="وصف تفصيلي للبند...">{{ item.description or '' }}</textarea>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                                
                                <!-- ملخص المبالغ -->
                                <div class="row mt-4">
                                    <div class="col-md-6 offset-md-6">
                                        <div class="card bg-light">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between mb-2">
                                                    <span>إجمالي البنود:</span>
                                                    <span id="items-total">${{ "{:,.2f}".format(salary.total_amount) }}</span>
                                                </div>
                                                <div class="d-flex justify-content-between mb-2">
                                                    <span>عمولة الشركة:</span>
                                                    <span id="commission-amount">${{ "{:,.2f}".format(salary.commission_amount) }}</span>
                                                </div>
                                                <hr>
                                                <div class="d-flex justify-content-between">
                                                    <strong>المبلغ الصافي:</strong>
                                                    <strong id="net-amount">${{ "{:,.2f}".format(salary.net_amount) }}</strong>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- عمولة الشركة -->
                        <div class="card mb-4">
                            <div class="card-header bg-warning text-dark">
                                <h6 class="mb-0">عمولة الشركة</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="commission_type" class="form-label">نوع العمولة</label>
                                            <select class="form-select" id="commission_type" name="commission_type" onchange="calculateTotal()">
                                                <option value="percentage" {% if salary.commission_type == 'percentage' %}selected{% endif %}>نسبة مئوية</option>
                                                <option value="fixed" {% if salary.commission_type == 'fixed' %}selected{% endif %}>مبلغ ثابت</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="commission_value" class="form-label">قيمة العمولة</label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" id="commission_value" name="commission_value" 
                                                       step="0.01" min="0" value="{{ salary.commission_value }}" onchange="calculateTotal()">
                                                <span class="input-group-text" id="commission-unit">{% if salary.commission_type == 'percentage' %}%{% else %}${% endif %}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="commission_account_id" class="form-label">حساب عمولة الشركة</label>
                                            <select class="form-select" id="commission_account_id" name="commission_account_id">
                                                <option value="">اختر حساب</option>
                                                {% for account in accounts %}
                                                <option value="{{ account.id }}" {% if salary.commission_account_id == account.id %}selected{% endif %}>{{ account.name }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>



                        <!-- الروابط والمرفقات -->
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">الروابط والمرفقات (اختياري)</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">الروابط</label>
                                    <div id="links-container">
                                        {% if salary.links %}
                                            {% set link_lines = salary.links.split('\n') %}
                                            {% for link in link_lines %}
                                                {% if link.strip() %}
                                                <div class="input-group mb-2">
                                                    <input type="url" class="form-control" name="links[]" placeholder="https://example.com" value="{{ link.strip() }}">
                                                    <button type="button" class="btn btn-outline-danger" onclick="removeLinkField(this)">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                                {% endif %}
                                            {% endfor %}
                                            <div class="input-group mb-2">
                                                <input type="url" class="form-control" name="links[]" placeholder="https://example.com">
                                                <button type="button" class="btn btn-outline-success" onclick="addLinkField()">
                                                    <i class="fas fa-plus"></i>
                                                </button>
                                            </div>
                                        {% else %}
                                        <div class="input-group mb-2">
                                            <input type="url" class="form-control" name="links[]" placeholder="https://example.com">
                                            <button type="button" class="btn btn-outline-success" onclick="addLinkField()">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </div>
                                        {% endif %}
                                    </div>
                                    <div class="d-flex gap-2">
                                        <button type="button" class="btn btn-sm btn-outline-success" onclick="addLinkField()">
                                            <i class="fas fa-plus me-1"></i>إضافة رابط
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="clearAllLinks()">
                                            <i class="fas fa-trash me-1"></i>حذف جميع الروابط
                                        </button>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="attachments" class="form-label">المرفقات</label>
                                    <input type="file" class="form-control" id="attachments" name="attachments" multiple
                                           accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png,.gif">
                                    <div class="form-text">يمكنك اختيار ملفات متعددة (PDF, Word, Excel, صور)</div>

                                    {% if salary.attachments %}
                                    <div class="mt-3">
                                        <h6>المرفقات الحالية:</h6>
                                        {% for attachment in salary.attachments %}
                                        <div class="d-flex justify-content-between align-items-center border rounded p-2 mb-2">
                                            <span>{{ attachment.filename }}</span>
                                            <div>
                                                <a href="{{ url_for('finance.download_salary_attachment', id=attachment.id) }}" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-download"></i>
                                                </a>
                                                <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeAttachment({{ attachment.id }})">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- أزرار الحفظ -->
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('finance.salaries') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-save me-1"></i>حفظ التغييرات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// متغيرات عامة
let itemIndex = {{ salary.items|length }};
const projects = {{ projects|tojson }};
const tasks = {{ tasks|tojson }};

// إضافة بند جديد
function addSalaryItem() {
    const container = document.getElementById('salary-items-container');
    const newItem = document.createElement('div');
    newItem.className = 'salary-item border rounded p-3 mb-3';
    newItem.setAttribute('data-index', itemIndex);

    newItem.innerHTML = `
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h6 class="mb-0">البند #${itemIndex + 1}</h6>
            <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeSalaryItem(${itemIndex})">
                <i class="fas fa-trash"></i>
            </button>
        </div>
        <div class="row">
            <div class="col-md-4">
                <div class="mb-3">
                    <label class="form-label">المشروع (اختياري)</label>
                    <div class="position-relative">
                        <input type="text" class="form-control project-search" placeholder="ابحث عن مشروع أو انقر للعرض..." data-index="${itemIndex}">
                        <div class="search-results project-results d-none" data-index="${itemIndex}"></div>
                    </div>
                    <select class="form-select project-select d-none" name="items[${itemIndex}][project_id]" onchange="updateTaskOptions(${itemIndex})">
                        <option value="">اختر مشروع</option>
                        ${projects.map(project => `<option value="${project.id}">${project.name}</option>`).join('')}
                    </select>
                    <div class="selected-project mt-2" data-index="${itemIndex}"></div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="mb-3">
                    <label class="form-label">المهمة (اختياري)</label>
                    <div class="position-relative">
                        <input type="text" class="form-control task-search" placeholder="ابحث عن مهمة..." data-index="${itemIndex}" disabled>
                        <div class="search-results task-results d-none" data-index="${itemIndex}"></div>
                    </div>
                    <select class="form-select task-select d-none" name="items[${itemIndex}][task_id]" disabled>
                        <option value="">اختر مهمة</option>
                    </select>
                    <div class="selected-task mt-2" data-index="${itemIndex}"></div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="mb-3">
                    <label class="form-label">المبلغ <span class="text-danger">*</span></label>
                    <div class="input-group">
                        <input type="number" class="form-control item-amount" name="items[${itemIndex}][amount]"
                               step="0.01" min="0" required onchange="calculateTotal()">
                        <span class="input-group-text">$</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="mb-3">
            <label class="form-label">وصف البند</label>
            <textarea class="form-control" name="items[${itemIndex}][description]" rows="2"
                      placeholder="وصف تفصيلي للبند..."></textarea>
        </div>
    `;

    container.appendChild(newItem);
    itemIndex++;
    updateRemoveButtons();

    // إضافة مستمعي الأحداث للبحث في البند الجديد
    setupSearchListeners(itemIndex - 1);
}

// حذف بند
function removeSalaryItem(index) {
    const item = document.querySelector(`[data-index="${index}"]`);
    if (item) {
        item.remove();
        calculateTotal();
        updateRemoveButtons();
    }
}

// تحديث أزرار الحذف
function updateRemoveButtons() {
    const items = document.querySelectorAll('.salary-item');
    items.forEach((item, index) => {
        const removeBtn = item.querySelector('.btn-outline-danger');
        if (items.length > 1) {
            removeBtn.style.display = 'inline-block';
        } else {
            removeBtn.style.display = 'none';
        }
    });
}

// تحديث خيارات المهام بناءً على المشروع المختار
function updateTaskOptions(itemIndex) {
    const projectSelect = document.querySelector(`[name="items[${itemIndex}][project_id]"]`);
    const taskSelect = document.querySelector(`[name="items[${itemIndex}][task_id]"]`);
    const taskSearch = document.querySelector(`[data-index="${itemIndex}"].task-search`);
    const projectId = parseInt(projectSelect.value);

    taskSelect.innerHTML = '<option value="">اختر مهمة</option>';

    if (projectId) {
        const projectTasks = tasks.filter(task => task.project_id === projectId);
        projectTasks.forEach(task => {
            const option = document.createElement('option');
            option.value = task.id;
            option.textContent = task.title;
            taskSelect.appendChild(option);
        });
        taskSelect.disabled = false;
        if (taskSearch) {
            taskSearch.disabled = false;
        }
    } else {
        taskSelect.disabled = true;
        if (taskSearch) {
            taskSearch.disabled = true;
            taskSearch.value = '';
        }
    }
}

// حساب الإجماليات
function calculateTotal() {
    const amounts = document.querySelectorAll('.item-amount');
    let total = 0;

    amounts.forEach(input => {
        const value = parseFloat(input.value) || 0;
        total += value;
    });

    const commissionType = document.getElementById('commission_type').value;
    const commissionValue = parseFloat(document.getElementById('commission_value').value) || 0;
    let commissionAmount = 0;

    if (commissionType === 'percentage') {
        commissionAmount = total * (commissionValue / 100);
        document.getElementById('commission-unit').textContent = '%';
    } else {
        commissionAmount = commissionValue;
        document.getElementById('commission-unit').textContent = '$';
    }

    const netAmount = total - commissionAmount;

    document.getElementById('items-total').textContent = `$${total.toFixed(2)}`;
    document.getElementById('commission-amount').textContent = `$${commissionAmount.toFixed(2)}`;
    document.getElementById('net-amount').textContent = `$${netAmount.toFixed(2)}`;
    document.getElementById('total_amount').value = netAmount.toFixed(2);
}

function toggleEmployeeFields() {
    const employeeType = document.getElementById('employee_type').value;
    const systemField = document.getElementById('system_employee_field');
    const manualFields = document.getElementById('manual_employee_fields');
    const employeeIdField = document.getElementById('employee_id');
    const employeeNameField = document.getElementById('employee_name');

    if (employeeType === 'system') {
        systemField.style.display = 'block';
        manualFields.style.display = 'none';
        employeeIdField.required = true;
        employeeNameField.required = false;
    } else {
        systemField.style.display = 'none';
        manualFields.style.display = 'block';
        employeeIdField.required = false;
        employeeNameField.required = true;
    }
}

// إعداد مستمعي أحداث البحث
function setupSearchListeners(index) {
    const projectSearch = document.querySelector(`[data-index="${index}"].project-search`);
    const taskSearch = document.querySelector(`[data-index="${index}"].task-search`);
    const projectResults = document.querySelector(`[data-index="${index}"].project-results`);
    const taskResults = document.querySelector(`[data-index="${index}"].task-results`);

    // البحث في المشاريع
    projectSearch.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase().trim();
        if (searchTerm.length === 0) {
            projectResults.classList.add('d-none');
            return;
        }

        const filteredProjects = projects.filter(project =>
            project.name.toLowerCase().includes(searchTerm)
        );

        displayProjectResults(filteredProjects, index);
    });

    // عرض جميع المشاريع عند التركيز
    projectSearch.addEventListener('focus', function() {
        if (this.value.trim() === '') {
            displayProjectResults(projects, index);
        }
    });

    // البحث في المهام
    taskSearch.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase().trim();
        if (searchTerm.length === 0) {
            taskResults.classList.add('d-none');
            return;
        }

        const selectedProjectId = getSelectedProjectId(index);
        let filteredTasks = tasks;

        if (selectedProjectId) {
            filteredTasks = tasks.filter(task => task.project_id === selectedProjectId);
        }

        filteredTasks = filteredTasks.filter(task =>
            task.title.toLowerCase().includes(searchTerm)
        );

        displayTaskResults(filteredTasks, index);
    });

    // عرض المهام المتاحة عند التركيز
    taskSearch.addEventListener('focus', function() {
        if (this.value.trim() === '') {
            const selectedProjectId = getSelectedProjectId(index);
            let availableTasks = tasks;

            if (selectedProjectId) {
                availableTasks = tasks.filter(task => task.project_id === selectedProjectId);
            }

            displayTaskResults(availableTasks, index);
        }
    });
}

// عرض نتائج البحث في المشاريع
function displayProjectResults(projects, index) {
    const resultsContainer = document.querySelector(`[data-index="${index}"].project-results`);

    if (projects.length === 0) {
        resultsContainer.classList.add('d-none');
        return;
    }

    resultsContainer.innerHTML = projects.map(project => `
        <div class="search-result-item" onclick="selectProject(${project.id}, '${project.name}', ${index})">
            ${project.name}
        </div>
    `).join('');

    resultsContainer.classList.remove('d-none');
}

// عرض نتائج البحث في المهام
function displayTaskResults(tasks, index) {
    const resultsContainer = document.querySelector(`[data-index="${index}"].task-results`);

    if (tasks.length === 0) {
        resultsContainer.classList.add('d-none');
        return;
    }

    resultsContainer.innerHTML = tasks.map(task => `
        <div class="search-result-item" onclick="selectTask(${task.id}, '${task.title}', ${index})">
            ${task.title}
        </div>
    `).join('');

    resultsContainer.classList.remove('d-none');
}

// اختيار مشروع
function selectProject(projectId, projectName, index) {
    const projectSelect = document.querySelector(`[name="items[${index}][project_id]"]`);
    const projectSearch = document.querySelector(`[data-index="${index}"].project-search`);
    const projectResults = document.querySelector(`[data-index="${index}"].project-results`);
    const selectedProjectDiv = document.querySelector(`[data-index="${index}"].selected-project`);
    const taskSearch = document.querySelector(`[data-index="${index}"].task-search`);

    projectSelect.value = projectId;
    projectSearch.value = projectName;
    projectResults.classList.add('d-none');

    selectedProjectDiv.innerHTML = `
        <span class="badge bg-primary">
            ${projectName}
            <button type="button" class="btn-close btn-close-white ms-1" onclick="clearProject(${index})"></button>
        </span>
    `;

    // تفعيل البحث في المهام
    taskSearch.disabled = false;
    taskSearch.placeholder = 'ابحث عن مهمة...';

    // مسح المهمة المختارة سابقاً
    clearTask(index);

    // تحديث خيارات المهام
    updateTaskOptions(index);
}

// اختيار مهمة
function selectTask(taskId, taskTitle, index) {
    const taskSelect = document.querySelector(`[name="items[${index}][task_id]"]`);
    const taskSearch = document.querySelector(`[data-index="${index}"].task-search`);
    const taskResults = document.querySelector(`[data-index="${index}"].task-results`);
    const selectedTaskDiv = document.querySelector(`[data-index="${index}"].selected-task`);

    taskSelect.value = taskId;
    taskSearch.value = taskTitle;
    taskResults.classList.add('d-none');

    selectedTaskDiv.innerHTML = `
        <span class="badge bg-success">
            ${taskTitle}
            <button type="button" class="btn-close btn-close-white ms-1" onclick="clearTask(${index})"></button>
        </span>
    `;
}

// مسح المشروع المختار
function clearProject(index) {
    const projectSelect = document.querySelector(`[name="items[${index}][project_id]"]`);
    const selectedProjectDiv = document.querySelector(`[data-index="${index}"].selected-project`);
    const taskSearch = document.querySelector(`[data-index="${index}"].task-search`);

    projectSelect.value = '';
    selectedProjectDiv.innerHTML = '';
    taskSearch.disabled = true;
    taskSearch.placeholder = 'اختر مشروع أولاً...';

    clearTask(index);
    updateTaskOptions(index);
}

// مسح المهمة المختارة
function clearTask(index) {
    const taskSelect = document.querySelector(`[name="items[${index}][task_id]"]`);
    const selectedTaskDiv = document.querySelector(`[data-index="${index}"].selected-task`);

    taskSelect.value = '';
    selectedTaskDiv.innerHTML = '';
}

// الحصول على معرف المشروع المختار
function getSelectedProjectId(index) {
    const projectSelect = document.querySelector(`[name="items[${index}][project_id]"]`);
    return parseInt(projectSelect.value) || null;
}

function addLinkField() {
    const container = document.getElementById('links-container');
    const newField = document.createElement('div');
    newField.className = 'input-group mb-2';
    newField.innerHTML = `
        <input type="url" class="form-control" name="links[]" placeholder="https://example.com">
        <button type="button" class="btn btn-outline-danger" onclick="removeLinkField(this)">
            <i class="fas fa-trash"></i>
        </button>
    `;
    container.appendChild(newField);
}

function removeLinkField(button) {
    const container = document.getElementById('links-container');
    // السماح بحذف جميع الحقول
    button.parentElement.remove();

    // إذا لم تعد هناك حقول، أضف حقل فارغ واحد
    if (container.children.length === 0) {
        addLinkField();
    }
}

function clearAllLinks() {
    if (confirm('هل أنت متأكد من حذف جميع الروابط؟')) {
        const container = document.getElementById('links-container');
        container.innerHTML = '';
        addLinkField(); // إضافة حقل فارغ واحد
    }
}

function removeAttachment(attachmentId) {
    if (confirm('هل أنت متأكد من حذف هذا المرفق؟')) {
        // إضافة حقل مخفي لحذف المرفق
        const form = document.getElementById('salaryForm');
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'remove_attachments[]';
        input.value = attachmentId;
        form.appendChild(input);

        // إخفاء المرفق من الواجهة
        event.target.closest('.d-flex').style.display = 'none';
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // إعداد البحث للبنود الموجودة
    {% for item in salary.items %}
    setupSearchListeners({{ loop.index0 }});
    {% endfor %}

    // تحديث نوع العمولة
    document.getElementById('commission_type').addEventListener('change', function() {
        const unit = document.getElementById('commission-unit');
        if (this.value === 'percentage') {
            unit.textContent = '%';
        } else {
            unit.textContent = '$';
        }
        calculateTotal();
    });

    // إخفاء نتائج البحث عند النقر خارجها
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.position-relative')) {
            document.querySelectorAll('.search-results').forEach(result => {
                result.classList.add('d-none');
            });
        }
    });

    // حساب الإجمالي عند تحميل الصفحة
    calculateTotal();
});
</script>

<style>
.search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.search-result-item {
    padding: 8px 12px;
    cursor: pointer;
    border-bottom: 1px solid #eee;
}

.search-result-item:hover {
    background-color: #f8f9fa;
}

.search-result-item:last-child {
    border-bottom: none;
}

.selected-project .badge,
.selected-task .badge {
    font-size: 0.9em;
    padding: 6px 10px;
}

.btn-close-white {
    filter: invert(1);
}
</style>
{% endblock %}
