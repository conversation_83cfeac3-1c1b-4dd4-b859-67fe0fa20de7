{% extends 'base.html' %}

{% block styles %}
<style>
.animated-gradient {
    background-size: 200% 200% !important;
    animation: gradientShift 3s ease infinite !important;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">تعديل الرتبة الشرفية</h1>
        <a href="{{ url_for('honorary_rank.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i>العودة إلى القائمة
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">تفاصيل الرتبة الشرفية</h6>
        </div>
        <div class="card-body">
            <form method="POST" action="{{ url_for('honorary_rank.edit', id=rank.id) }}">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="name" class="form-label">اسم الرتبة <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="name" name="name" value="{{ rank.name }}" required>
                    </div>

                    <div class="col-md-12 mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_premium" name="is_premium"
                                   {% if rank.is_premium %}checked{% endif %} onchange="togglePremiumOptions()">
                            <label class="form-check-label" for="is_premium">
                                رتبة مميزة (تدرج لوني)
                            </label>
                        </div>
                    </div>

                    <div id="basic-color-section" class="col-md-6 mb-3" {% if rank.is_premium %}style="display: none;"{% endif %}>
                        <label for="color" class="form-label">لون الرتبة</label>
                        <input type="color" class="form-control form-control-color w-100" id="color" name="color" value="{{ rank.color }}">
                        <div class="form-text">اختر لوناً للرتبة</div>
                    </div>

                    <div id="premium-color-section" {% if not rank.is_premium %}style="display: none;"{% endif %}>
                        <div class="col-md-6 mb-3">
                            <label for="color_start" class="form-label">اللون الأول</label>
                            <input type="color" class="form-control form-control-color w-100" id="color_start" name="color_start"
                                   value="{{ rank.color_start or '#007bff' }}">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="color_end" class="form-label">اللون الثاني</label>
                            <input type="color" class="form-control form-control-color w-100" id="color_end" name="color_end"
                                   value="{{ rank.color_end or '#28a745' }}">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="gradient_direction" class="form-label">اتجاه التدرج</label>
                            <select class="form-select" id="gradient_direction" name="gradient_direction">
                                <option value="to right" {% if rank.gradient_direction == 'to right' %}selected{% endif %}>من اليسار إلى اليمين</option>
                                <option value="to left" {% if rank.gradient_direction == 'to left' %}selected{% endif %}>من اليمين إلى اليسار</option>
                                <option value="to bottom" {% if rank.gradient_direction == 'to bottom' %}selected{% endif %}>من الأعلى إلى الأسفل</option>
                                <option value="to top" {% if rank.gradient_direction == 'to top' %}selected{% endif %}>من الأسفل إلى الأعلى</option>
                                <option value="45deg" {% if rank.gradient_direction == '45deg' %}selected{% endif %}>قطري (45 درجة)</option>
                                <option value="135deg" {% if rank.gradient_direction == '135deg' %}selected{% endif %}>قطري عكسي (135 درجة)</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="animated_gradient" name="animated_gradient"
                                       {% if rank.animated_gradient %}checked{% endif %}>
                                <label class="form-check-label" for="animated_gradient">
                                    تأثير متحرك (تغيير اللون باستمرار)
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-12 mb-3">
                        <label for="description" class="form-label">وصف الرتبة</label>
                        <textarea class="form-control" id="description" name="description" rows="3">{{ rank.description }}</textarea>
                    </div>

                    <div class="col-md-12 mb-3">
                        <label for="icon" class="form-label">أيقونة الرتبة</label>
                        <select class="form-select" id="icon" name="icon">
                            <option value="fa-award" {% if rank.icon == 'fa-award' %}selected{% endif %}>جائزة (Award)</option>
                            <option value="fa-medal" {% if rank.icon == 'fa-medal' %}selected{% endif %}>ميدالية (Medal)</option>
                            <option value="fa-trophy" {% if rank.icon == 'fa-trophy' %}selected{% endif %}>كأس (Trophy)</option>
                            <option value="fa-star" {% if rank.icon == 'fa-star' %}selected{% endif %}>نجمة (Star)</option>
                            <option value="fa-certificate" {% if rank.icon == 'fa-certificate' %}selected{% endif %}>شهادة (Certificate)</option>
                            <option value="fa-crown" {% if rank.icon == 'fa-crown' %}selected{% endif %}>تاج (Crown)</option>
                            <option value="fa-gem" {% if rank.icon == 'fa-gem' %}selected{% endif %}>جوهرة (Gem)</option>
                            <option value="fa-shield-alt" {% if rank.icon == 'fa-shield-alt' %}selected{% endif %}>درع (Shield)</option>
                            <option value="fa-bookmark" {% if rank.icon == 'fa-bookmark' %}selected{% endif %}>علامة مرجعية (Bookmark)</option>
                            <option value="fa-heart" {% if rank.icon == 'fa-heart' %}selected{% endif %}>قلب (Heart)</option>
                            <option value="fa-thumbs-up" {% if rank.icon == 'fa-thumbs-up' %}selected{% endif %}>إعجاب (Thumbs Up)</option>
                            <option value="fa-microphone" {% if rank.icon == 'fa-microphone' %}selected{% endif %}>مايك (Microphone)</option>
                            <option value="fa-book" {% if rank.icon == 'fa-book' %}selected{% endif %}>دفتر (Book)</option>
                            <option value="fa-file-excel" {% if rank.icon == 'fa-file-excel' %}selected{% endif %}>إكسل (Excel)</option>
                            <option value="fa-palette" {% if rank.icon == 'fa-palette' %}selected{% endif %}>فوتوشوب (Photoshop)</option>
                            <option value="fa-camera" {% if rank.icon == 'fa-camera' %}selected{% endif %}>كاميرا (Camera)</option>
                            <option value="fa-video" {% if rank.icon == 'fa-video' %}selected{% endif %}>فيديو (Video)</option>
                            <option value="fa-music" {% if rank.icon == 'fa-music' %}selected{% endif %}>موسيقى (Music)</option>
                            <option value="fa-paint-brush" {% if rank.icon == 'fa-paint-brush' %}selected{% endif %}>فرشاة (Paint Brush)</option>
                            <option value="fa-code" {% if rank.icon == 'fa-code' %}selected{% endif %}>كود (Code)</option>
                            <option value="fa-laptop" {% if rank.icon == 'fa-laptop' %}selected{% endif %}>لابتوب (Laptop)</option>
                            <option value="fa-mobile-alt" {% if rank.icon == 'fa-mobile-alt' %}selected{% endif %}>موبايل (Mobile)</option>
                            <option value="fa-rocket" {% if rank.icon == 'fa-rocket' %}selected{% endif %}>صاروخ (Rocket)</option>
                            <option value="fa-lightbulb" {% if rank.icon == 'fa-lightbulb' %}selected{% endif %}>لمبة (Light Bulb)</option>
                            <option value="fa-graduation-cap" {% if rank.icon == 'fa-graduation-cap' %}selected{% endif %}>قبعة التخرج (Graduation Cap)</option>
                            <option value="fa-brain" {% if rank.icon == 'fa-brain' %}selected{% endif %}>دماغ (Brain)</option>
                            <option value="fa-eye" {% if rank.icon == 'fa-eye' %}selected{% endif %}>عين (Eye)</option>
                            <option value="fa-hand-holding-heart" {% if rank.icon == 'fa-hand-holding-heart' %}selected{% endif %}>يد تحمل قلب (Hand Holding Heart)</option>
                            <option value="fa-fire" {% if rank.icon == 'fa-fire' %}selected{% endif %}>نار (Fire)</option>
                            <option value="fa-bolt" {% if rank.icon == 'fa-bolt' %}selected{% endif %}>برق (Lightning)</option>
                            <option value="fa-magic" {% if rank.icon == 'fa-magic' %}selected{% endif %}>سحر (Magic)</option>
                            <option value="fa-diamond" {% if rank.icon == 'fa-diamond' %}selected{% endif %}>ماس (Diamond)</option>
                            <option value="fa-key" {% if rank.icon == 'fa-key' %}selected{% endif %}>مفتاح (Key)</option>
                            <option value="fa-lock" {% if rank.icon == 'fa-lock' %}selected{% endif %}>قفل (Lock)</option>
                            <option value="fa-unlock" {% if rank.icon == 'fa-unlock' %}selected{% endif %}>فتح القفل (Unlock)</option>
                        </select>
                        <div class="form-text">اختر أيقونة للرتبة</div>
                    </div>

                    <div class="col-md-12 mb-3">
                        <label class="form-label">معاينة الرتبة:</label>
                        <div class="p-3 border rounded">
                            <span id="preview-badge" class="badge" style="background-color: {{ rank.color }}; color: {{ '#000' if rank.color == '#ffffff' else '#fff' }}">
                                <i id="preview-icon" class="fas {{ rank.icon }} me-1"></i>
                                <span id="preview-name">{{ rank.name }}</span>
                            </span>
                        </div>
                    </div>

                    <div class="col-12 mt-3">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>حفظ التغييرات
                        </button>
                        <a href="{{ url_for('honorary_rank.index') }}" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i>إلغاء
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    function togglePremiumOptions() {
        const isPremium = document.getElementById('is_premium').checked;
        const basicSection = document.getElementById('basic-color-section');
        const premiumSection = document.getElementById('premium-color-section');

        if (isPremium) {
            basicSection.style.display = 'none';
            premiumSection.style.display = 'block';
        } else {
            basicSection.style.display = 'block';
            premiumSection.style.display = 'none';
        }
        updatePreview();
    }

    $(document).ready(function() {
        // Update preview when inputs change
        function updatePreview() {
            const name = $('#name').val() || 'اسم الرتبة';
            const icon = $('#icon').val();
            const isPremium = $('#is_premium').is(':checked');

            $('#preview-name').text(name);
            $('#preview-icon').attr('class', 'fas ' + icon + ' me-1');

            if (isPremium) {
                const colorStart = $('#color_start').val();
                const colorEnd = $('#color_end').val();
                const direction = $('#gradient_direction').val();
                const animated = $('#animated_gradient').is(':checked');

                let gradient = `linear-gradient(${direction}, ${colorStart}, ${colorEnd})`;
                $('#preview-badge').css('background', gradient);
                $('#preview-badge').css('color', '#fff');

                if (animated) {
                    $('#preview-badge').addClass('animated-gradient');
                } else {
                    $('#preview-badge').removeClass('animated-gradient');
                }
            } else {
                const color = $('#color').val();
                $('#preview-badge').css('background', color);
                $('#preview-badge').css('color', isLightColor(color) ? '#000' : '#fff');
                $('#preview-badge').removeClass('animated-gradient');
            }
        }
        
        // Check if color is light (to determine text color)
        function isLightColor(color) {
            const hex = color.replace('#', '');
            const r = parseInt(hex.substr(0, 2), 16);
            const g = parseInt(hex.substr(2, 2), 16);
            const b = parseInt(hex.substr(4, 2), 16);
            const brightness = ((r * 299) + (g * 587) + (b * 114)) / 1000;
            return brightness > 155;
        }
        
        // Update preview on input change
        $('#name, #color, #icon, #color_start, #color_end, #gradient_direction, #animated_gradient, #is_premium').on('input change', updatePreview);

        // Initial preview update
        updatePreview();
    });
</script>
{% endblock %}
