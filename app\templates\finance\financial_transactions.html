{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
        {% if current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance') %}
        <a href="{{ url_for('finance.add_financial_transaction') }}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i>إضافة معاملة جديدة
        </a>
        {% endif %}
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header bg-light">
                    <h5 class="mb-0">المعاملات المالية</h5>
                </div>
                <div class="card-body">
                    <!-- Search and Filter Form -->
                    <form method="GET" action="{{ url_for('finance.financial_transactions') }}" class="mb-4">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="input-group">
                                    <input type="text" class="form-control" name="search" 
                                           value="{{ search_filter }}" placeholder="البحث برقم أو اسم المعاملة...">
                                    <button class="btn btn-outline-secondary" type="submit">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" name="status" onchange="this.form.submit()">
                                    <option value="">جميع الحالات</option>
                                    <option value="draft" {% if status_filter == 'draft' %}selected{% endif %}>مسودة</option>
                                    <option value="pending" {% if status_filter == 'pending' %}selected{% endif %}>معلق</option>
                                    <option value="paid" {% if status_filter == 'paid' %}selected{% endif %}>مدفوع</option>
                                    <option value="cancelled" {% if status_filter == 'cancelled' %}selected{% endif %}>ملغي</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                {% if status_filter or search_filter %}
                                <a href="{{ url_for('finance.financial_transactions') }}" class="btn btn-secondary">
                                    <i class="fas fa-times me-1"></i>إزالة الفلاتر
                                </a>
                                {% endif %}
                            </div>
                        </div>
                    </form>

                    {% if transactions.items %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>رقم المعاملة</th>
                                    <th>اسم المعاملة</th>
                                    <th>الحالة</th>
                                    <th>عدد البنود</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for transaction in transactions.items %}
                                <tr>
                                    <td>
                                        <strong>{{ transaction.transaction_number }}</strong>
                                    </td>
                                    <td>{{ transaction.transaction_name }}</td>
                                    <td>
                                        {% if transaction.status == 'draft' %}
                                        <span class="badge bg-secondary">مسودة</span>
                                        {% elif transaction.status == 'pending' %}
                                        <span class="badge bg-warning">معلق</span>
                                        {% elif transaction.status == 'paid' %}
                                        <span class="badge bg-success">مدفوع</span>
                                        {% elif transaction.status == 'cancelled' %}
                                        <span class="badge bg-danger">ملغي</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ transaction.items.count() }}</span>
                                    </td>
                                    <td>{{ transaction.created_at.strftime('%Y-%m-%d') }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ url_for('finance.view_financial_transaction', id=transaction.id) }}"
                                               class="btn btn-sm btn-outline-primary" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            {% if current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance') %}
                                            <a href="{{ url_for('finance.edit_financial_transaction', id=transaction.id) }}"
                                               class="btn btn-sm btn-outline-warning" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            {% endif %}
                                            {% if current_user.has_role('admin') or current_user.has_role('finance') %}
                                            <button type="button" 
                                                    class="btn btn-sm btn-outline-danger delete-transaction-btn" 
                                                    data-transaction-id="{{ transaction.id }}"
                                                    data-bs-toggle="modal" 
                                                    data-bs-target="#deleteModal"
                                                    title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if transactions.pages > 1 %}
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center">
                            {% if transactions.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('finance.financial_transactions', page=transactions.prev_num, status=status_filter, search=search_filter) }}">السابق</a>
                            </li>
                            {% endif %}
                            
                            {% for page_num in transactions.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != transactions.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('finance.financial_transactions', page=page_num, status=status_filter, search=search_filter) }}">{{ page_num }}</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if transactions.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('finance.financial_transactions', page=transactions.next_num, status=status_filter, search=search_filter) }}">التالي</a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-exchange-alt fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد معاملات مالية مضافة بعد</p>
                        {% if current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance') %}
                        <a href="{{ url_for('finance.add_financial_transaction') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>إضافة أول معاملة
                        </a>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                هل أنت متأكد من حذف هذه المعاملة المالية؟ لا يمكن التراجع عن هذا الإجراء.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle delete confirmation
    const deleteButtons = document.querySelectorAll('.delete-transaction-btn');
    const deleteForm = document.getElementById('deleteForm');
    
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const transactionId = this.getAttribute('data-transaction-id');
            deleteForm.action = `/finance/financial-transactions/delete/${transactionId}`;
        });
    });
});
</script>
{% endblock %}
