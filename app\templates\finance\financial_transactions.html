{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
        {% if current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance') %}
        <a href="{{ url_for('finance.add_financial_transaction') }}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i>إضافة معاملة جديدة
        </a>
        {% endif %}
    </div>

    <!-- رسالة مثبتة -->
    {% if system_message %}
    <div class="alert {{ system_message.get_bootstrap_class() }} {% if system_message.is_dismissible %}alert-dismissible{% endif %} fade show d-flex justify-content-between align-items-start" role="alert">
        <div class="flex-grow-1">
            <i class="{{ system_message.get_icon_class() }} me-2"></i>
            <strong>{{ system_message.title }}</strong>
            <div class="mt-1">{{ system_message.content }}</div>
        </div>
        <div class="ms-3">
            {% if current_user.has_role('admin') %}
            <button type="button" class="btn btn-sm btn-outline-secondary me-2" onclick="editSystemMessage()">
                <i class="fas fa-edit"></i> تعديل
            </button>
            {% endif %}
            {% if system_message.is_dismissible %}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            {% endif %}
        </div>
    </div>
    {% endif %}

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header bg-light">
                    <h5 class="mb-0">المعاملات المالية</h5>
                </div>
                <div class="card-body">
                    <!-- Search and Filter Form -->
                    <form method="GET" action="{{ url_for('finance.financial_transactions') }}" class="mb-4">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="input-group">
                                    <input type="text" class="form-control" name="search" 
                                           value="{{ search_filter }}" placeholder="البحث برقم أو اسم المعاملة...">
                                    <button class="btn btn-outline-secondary" type="submit">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" name="status" onchange="this.form.submit()">
                                    <option value="">جميع الحالات</option>
                                    <option value="draft" {% if status_filter == 'draft' %}selected{% endif %}>مسودة</option>
                                    <option value="pending" {% if status_filter == 'pending' %}selected{% endif %}>معلق</option>
                                    <option value="paid" {% if status_filter == 'paid' %}selected{% endif %}>مدفوع</option>
                                    <option value="cancelled" {% if status_filter == 'cancelled' %}selected{% endif %}>ملغي</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" name="per_page" onchange="this.form.submit()">
                                    <option value="25" {% if per_page == 25 %}selected{% endif %}>25 نتيجة</option>
                                    <option value="50" {% if per_page == 50 %}selected{% endif %}>50 نتيجة</option>
                                    <option value="100" {% if per_page == 100 %}selected{% endif %}>100 نتيجة</option>
                                    <option value="200" {% if per_page == 200 %}selected{% endif %}>200 نتيجة</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                {% if status_filter or search_filter %}
                                <a href="{{ url_for('finance.financial_transactions') }}" class="btn btn-secondary">
                                    <i class="fas fa-times me-1"></i>إزالة الفلاتر
                                </a>
                                {% endif %}
                            </div>
                        </div>
                    </form>

                    {% if transactions.items %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>رقم المعاملة</th>
                                    <th>اسم المعاملة</th>
                                    <th>الحالة</th>
                                    <th>عدد البنود</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for transaction in transactions.items %}
                                <tr>
                                    <td>
                                        <strong>{{ transaction.transaction_number }}</strong>
                                    </td>
                                    <td>{{ transaction.transaction_name }}</td>
                                    <td>
                                        {% if transaction.status == 'draft' %}
                                        <span class="badge bg-secondary">مسودة</span>
                                        {% elif transaction.status == 'pending' %}
                                        <span class="badge bg-warning">معلق</span>
                                        {% elif transaction.status == 'paid' %}
                                        <span class="badge bg-success">مدفوع</span>
                                        {% elif transaction.status == 'cancelled' %}
                                        <span class="badge bg-danger">ملغي</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ transaction.items.count() }}</span>
                                    </td>
                                    <td>{{ transaction.created_at.strftime('%Y-%m-%d') }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ url_for('finance.view_financial_transaction', id=transaction.id) }}"
                                               class="btn btn-sm btn-outline-primary" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            {% if current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance') %}
                                            <a href="{{ url_for('finance.edit_financial_transaction', id=transaction.id) }}"
                                               class="btn btn-sm btn-outline-warning" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            {% endif %}
                                            {% if current_user.has_role('admin') or current_user.has_role('finance') %}
                                            <button type="button" 
                                                    class="btn btn-sm btn-outline-danger delete-transaction-btn" 
                                                    data-transaction-id="{{ transaction.id }}"
                                                    data-bs-toggle="modal" 
                                                    data-bs-target="#deleteModal"
                                                    title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if transactions.pages > 1 %}
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center">
                            {% if transactions.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('finance.financial_transactions', page=transactions.prev_num, status=status_filter, search=search_filter) }}">السابق</a>
                            </li>
                            {% endif %}
                            
                            {% for page_num in transactions.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != transactions.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('finance.financial_transactions', page=page_num, status=status_filter, search=search_filter) }}">{{ page_num }}</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if transactions.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('finance.financial_transactions', page=transactions.next_num, status=status_filter, search=search_filter) }}">التالي</a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-exchange-alt fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد معاملات مالية مضافة بعد</p>
                        {% if current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance') %}
                        <a href="{{ url_for('finance.add_financial_transaction') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>إضافة أول معاملة
                        </a>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                هل أنت متأكد من حذف هذه المعاملة المالية؟ لا يمكن التراجع عن هذا الإجراء.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Modal تعديل الرسالة المثبتة -->
{% if current_user.has_role('admin') %}
<div class="modal fade" id="editMessageModal" tabindex="-1" aria-labelledby="editMessageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editMessageModalLabel">تعديل الرسالة المثبتة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editMessageForm" method="POST" action="{{ url_for('finance.update_system_message') }}">
                <div class="modal-body">
                    <input type="hidden" name="page_name" value="financial_transactions">

                    <div class="mb-3">
                        <label for="message_type" class="form-label">نوع الرسالة</label>
                        <select class="form-select" id="message_type" name="message_type" required>
                            <option value="info" {% if system_message and system_message.message_type == 'info' %}selected{% endif %}>معلومات (أزرق)</option>
                            <option value="success" {% if system_message and system_message.message_type == 'success' %}selected{% endif %}>نجاح (أخضر)</option>
                            <option value="warning" {% if system_message and system_message.message_type == 'warning' %}selected{% endif %}>تحذير (أصفر)</option>
                            <option value="danger" {% if system_message and system_message.message_type == 'danger' %}selected{% endif %}>خطر (أحمر)</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="title" class="form-label">عنوان الرسالة <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="title" name="title"
                               value="{{ system_message.title if system_message else '' }}" required>
                    </div>

                    <div class="mb-3">
                        <label for="content" class="form-label">محتوى الرسالة <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="content" name="content" rows="4" required>{{ system_message.content if system_message else '' }}</textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active"
                                       {% if not system_message or system_message.is_active %}checked{% endif %}>
                                <label class="form-check-label" for="is_active">
                                    الرسالة نشطة
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_dismissible" name="is_dismissible"
                                       {% if not system_message or system_message.is_dismissible %}checked{% endif %}>
                                <label class="form-check-label" for="is_dismissible">
                                    يمكن إغلاق الرسالة
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle delete confirmation
    const deleteButtons = document.querySelectorAll('.delete-transaction-btn');
    const deleteForm = document.getElementById('deleteForm');
    
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const transactionId = this.getAttribute('data-transaction-id');
            // توجيه للصفحة بدلاً من الحذف المباشر
            window.location.href = `/finance/financial-transactions/delete/${transactionId}`;
        });
    });
});

// دالة تعديل الرسالة المثبتة
function editSystemMessage() {
    const modal = new bootstrap.Modal(document.getElementById('editMessageModal'));
    modal.show();
}
</script>
{% endblock %}
