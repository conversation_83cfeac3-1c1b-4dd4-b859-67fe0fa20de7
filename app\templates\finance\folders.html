{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">{{ title }}</h1>
        <div>
            <a href="{{ url_for('finance.add_folder') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>إضافة مجلد جديد
            </a>
            <a href="{{ url_for('finance.accounts') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>العودة لشجرة الحسابات
            </a>
        </div>
    </div>

    {% if folders %}
    <div class="row">
        {% for folder in folders %}
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card h-100 shadow-sm" style="border-left: 4px solid {{ folder.color }};">
                <div class="card-header" style="background: linear-gradient(135deg, {{ folder.color }}15, {{ folder.color }}05);">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <i class="{{ folder.icon }} me-2" style="color: {{ folder.color }};"></i>
                            <strong>{{ folder.name }}</strong>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-cog"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ url_for('finance.edit_folder', id=folder.id) }}">
                                    <i class="fas fa-edit me-2"></i>تعديل
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" href="{{ url_for('finance.delete_folder', id=folder.id) }}">
                                    <i class="fas fa-trash me-2"></i>حذف
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    {% if folder.description %}
                    <p class="card-text text-muted">{{ folder.description }}</p>
                    {% endif %}
                    
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <div class="h5 mb-0 text-primary">{{ folder.accounts|length }}</div>
                                <small class="text-muted">حساب</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="h5 mb-0 text-info">{{ folder.children|length }}</div>
                            <small class="text-muted">مجلد فرعي</small>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-transparent">
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">
                            ترتيب: {{ folder.sort_order }}
                        </small>
                        <div>
                            {% if folder.is_expanded %}
                            <span class="badge bg-success">مفتوح</span>
                            {% else %}
                            <span class="badge bg-secondary">مطوي</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    {% else %}
    <div class="text-center py-5">
        <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
        <h4 class="text-muted">لا توجد مجلدات</h4>
        <p class="text-muted">ابدأ بإضافة مجلد جديد لتنظيم حساباتك</p>
        <a href="{{ url_for('finance.add_folder') }}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>إضافة مجلد جديد
        </a>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تأكيد الحذف
    document.querySelectorAll('a[href*="delete_folder"]').forEach(link => {
        link.addEventListener('click', function(e) {
            if (!confirm('هل أنت متأكد من حذف هذا المجلد؟')) {
                e.preventDefault();
            }
        });
    });
});
</script>
{% endblock %}
