{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
        <a href="{{ url_for('finance.view_financial_transaction', id=transaction.id) }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i>العودة إلى تفاصيل المعاملة
        </a>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>تعديل المعاملة المالية: {{ transaction.transaction_number }}
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" id="transactionForm">
                        <!-- معلومات المعاملة الأساسية -->
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">معلومات المعاملة الأساسية</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="transaction_number" class="form-label">رقم المعاملة <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="transaction_number" name="transaction_number"
                                                   value="{{ transaction.transaction_number }}" required>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="transaction_name" class="form-label">اسم المعاملة <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="transaction_name" name="transaction_name" 
                                                   value="{{ transaction.transaction_name }}" required>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="status" class="form-label">حالة المعاملة</label>
                                            <select class="form-select" id="status" name="status">
                                                <option value="draft" {% if transaction.status == 'draft' %}selected{% endif %}>مسودة</option>
                                                <option value="pending" {% if transaction.status == 'pending' %}selected{% endif %}>معلقة</option>
                                                <option value="paid" {% if transaction.status == 'paid' %}selected{% endif %}>مدفوعة</option>
                                                <option value="cancelled" {% if transaction.status == 'cancelled' %}selected{% endif %}>ملغية</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="mb-3">
                                            <label for="notes" class="form-label">ملاحظات</label>
                                            <textarea class="form-control" id="notes" name="notes" rows="3">{{ transaction.notes or '' }}</textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- بنود المعاملة -->
                        <div class="card mb-4">
                            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">بنود المعاملة</h6>
                                <button type="button" class="btn btn-sm btn-success" onclick="addTransactionItem()">
                                    <i class="fas fa-plus me-1"></i>إضافة بند
                                </button>
                            </div>
                            <div class="card-body">
                                <div id="transaction-items">
                                    <!-- البنود الموجودة -->
                                    {% for item in transaction.items %}
                                    <div class="transaction-item border rounded p-3 mb-3">
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <h6 class="mb-0">بند المعاملة</h6>
                                            <button type="button" class="btn btn-sm btn-danger" onclick="removeTransactionItem(this)">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-4">
                                                <div class="mb-3">
                                                    <label class="form-label">الحساب <span class="text-danger">*</span></label>
                                                    <select class="form-select" name="item_account_id[]" required>
                                                        <option value="">اختر الحساب</option>
                                                        {% for account in accounts %}
                                                        <option value="{{ account.id }}" {% if account.id == item.account_id %}selected{% endif %}>
                                                            {{ account.full_path }}
                                                        </option>
                                                        {% endfor %}
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-2">
                                                <div class="mb-3">
                                                    <label class="form-label">الإجراء</label>
                                                    <select class="form-select" name="item_action[]">
                                                        <option value="add" {% if item.action == 'add' %}selected{% endif %}>إضافة</option>
                                                        <option value="subtract" {% if item.action == 'subtract' %}selected{% endif %}>خصم</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="mb-3">
                                                    <label class="form-label">المبلغ <span class="text-danger">*</span></label>
                                                    <div class="input-group">
                                                        <input type="number" step="0.01" class="form-control" name="item_amount[]" 
                                                               value="{{ item.amount }}" min="0" required>
                                                        <span class="input-group-text">$</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="mb-3">
                                                    <label class="form-label">تاريخ ووقت الإجراء</label>
                                                    <input type="datetime-local" class="form-control" name="item_date[]"
                                                           value="{{ item.transaction_date.strftime('%Y-%m-%dT%H:%M') }}">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('finance.view_financial_transaction', id=transaction.id) }}" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-save me-1"></i>حفظ التغييرات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- قالب بند المعاملة -->
<template id="transaction-item-template">
    <div class="transaction-item border rounded p-3 mb-3">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h6 class="mb-0">بند المعاملة</h6>
            <button type="button" class="btn btn-sm btn-danger" onclick="removeTransactionItem(this)">
                <i class="fas fa-trash"></i>
            </button>
        </div>
        <div class="row">
            <div class="col-md-4">
                <div class="mb-3">
                    <label class="form-label">الحساب <span class="text-danger">*</span></label>
                    <select class="form-select" name="item_account_id[]" required>
                        <option value="">اختر الحساب</option>
                        {% for account in accounts %}
                        <option value="{{ account.id }}">{{ account.full_path }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            <div class="col-md-2">
                <div class="mb-3">
                    <label class="form-label">الإجراء</label>
                    <select class="form-select" name="item_action[]">
                        <option value="add">إضافة</option>
                        <option value="subtract">خصم</option>
                    </select>
                </div>
            </div>
            <div class="col-md-3">
                <div class="mb-3">
                    <label class="form-label">المبلغ <span class="text-danger">*</span></label>
                    <div class="input-group">
                        <input type="number" step="0.01" class="form-control" name="item_amount[]" min="0" required>
                        <span class="input-group-text">$</span>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="mb-3">
                    <label class="form-label">تاريخ ووقت الإجراء</label>
                    <input type="datetime-local" class="form-control" name="item_date[]">
                </div>
            </div>
        </div>
    </div>
</template>
{% endblock %}

{% block scripts %}
<script>
let itemCounter = {{ transaction.items.count() }};

function addTransactionItem() {
    const template = document.getElementById('transaction-item-template');
    const clone = template.content.cloneNode(true);
    
    // Set current datetime as default
    const datetimeInput = clone.querySelector('input[type="datetime-local"]');
    const now = new Date();
    now.setMinutes(now.getMinutes() - now.getTimezoneOffset());
    datetimeInput.value = now.toISOString().slice(0, 16);
    
    document.getElementById('transaction-items').appendChild(clone);
    itemCounter++;
}

function removeTransactionItem(button) {
    button.closest('.transaction-item').remove();
}

document.addEventListener('DOMContentLoaded', function() {
    // Form validation
    const form = document.getElementById('transactionForm');
    form.addEventListener('submit', function(e) {
        const transactionName = document.getElementById('transaction_name').value;
        const items = document.querySelectorAll('.transaction-item');
        
        if (!transactionName.trim()) {
            e.preventDefault();
            alert('اسم المعاملة مطلوب');
            return false;
        }
        
        if (items.length === 0) {
            e.preventDefault();
            alert('يجب إضافة بند واحد على الأقل');
            return false;
        }
        
        // Validate each item
        let hasValidItem = false;
        items.forEach(item => {
            const account = item.querySelector('select[name="item_account_id[]"]').value;
            const amount = item.querySelector('input[name="item_amount[]"]').value;
            
            if (account && amount && parseFloat(amount) > 0) {
                hasValidItem = true;
            }
        });
        
        if (!hasValidItem) {
            e.preventDefault();
            alert('يجب أن يحتوي بند واحد على الأقل على حساب ومبلغ صحيح');
            return false;
        }
    });
});
</script>
{% endblock %}
