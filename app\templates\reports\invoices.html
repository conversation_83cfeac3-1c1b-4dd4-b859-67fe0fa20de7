{% extends 'base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">تقرير الفواتير</h1>
        <div>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#exportModal">
                <i class="fas fa-file-export me-1"></i>تصدير التقرير
            </button>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">فلترة الفواتير</h6>
            <button class="btn btn-sm btn-outline-primary" type="button" data-bs-toggle="collapse" data-bs-target="#filterCollapse" aria-expanded="false" aria-controls="filterCollapse">
                <i class="fas fa-filter me-1"></i>عرض/إخفاء الفلاتر
            </button>
        </div>
        <div class="collapse" id="filterCollapse">
            <div class="card-body">
                <form id="filterForm" action="{{ url_for('report.invoices') }}" method="GET">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="status" class="form-label">الحالة</label>
                            <select class="form-select" id="status" name="status">
                                <option value="all" {% if status == 'all' %}selected{% endif %}>الكل</option>
                                <option value="draft" {% if status == 'draft' %}selected{% endif %}>مسودة</option>
                                <option value="pending" {% if status == 'pending' %}selected{% endif %}>قيد الانتظار</option>
                                <option value="paid" {% if status == 'paid' %}selected{% endif %}>مدفوعة</option>
                                <option value="overdue" {% if status == 'overdue' %}selected{% endif %}>متأخرة</option>
                                <option value="cancelled" {% if status == 'cancelled' %}selected{% endif %}>ملغاة</option>
                            </select>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="client_id" class="form-label">العميل</label>
                            <select class="form-select" id="client_id" name="client_id">
                                <option value="all" {% if client_id == 'all' %}selected{% endif %}>الكل</option>
                                {% for client in clients %}
                                <option value="{{ client.id }}" {% if client_id|string == client.id|string %}selected{% endif %}>{{ client.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="project_id" class="form-label">المشروع</label>
                            <select class="form-select" id="project_id" name="project_id">
                                <option value="all" {% if project_id == 'all' %}selected{% endif %}>الكل</option>
                                {% for project in projects %}
                                <option value="{{ project.id }}" {% if project_id|string == project.id|string %}selected{% endif %}>{{ project.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="start_date" class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" value="{{ start_date }}">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="end_date" class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="end_date" name="end_date" value="{{ end_date }}">
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="export_format" class="form-label">تنسيق التصدير</label>
                            <select class="form-select" id="export_format" name="export_format">
                                <option value="pdf" selected>PDF</option>
                                <option value="excel">Excel</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-file-export me-1"></i>تصدير التقرير
                                </button>
                                <a href="{{ url_for('report.invoices') }}" class="btn btn-secondary">
                                    <i class="fas fa-redo me-1"></i>إعادة تعيين
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">الفواتير</h6>
            <div class="d-flex align-items-center">
                <select class="form-select form-select-sm me-2" onchange="changePerPage(this.value)" style="width: auto;">
                    <option value="25" {% if per_page == 25 %}selected{% endif %}>25 فاتورة</option>
                    <option value="50" {% if per_page == 50 %}selected{% endif %}>50 فاتورة</option>
                    <option value="100" {% if per_page == 100 %}selected{% endif %}>100 فاتورة</option>
                </select>
            </div>
        </div>
        <div class="card-body">
            {% if invoices %}
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>رقم الفاتورة</th>
                            <th>العميل</th>
                            <th>المشروع</th>
                            <th>تاريخ الإصدار</th>
                            <th>تاريخ الاستحقاق</th>
                            <th>المبلغ الإجمالي</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% if invoices.items %}
                            {% for invoice in invoices.items %}
                            <tr>
                                <td>{{ loop.index + ((invoices.page - 1) * invoices.per_page) }}</td>
                                <td>{{ invoice.invoice_number }}</td>
                                <td>{{ invoice.client.name if invoice.client else 'غير محدد' }}</td>
                                <td>{{ invoice.project.name if invoice.project else 'غير محدد' }}</td>
                                <td>{{ invoice.issue_date.strftime('%Y-%m-%d') }}</td>
                                <td>{{ invoice.due_date.strftime('%Y-%m-%d') if invoice.due_date else 'غير محدد' }}</td>
                                <td>${{ "%.2f"|format(invoice.calculate_total()) }}</td>
                                <td>
                                    {% if invoice.status == 'draft' %}
                                    <span class="badge bg-secondary">مسودة</span>
                                    {% elif invoice.status == 'pending' %}
                                    <span class="badge bg-warning text-dark">قيد الانتظار</span>
                                    {% elif invoice.status == 'paid' %}
                                    <span class="badge bg-success">مدفوعة</span>
                                    {% elif invoice.status == 'overdue' %}
                                    <span class="badge bg-danger">متأخرة</span>
                                    {% elif invoice.status == 'cancelled' %}
                                    <span class="badge bg-dark">ملغاة</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ url_for('finance.view_invoice', id=invoice.id) }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        {% else %}
                            {% for invoice in invoices %}
                            <tr>
                                <td>{{ loop.index }}</td>
                                <td>{{ invoice.invoice_number }}</td>
                                <td>{{ invoice.client.name if invoice.client else 'غير محدد' }}</td>
                                <td>{{ invoice.project.name if invoice.project else 'غير محدد' }}</td>
                                <td>{{ invoice.issue_date.strftime('%Y-%m-%d') }}</td>
                                <td>{{ invoice.due_date.strftime('%Y-%m-%d') if invoice.due_date else 'غير محدد' }}</td>
                                <td>${{ "%.2f"|format(invoice.calculate_total()) }}</td>
                                <td>
                                    {% if invoice.status == 'draft' %}
                                    <span class="badge bg-secondary">مسودة</span>
                                    {% elif invoice.status == 'pending' %}
                                    <span class="badge bg-warning text-dark">قيد الانتظار</span>
                                    {% elif invoice.status == 'paid' %}
                                    <span class="badge bg-success">مدفوعة</span>
                                    {% elif invoice.status == 'overdue' %}
                                    <span class="badge bg-danger">متأخرة</span>
                                    {% elif invoice.status == 'cancelled' %}
                                    <span class="badge bg-dark">ملغاة</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ url_for('finance.view_invoice', id=invoice.id) }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        {% endif %}
                    </tbody>
                </table>
            </div>

            <!-- تقسيم الصفحات -->
            {% if invoices.items and invoices.pages > 1 %}
            <div class="d-flex justify-content-between align-items-center mt-3">
                <div>
                    <small class="text-muted">
                        عرض {{ invoices.per_page * (invoices.page - 1) + 1 }} إلى
                        {{ invoices.per_page * (invoices.page - 1) + invoices.items|length }}
                        من {{ invoices.total }} فاتورة
                    </small>
                </div>
                <nav aria-label="تقسيم صفحات الفواتير">
                    <ul class="pagination pagination-sm mb-0">
                        {% if invoices.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('report.invoices', page=invoices.prev_num, per_page=per_page) }}">السابق</a>
                        </li>
                        {% endif %}

                        {% for page_num in invoices.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != invoices.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('report.invoices', page=page_num, per_page=per_page) }}">{{ page_num }}</a>
                                </li>
                                {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                                {% endif %}
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">…</span>
                            </li>
                            {% endif %}
                        {% endfor %}

                        {% if invoices.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('report.invoices', page=invoices.next_num, per_page=per_page) }}">التالي</a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
            {% endif %}
            {% else %}
            <div class="text-center py-4">
                <p class="text-muted mb-0">لا توجد فواتير لعرضها</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Export Modal -->
<div class="modal fade" id="exportModal" tabindex="-1" aria-labelledby="exportModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form action="{{ url_for('report.export', report_type='invoices') }}" method="POST">
                <div class="modal-header">
                    <h5 class="modal-title" id="exportModalLabel">تصدير تقرير الفواتير</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="export_format" class="form-label">صيغة التصدير</label>
                        <select class="form-select" id="export_format" name="export_format">
                            <option value="pdf">PDF</option>
                            <option value="excel">Excel</option>
                        </select>
                    </div>

                    <!-- Hidden fields for filters -->
                    <input type="hidden" id="hidden_status" name="status" value="{{ status }}">
                    <input type="hidden" id="hidden_client_id" name="client_id" value="{{ client_id }}">
                    <input type="hidden" id="hidden_project_id" name="project_id" value="{{ project_id }}">
                    <input type="hidden" id="hidden_start_date" name="start_date" value="{{ start_date }}">
                    <input type="hidden" id="hidden_end_date" name="end_date" value="{{ end_date }}">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">تصدير</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        $('#dataTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json"
            },
            "order": [[4, "desc"]]
        });

        // Update hidden fields when form is submitted
        $('#filterForm').on('submit', function() {
            $('#hidden_status').val($('#status').val());
            $('#hidden_client_id').val($('#client_id').val());
            $('#hidden_project_id').val($('#project_id').val());
            $('#hidden_start_date').val($('#start_date').val());
            $('#hidden_end_date').val($('#end_date').val());
        });

        // Update hidden fields when export button is clicked
        $('#exportModal').on('show.bs.modal', function() {
            $('#hidden_status').val($('#status').val());
            $('#hidden_client_id').val($('#client_id').val());
            $('#hidden_project_id').val($('#project_id').val());
            $('#hidden_start_date').val($('#start_date').val());
            $('#hidden_end_date').val($('#end_date').val());
        });
    });

function changePerPage(perPage) {
    const url = new URL(window.location);
    url.searchParams.set('per_page', perPage);
    url.searchParams.set('page', 1); // Reset to first page
    window.location.href = url.toString();
}
</script>
{% endblock %}
