{% extends 'base.html' %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1>إرسال إشعار</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('dashboard.index') }}">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('notification.index') }}">الإشعارات</a></li>
                    <li class="breadcrumb-item active" aria-current="page">إرسال إشعار</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header bg-light">
                    <h5 class="mb-0">إرسال إشعار جديد</h5>
                </div>
                <div class="card-body">
                    <form action="{{ url_for('notification.send') }}" method="POST">
                        <div class="mb-3">
                            <label for="user_ids" class="form-label">المستلمين</label>
                            <select class="form-select" id="user_ids" name="user_ids" multiple required>
                                {% for user in users %}
                                <option value="{{ user.id }}">{{ user.username }} ({{ user.email }})</option>
                                {% endfor %}
                            </select>
                            <div class="form-text">يمكنك اختيار أكثر من مستلم باستخدام مفتاح Ctrl أو Shift</div>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="select_all_users" name="select_all_users">
                                <label class="form-check-label" for="select_all_users">
                                    إرسال إلى جميع المستخدمين
                                </label>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="title" class="form-label">العنوان</label>
                            <input type="text" class="form-control" id="title" name="title" required>
                        </div>
                        <div class="mb-3">
                            <label for="message" class="form-label">الرسالة</label>
                            <textarea class="form-control" id="message" name="message" rows="3" required></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="link_url" class="form-label">رابط (اختياري)</label>
                            <input type="url" class="form-control" id="link_url" name="link_url" placeholder="https://example.com">
                            <div class="form-text">يمكنك إضافة رابط للإشعار (مثل رابط لصفحة المشروع أو المهمة)</div>
                        </div>

                        <div class="mb-3">
                            <label for="link_text" class="form-label">نص الرابط (اختياري)</label>
                            <input type="text" class="form-control" id="link_text" name="link_text" placeholder="انقر هنا للعرض">
                            <div class="form-text">النص الذي سيظهر للرابط</div>
                        </div>
                        <div class="mb-3">
                            <label for="notification_type" class="form-label">نوع الإشعار</label>
                            <select class="form-select" id="notification_type" name="notification_type">
                                <option value="">-- بدون نوع --</option>
                                <option value="task">مهمة</option>
                                <option value="project">مشروع</option>
                                <option value="system">النظام</option>
                                <option value="finance">مالية</option>
                                <option value="client">عميل</option>
                            </select>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="related_project_id" class="form-label">المشروع المرتبط (اختياري)</label>
                                <div class="project-search-container position-relative">
                                    <input type="text" class="form-control" id="project-search" placeholder="ابحث في المشاريع...">
                                    <div class="search-results d-none" id="project-search-results"></div>
                                </div>
                                <div class="selected-project mt-2" id="selected-project"></div>
                                <input type="hidden" name="related_project_id" id="project-input">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="related_task_id" class="form-label">المهمة المرتبطة (اختياري)</label>
                                <div class="task-search-container position-relative">
                                    <input type="text" class="form-control" id="task-search" placeholder="ابحث في المهام...">
                                    <div class="search-results d-none" id="task-search-results"></div>
                                </div>
                                <div class="selected-task mt-2" id="selected-task"></div>
                                <input type="hidden" name="related_task_id" id="task-input">
                            </div>
                        </div>
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">إرسال الإشعار</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Handle select all users checkbox
        const selectAllCheckbox = document.getElementById('select_all_users');
        const userSelect = document.getElementById('user_ids');

        if (selectAllCheckbox && userSelect) {
            selectAllCheckbox.addEventListener('change', function() {
                if (this.checked) {
                    // Select all options
                    for (let i = 0; i < userSelect.options.length; i++) {
                        userSelect.options[i].selected = true;
                    }
                    userSelect.disabled = true;
                } else {
                    // Deselect all options
                    for (let i = 0; i < userSelect.options.length; i++) {
                        userSelect.options[i].selected = false;
                    }
                    userSelect.disabled = false;
                }
            });
        }

        // البحث في المشاريع والمهام
        const projects = {{ projects|tojson }};
        const tasks = {{ tasks|tojson }};
        let selectedProject = null;
        let selectedTask = null;

        // البحث في المشاريع
        const projectSearch = document.getElementById('project-search');
        const projectSearchResults = document.getElementById('project-search-results');

        if (projectSearch && projectSearchResults) {
            projectSearch.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                const filteredProjects = projects.filter(project =>
                    project.name.toLowerCase().includes(searchTerm)
                );
                displayProjectResults(filteredProjects);
            });

            projectSearch.addEventListener('focus', function() {
                if (this.value.trim() === '') {
                    displayProjectResults(projects);
                }
            });
        }

        function displayProjectResults(projects) {
        if (projects.length === 0) {
            projectSearchResults.classList.add('d-none');
            return;
        }
        projectSearchResults.innerHTML = projects.map(project =>
            `<div class="search-result-item" data-id="${project.id}" data-name="${project.name.replace(/"/g, '&quot;')}">
                ${project.name}
            </div>`
        ).join('');
        projectSearchResults.querySelectorAll('.search-result-item').forEach(item => {
            item.addEventListener('click', function() {
                const id = parseInt(this.dataset.id);
                const name = this.dataset.name;
                selectProject(id, name);
            });
        });
        projectSearchResults.classList.remove('d-none');
        }

        function selectProject(id, name) {
            selectedProject = {id, name};
            updateSelectedProject();
            projectSearch.value = '';
            projectSearchResults.classList.add('d-none');
        }

        function updateSelectedProject() {
            const container = document.getElementById('selected-project');
            if (selectedProject) {
                container.innerHTML = `<span class="badge bg-primary me-1 mb-1">
                    ${selectedProject.name}
                    <button type="button" class="btn-close btn-close-white ms-1"></button>
                </span>`;
                container.querySelector('.btn-close').addEventListener('click', function() {
                    removeProject();
                });
                document.getElementById('project-input').value = selectedProject.id;
            } else {
                container.innerHTML = '';
                document.getElementById('project-input').value = '';
            }
        }

        function removeProject() {
            selectedProject = null;
            updateSelectedProject();
        }

        // البحث في المهام
        const taskSearch = document.getElementById('task-search');
        const taskSearchResults = document.getElementById('task-search-results');

        if (taskSearch && taskSearchResults) {
            taskSearch.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                const filteredTasks = tasks.filter(task =>
                    task.title.toLowerCase().includes(searchTerm)
                );
                displayTaskResults(filteredTasks);
            });

            taskSearch.addEventListener('focus', function() {
                if (this.value.trim() === '') {
                    displayTaskResults(tasks);
                }
            });
        }

        function displayTaskResults(tasks) {
            if (tasks.length === 0) {
                taskSearchResults.classList.add('d-none');
                return;
            }
            taskSearchResults.innerHTML = tasks.map(task =>
                `<div class="search-result-item" data-id="${task.id}" data-title="${task.title.replace(/"/g, '&quot;')}">
                    ${task.title}
                </div>`
            ).join('');
            taskSearchResults.querySelectorAll('.search-result-item').forEach(item => {
                item.addEventListener('click', function() {
                    const id = parseInt(this.dataset.id);
                    const title = this.dataset.title;
                    selectTask(id, title);
                });
            });
            taskSearchResults.classList.remove('d-none');
        }

        function selectTask(id, title) {
            selectedTask = {id, title};
            updateSelectedTask();
            taskSearch.value = '';
            taskSearchResults.classList.add('d-none');
        }

        function updateSelectedTask() {
            const container = document.getElementById('selected-task');
            if (selectedTask) {
                container.innerHTML = `<span class="badge bg-success me-1 mb-1">
                    ${selectedTask.title}
                    <button type="button" class="btn-close btn-close-white ms-1"></button>
                </span>`;
                container.querySelector('.btn-close').addEventListener('click', function() {
                    removeTask();
                });
                document.getElementById('task-input').value = selectedTask.id;
            } else {
                container.innerHTML = '';
                document.getElementById('task-input').value = '';
            }
        }

        function removeTask() {
            selectedTask = null;
            updateSelectedTask();
        }

        // إخفاء النتائج عند النقر خارجها
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.project-search-container')) {
                projectSearchResults.classList.add('d-none');
            }
            if (!e.target.closest('.task-search-container')) {
                taskSearchResults.classList.add('d-none');
            }
        });

    }); // إغلاق DOMContentLoaded
</script>

<style>
.search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-radius: 0.375rem;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.search-result-item {
    padding: 0.5rem 1rem;
    cursor: pointer;
    border-bottom: 1px solid #eee;
}

.search-result-item:hover {
    background-color: #f8f9fa;
}

.search-result-item:last-child {
    border-bottom: none;
}
</style>
{% endblock %}

{% endblock %}
