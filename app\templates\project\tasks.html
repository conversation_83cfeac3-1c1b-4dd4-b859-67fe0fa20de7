{% extends 'base.html' %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>مهام مشروع {{ project.name }}</h1>
    <div>
        {% if current_user.has_role('admin') or current_user.has_role('manager') or
            current_user.id == project.manager_id or
            current_user in project.managers or
            (current_user.has_role('department_head') and current_user.managed_department.id == project.department_id) %}
        <a href="{{ url_for('project.create_task', id=project.id) }}" class="btn btn-primary me-2">
            <i class="fas fa-plus me-1"></i>إضافة مهمة
        </a>
        {% endif %}
        <a href="{{ url_for('project.view', id=project.id) }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i>العودة للمشروع
        </a>
    </div>
</div>

<!-- Search and Filter Form -->
<div class="card shadow mb-4">
    <div class="card-header bg-light">
        <h5 class="mb-0">بحث وتصفية</h5>
    </div>
    <div class="card-body">
        <form method="GET" action="{{ url_for('project.tasks', id=project.id) }}" class="row g-3">
            <div class="col-md-4">
                <div class="input-group">
                    <input type="text" class="form-control" id="search" name="search" placeholder="ابحث بعنوان المهمة أو الوصف" value="{{ search_query }}">
                    <button class="btn btn-primary" type="submit">
                        <i class="fas fa-search"></i> بحث
                    </button>
                    {% if search_query %}
                    <a href="{{ url_for('project.tasks', id=project.id) }}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> مسح
                    </a>
                    {% endif %}
                </div>
            </div>
            <div class="col-md-2">
                <select class="form-select" id="status" name="status" onchange="this.form.submit()">
                    <option value="all" {% if status_filter == 'all' or not status_filter %}selected{% endif %}>جميع الحالات</option>
                    <option value="pending" {% if status_filter == 'pending' %}selected{% endif %}>معلق</option>
                    <option value="in_progress" {% if status_filter == 'in_progress' %}selected{% endif %}>قيد التنفيذ</option>
                    <option value="completed" {% if status_filter == 'completed' %}selected{% endif %}>مكتمل</option>
                    <option value="cancelled" {% if status_filter == 'cancelled' %}selected{% endif %}>ملغي</option>
                </select>
            </div>
            <div class="col-md-2">
                <select class="form-select" id="priority" name="priority" onchange="this.form.submit()">
                    <option value="all" {% if priority_filter == 'all' or not priority_filter %}selected{% endif %}>جميع الأولويات</option>
                    <option value="high" {% if priority_filter == 'high' %}selected{% endif %}>عالية</option>
                    <option value="medium" {% if priority_filter == 'medium' %}selected{% endif %}>متوسطة</option>
                    <option value="low" {% if priority_filter == 'low' %}selected{% endif %}>منخفضة</option>
                </select>
            </div>
            <div class="col-md-2">
                <select class="form-select" id="assignee" name="assignee" onchange="this.form.submit()">
                    <option value="all" {% if assignee_filter == 'all' or not assignee_filter %}selected{% endif %}>جميع المسؤولين</option>
                    {% for member in project_members %}
                    <option value="{{ member.id }}" {% if assignee_filter|string == member.id|string %}selected{% endif %}>{{ member.get_full_name() }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <select class="form-select" id="per_page" name="per_page" onchange="this.form.submit()">
                    <option value="25" {% if current_per_page == 25 %}selected{% endif %}>عرض 25 مهمة</option>
                    <option value="50" {% if current_per_page == 50 %}selected{% endif %}>عرض 50 مهمة</option>
                    <option value="100" {% if current_per_page == 100 %}selected{% endif %}>عرض 100 مهمة</option>
                </select>
            </div>
        </form>
    </div>
</div>

<div class="card shadow mb-4">
    <div class="card-header bg-light">
        <h5 class="mb-0">قائمة المهام</h5>
    </div>
    <div class="card-body">
        {% if tasks.items %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>عنوان المهمة</th>
                        <th>المسؤول</th>
                        <th>الحالة</th>
                        <th>تاريخ البدء</th>
                        <th>تاريخ الاستحقاق</th>
                        <th>الأولوية</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for task in tasks.items %}
                    <tr class="task-item" data-status="{{ task.status }}">
                        <td>{{ task.title }}</td>
                        <td>{{ task.assignee.get_full_name() if task.assignee else 'غير محدد' }}</td>
                        <td>
                            {% if current_user.has_role('admin') or current_user.has_role('manager') or
                                current_user.id == project.manager_id or
                                current_user in project.managers or
                                current_user.id == task.assignee_id %}
                            <form id="task-status-form-{{ task.id }}" action="{{ url_for('project.update_task_status', task_id=task.id) }}" method="POST">
                                <select class="form-select form-select-sm task-status-select" data-task-id="{{ task.id }}" name="status">
                                    <option value="pending" {% if task.status == 'pending' %}selected{% endif %}>معلق</option>
                                    <option value="in_progress" {% if task.status == 'in_progress' %}selected{% endif %}>قيد التنفيذ</option>
                                    <option value="completed" {% if task.status == 'completed' %}selected{% endif %}>مكتمل</option>
                                    <option value="cancelled" {% if task.status == 'cancelled' %}selected{% endif %}>ملغي</option>
                                </select>
                            </form>
                            {% else %}
                            {% if task.status == 'pending' %}
                            <span class="badge bg-secondary">معلق</span>
                            {% elif task.status == 'in_progress' %}
                            <span class="badge bg-primary">قيد التنفيذ</span>
                            {% elif task.status == 'completed' %}
                            <span class="badge bg-success">مكتمل</span>
                            {% elif task.status == 'cancelled' %}
                            <span class="badge bg-danger">ملغي</span>
                            {% endif %}
                            {% endif %}
                        </td>
                        <td>{{ task.start_date.strftime('%Y-%m-%d') }}</td>
                        <td>
                            {{ task.due_date.strftime('%Y-%m-%d') if task.due_date else 'غير محدد' }}
                            {% if task.is_overdue() %}
                            <span class="badge bg-danger ms-1">متأخر</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if task.priority == 'low' %}
                            <span class="badge bg-info">منخفضة</span>
                            {% elif task.priority == 'medium' %}
                            <span class="badge bg-warning">متوسطة</span>
                            {% elif task.priority == 'high' %}
                            <span class="badge bg-danger">عالية</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group">
                                <a href="{{ url_for('project.view_task', task_id=task.id) }}" class="btn btn-sm btn-info">
                                    <i class="fas fa-eye"></i>
                                </a>
                                {% if current_user.has_role('admin') or current_user.has_role('manager') or
                                    current_user.id == project.manager_id or
                                    current_user in project.managers or
                                    (current_user.has_role('department_head') and current_user.managed_department.id == project.department_id) %}
                                <a href="{{ url_for('project.edit_task', task_id=task.id) }}" class="btn btn-sm btn-primary">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{{ url_for('project.delete_task', task_id=task.id) }}" class="btn btn-sm btn-danger">
                                    <i class="fas fa-trash"></i>
                                </a>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>

            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center mt-4">
                <div>
                    عرض {{ tasks.items|length }} من {{ tasks.total }} مهمة
                    {% if search_query %}
                    <span class="text-muted">(تصفية بواسطة: {{ search_query }})</span>
                    {% endif %}
                    {% if status_filter and status_filter != 'all' %}
                    <span class="text-muted">
                        (الحالة:
                        {% if status_filter == 'pending' %}معلق{% endif %}
                        {% if status_filter == 'in_progress' %}قيد التنفيذ{% endif %}
                        {% if status_filter == 'completed' %}مكتمل{% endif %}
                        {% if status_filter == 'cancelled' %}ملغي{% endif %}
                        )
                    </span>
                    {% endif %}
                    {% if priority_filter and priority_filter != 'all' %}
                    <span class="text-muted">
                        (الأولوية:
                        {% if priority_filter == 'high' %}عالية{% endif %}
                        {% if priority_filter == 'medium' %}متوسطة{% endif %}
                        {% if priority_filter == 'low' %}منخفضة{% endif %}
                        )
                    </span>
                    {% endif %}
                </div>
                <nav aria-label="Page navigation">
                    <ul class="pagination">
                        {% if tasks.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('project.tasks', id=project.id, page=tasks.prev_num, per_page=current_per_page, search=search_query, status=status_filter, priority=priority_filter, assignee=assignee_filter) }}" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item disabled">
                            <a class="page-link" href="#" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        {% endif %}

                        {% set start_page = tasks.page - 2 if tasks.page > 2 else 1 %}
                        {% set end_page = start_page + 4 if start_page + 4 <= tasks.pages else tasks.pages %}
                        {% set start_page = end_page - 4 if end_page - 4 >= 1 else 1 %}

                        {% for page_num in range(start_page, end_page + 1) %}
                        <li class="page-item {% if page_num == tasks.page %}active{% endif %}">
                            <a class="page-link" href="{{ url_for('project.tasks', id=project.id, page=page_num, per_page=current_per_page, search=search_query, status=status_filter, priority=priority_filter, assignee=assignee_filter) }}">{{ page_num }}</a>
                        </li>
                        {% endfor %}

                        {% if tasks.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('project.tasks', id=project.id, page=tasks.next_num, per_page=current_per_page, search=search_query, status=status_filter, priority=priority_filter, assignee=assignee_filter) }}" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item disabled">
                            <a class="page-link" href="#" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
        </div>
        {% else %}
        <div class="alert alert-info">
            {% if search_query or status_filter != 'all' or priority_filter != 'all' or assignee_filter != 'all' %}
            لا توجد نتائج مطابقة للبحث.
            <a href="{{ url_for('project.tasks', id=project.id) }}" class="alert-link">عرض جميع المهام</a>
            {% else %}
            لا يوجد مهام لهذا المشروع حاليًا.
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Auto-submit task status form when status changes
        const taskStatusSelects = document.querySelectorAll('.task-status-select');
        taskStatusSelects.forEach(select => {
            select.addEventListener('change', function() {
                const taskId = this.getAttribute('data-task-id');
                document.getElementById(`task-status-form-${taskId}`).submit();
            });
        });

        // Apply color to task rows based on status
        const taskRows = document.querySelectorAll('.task-item');
        taskRows.forEach(row => {
            const status = row.getAttribute('data-status');
            if (status === 'completed') {
                row.classList.add('table-success');
            } else if (status === 'in_progress') {
                row.classList.add('table-primary');
            } else if (status === 'cancelled') {
                row.classList.add('table-danger');
            } else if (status === 'pending') {
                row.classList.add('table-secondary');
            }
        });
    });
</script>
{% endblock %}
{% endblock %}
