{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
        <div>
            {% if current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance') %}
            <a href="{{ url_for('finance.edit_salary', id=salary.id) }}" class="btn btn-warning me-2">
                <i class="fas fa-edit me-1"></i>تعديل الراتب
            </a>
            {% endif %}
            <a href="{{ url_for('finance.salaries') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-1"></i>العودة إلى الرواتب
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-money-bill-wave me-2"></i>تفاصيل الراتب
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-muted">معلومات الموظف</h6>
                            <table class="table table-borderless">
                                {% if salary.employee %}
                                <!-- موظف من النظام -->
                                <tr>
                                    <td><strong>الموظف في النظام:</strong></td>
                                    <td>
                                        <a href="{{ url_for('employee.view', id=salary.employee.id) }}">
                                            {{ salary.employee.first_name }} {{ salary.employee.last_name }}
                                        </a>
                                    </td>
                                </tr>
                                {% else %}
                                <!-- موظف يدوي -->
                                <tr>
                                    <td><strong>اسم الموظف:</strong></td>
                                    <td>{{ salary.employee_name }}</td>
                                </tr>
                                {% if salary.employee_manual %}
                                <tr>
                                    <td><strong>تفاصيل إضافية:</strong></td>
                                    <td>{{ salary.employee_manual }}</td>
                                </tr>
                                {% endif %}
                                {% endif %}
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted">معلومات الراتب</h6>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>إجمالي البنود:</strong></td>
                                    <td><span class="h5 text-primary">${{ "{:,.2f}".format(salary.total_amount) }}</span></td>
                                </tr>
                                {% if salary.commission_amount > 0 %}
                                <tr>
                                    <td><strong>عمولة الشركة:</strong></td>
                                    <td><span class="text-warning">${{ "{:,.2f}".format(salary.commission_amount) }}</span></td>
                                </tr>
                                <tr>
                                    <td><strong>المبلغ الصافي:</strong></td>
                                    <td><span class="h5 text-success">${{ "{:,.2f}".format(salary.net_amount) }}</span></td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td><strong>المبلغ الصافي:</strong></td>
                                    <td><span class="h5 text-success">${{ "{:,.2f}".format(salary.total_amount) }}</span></td>
                                </tr>
                                {% endif %}
                                <tr>
                                    <td><strong>الحالة:</strong></td>
                                    <td>
                                        {% if salary.status == 'draft' %}
                                        <span class="badge bg-secondary">مسودة</span>
                                        {% elif salary.status == 'pending' %}
                                        <span class="badge bg-warning">معلق</span>
                                        {% elif salary.status == 'paid' %}
                                        <span class="badge bg-success">مدفوع</span>
                                        {% elif salary.status == 'cancelled' %}
                                        <span class="badge bg-danger">ملغي</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% if salary.transfer_date %}
                                <tr>
                                    <td><strong>موعد الحوالة:</strong></td>
                                    <td>{{ salary.transfer_date.strftime('%Y-%m-%d %H:%M') }}</td>
                                </tr>
                                {% endif %}
                            </table>
                        </div>
                    </div>

                    {% if salary.notes %}
                    <div class="row mt-3">
                        <div class="col-md-12">
                            <h6 class="text-muted">ملاحظات</h6>
                            <div class="alert alert-info">
                                {{ salary.notes }}
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- الحسابات المرتبطة -->
            <div class="card shadow mt-4">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-sitemap me-2"></i>الحسابات المرتبطة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="text-center">
                                <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                                <h6>حساب المال المعلق</h6>
                                <p class="text-muted">
                                    {% if salary.pending_account %}
                                    {{ salary.pending_account.name }}
                                    {% else %}
                                    غير محدد
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <i class="fas fa-minus-circle fa-2x text-danger mb-2"></i>
                                <h6>حساب الخصم</h6>
                                <p class="text-muted">
                                    {% if salary.deduction_account %}
                                    {{ salary.deduction_account.name }}
                                    {% else %}
                                    غير محدد
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                                <h6>حساب الرواتب المدفوعة</h6>
                                <p class="text-muted">
                                    {% if salary.paid_account %}
                                    {{ salary.paid_account.name }}
                                    {% else %}
                                    غير محدد
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- بنود الراتب -->
            {% if salary.items %}
            <div class="card shadow mt-4">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-list me-2"></i>بنود الراتب
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>#</th>
                                    <th>المشروع</th>
                                    <th>المهمة</th>
                                    <th>المبلغ</th>
                                    <th>الوصف</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in salary.items %}
                                <tr>
                                    <td>{{ loop.index }}</td>
                                    <td>
                                        {% if item.project %}
                                        <span class="badge bg-primary">{{ item.project.name }}</span>
                                        {% else %}
                                        <span class="text-muted">غير محدد</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if item.task %}
                                        <span class="badge bg-success">{{ item.task.title }}</span>
                                        {% else %}
                                        <span class="text-muted">غير محدد</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <strong class="text-success">${{ "{:,.2f}".format(item.amount) }}</strong>
                                    </td>
                                    <td>
                                        {% if item.description %}
                                        {{ item.description }}
                                        {% else %}
                                        <span class="text-muted">لا يوجد وصف</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                            <tfoot class="table-light">
                                <tr>
                                    <th colspan="3">الإجمالي:</th>
                                    <th class="text-success">${{ "{:,.2f}".format(salary.total_amount) }}</th>
                                    <th></th>
                                </tr>
                                {% if salary.commission_amount > 0 %}
                                <tr>
                                    <th colspan="3">عمولة الشركة ({{ salary.commission_type == 'percentage' and salary.commission_value|string + '%' or 'مبلغ ثابت' }}):</th>
                                    <th class="text-warning">-${{ "{:,.2f}".format(salary.commission_amount) }}</th>
                                    <th></th>
                                </tr>
                                <tr>
                                    <th colspan="3">المبلغ الصافي:</th>
                                    <th class="text-primary">${{ "{:,.2f}".format(salary.net_amount) }}</th>
                                    <th></th>
                                </tr>
                                {% endif %}
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
            {% else %}
            <div class="card shadow mt-4">
                <div class="card-body text-center">
                    <i class="fas fa-list fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد بنود للراتب</h5>
                    <p class="text-muted">لم يتم إضافة أي بنود لهذا الراتب بعد</p>
                </div>
            </div>
            {% endif %}

            <!-- المرفقات والروابط - تم نقلها لأقسام منفصلة أدناه -->
        </div>

        <div class="col-md-4">
            <!-- معلومات إضافية -->
            <div class="card shadow">
                <div class="card-header bg-light">
                    <h6 class="mb-0">معلومات إضافية</h6>
                </div>
                <div class="card-body">
                    <table class="table table-borderless table-sm">
                        {% if salary.supervisor %}
                        <tr>
                            <td><strong>المشرف:</strong></td>
                            <td>{{ salary.supervisor.first_name }} {{ salary.supervisor.last_name }}</td>
                        </tr>
                        {% endif %}
                        <tr>
                            <td><strong>أنشأ بواسطة:</strong></td>
                            <td>{{ salary.created_by.first_name }} {{ salary.created_by.last_name }}</td>
                        </tr>
                        <tr>
                            <td><strong>تاريخ الإنشاء:</strong></td>
                            <td>{{ salary.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                        </tr>
                        <tr>
                            <td><strong>آخر تحديث:</strong></td>
                            <td>{{ salary.updated_at.strftime('%Y-%m-%d %H:%M') }}</td>
                        </tr>
                    </table>
                </div>
            </div>

            <!-- إجراءات سريعة -->
            {% if current_user.has_role('admin') or current_user.has_role('finance') %}
            <div class="card shadow mt-4">
                <div class="card-header bg-warning text-dark">
                    <h6 class="mb-0">إجراءات سريعة</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        {% if salary.status == 'draft' %}
                        <button class="btn btn-warning btn-sm" onclick="changeStatus('pending')">
                            <i class="fas fa-clock me-1"></i>تعليق الراتب
                        </button>
                        {% elif salary.status == 'pending' %}
                        <button class="btn btn-success btn-sm" onclick="changeStatus('paid')">
                            <i class="fas fa-check me-1"></i>تأكيد الدفع
                        </button>
                        {% endif %}
                        {% if salary.status != 'cancelled' %}
                        <button class="btn btn-danger btn-sm" onclick="changeStatus('cancelled')">
                            <i class="fas fa-times me-1"></i>إلغاء الراتب
                        </button>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- الروابط -->
            {% if salary.links %}
            <div class="card shadow mb-4">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-link me-2"></i>الروابط المرفقة
                    </h6>
                </div>
                <div class="card-body">
                    {% set link_lines = salary.links.split('\n') %}
                    {% for link in link_lines %}
                        {% if link.strip() %}
                            <div class="d-flex justify-content-between align-items-center mb-2 p-2 border rounded">
                                <div>
                                    <i class="fas fa-external-link-alt me-2"></i>
                                    <a href="{{ link.strip() }}" target="_blank" class="text-decoration-none">
                                        {{ link.strip() }}
                                    </a>
                                </div>
                                <div>
                                    <a href="{{ link.strip() }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-external-link-alt me-1"></i>فتح
                                    </a>
                                </div>
                            </div>
                        {% endif %}
                    {% endfor %}
                </div>
            </div>
            {% endif %}

            <!-- الملفات المرفقة -->
            {% if salary.notes and 'ملف مرفق:' in salary.notes %}
            <div class="card shadow mb-4">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-paperclip me-2"></i>الملفات المرفقة
                    </h6>
                </div>
                <div class="card-body">
                    {% set attachment_lines = salary.notes.split('\n') %}
                    {% for line in attachment_lines %}
                        {% if 'ملف مرفق:' in line %}
                            {% set filename = line.replace('ملف مرفق:', '').strip() %}
                            <div class="d-flex justify-content-between align-items-center mb-2 p-2 border rounded">
                                <div>
                                    <i class="fas fa-file me-2"></i>
                                    <span>{{ filename }}</span>
                                </div>
                                <div>
                                    <a href="{{ url_for('finance.download_salary_attachment', filename=filename) }}"
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-download me-1"></i>تحميل
                                    </a>
                                </div>
                            </div>
                        {% endif %}
                    {% endfor %}
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function changeStatus(newStatus) {
    if (confirm('هل أنت متأكد من تغيير حالة الراتب؟')) {
        // يمكن إضافة AJAX request هنا لتغيير الحالة
        window.location.href = `{{ url_for('finance.edit_salary', id=salary.id) }}?status=${newStatus}`;
    }
}
</script>
{% endblock %}
