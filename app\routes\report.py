from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify, send_file, make_response
from flask_login import login_required, current_user
from datetime import datetime
import os
import json
import pandas as pd
from io import BytesIO
import tempfile
import base64

from app import db
from app.models.user import User
from app.models.department import Department
from app.models.leave import LeaveRequest
from app.models.meeting import Meeting
from app.models.penalty import Penalty
from app.models.client import Client
from app.models.report import Report
from app.models.finance import Transaction, Invoice
from app.models.project import Project

def get_logo_base64():
    """Convert logo image to base64 string"""
    try:
        logo_path = os.path.join('app', 'static', 'img', 'logo.png')
        if os.path.exists(logo_path):
            with open(logo_path, 'rb') as image_file:
                encoded_string = base64.b64encode(image_file.read()).decode('utf-8')
                return encoded_string
        else:
            # Return a placeholder if logo not found
            return ''
    except Exception as e:
        print(f"Error encoding logo: {e}")
        return ''

def get_default_profile_image_base64():
    """Convert default profile image to base64 string"""
    try:
        # Try to load the default profile image
        default_image_path = os.path.join('app', 'static', 'uploads', 'default.jpg')
        if os.path.exists(default_image_path):
            with open(default_image_path, 'rb') as image_file:
                encoded_string = base64.b64encode(image_file.read()).decode('utf-8')
                return encoded_string
        else:
            # If default image not found, return an empty string
            print("Default profile image not found")
            return ''
    except Exception as e:
        print(f"Error encoding default profile image: {e}")
        return ''

report_bp = Blueprint('report', __name__, url_prefix='/reports')

# Add view and delete routes for reports
@report_bp.route('/download/<int:id>')
@login_required
def download(id):
    # Check if user has permission to view reports
    if not (current_user.has_role('admin') or current_user.has_role('manager')):
        flash('ليس لديك صلاحية لعرض التقارير', 'danger')
        return redirect(url_for('dashboard.index'))

    # Get report
    report = Report.query.get_or_404(id)

    # Send the file
    return send_file(
        report.file_path,
        as_attachment=True,
        download_name=f'{report.title}_{report.created_at.strftime("%Y%m%d_%H%M")}.html',
        mimetype='text/html'
    )

@report_bp.route('/remove/<int:id>', methods=['POST'])
@login_required
def remove(id):
    # Check if user has permission to delete reports
    if not current_user.has_role('admin'):
        flash('ليس لديك صلاحية لحذف التقارير', 'danger')
        return redirect(url_for('report.index'))

    # Get report
    report = Report.query.get_or_404(id)

    # Delete file
    try:
        if os.path.exists(report.file_path):
            os.remove(report.file_path)
    except Exception as e:
        print(f"Error deleting file: {e}")

    # Delete report from database
    db.session.delete(report)
    db.session.commit()

    flash('تم حذف التقرير بنجاح', 'success')
    return redirect(url_for('report.index'))

@report_bp.route('/')
@login_required
def index():
    # Check if user has permission to view reports
    if not (current_user.has_role('admin') or current_user.has_role('manager')):
        flash('ليس لديك صلاحية لعرض التقارير', 'danger')
        return redirect(url_for('dashboard.index'))

    # Get query parameters for pagination and search
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 25, type=int)
    search_query = request.args.get('search', '')
    report_type = request.args.get('report_type', '')
    date_from = request.args.get('date_from', '')
    date_to = request.args.get('date_to', '')

    # Build the query
    query = Report.query

    # Apply search filter if provided
    if search_query:
        search_term = f"%{search_query}%"
        query = query.filter(
            (Report.title.like(search_term))
        )

    # Apply report type filter if provided
    if report_type and report_type != 'all':
        query = query.filter(Report.report_type == report_type)

    # Apply date range filters if provided
    if date_from:
        try:
            from_date = datetime.strptime(date_from, '%Y-%m-%d')
            query = query.filter(Report.created_at >= from_date)
        except ValueError:
            pass

    if date_to:
        try:
            to_date = datetime.strptime(date_to, '%Y-%m-%d')
            to_date = to_date.replace(hour=23, minute=59, second=59)
            query = query.filter(Report.created_at <= to_date)
        except ValueError:
            pass

    # Order by created_at (newest first)
    query = query.order_by(Report.created_at.desc())

    # Paginate the results
    try:
        reports = query.paginate(page=page, per_page=per_page, error_out=False)
    except Exception as e:
        print(f"Error fetching reports: {e}")
        reports = []

    # Get unique report types for filter
    report_types = [
        {'value': 'employees', 'label': 'الموظفين'},
        {'value': 'departments', 'label': 'الأقسام'},
        {'value': 'leave', 'label': 'الإجازات'},
        {'value': 'meetings', 'label': 'الاجتماعات'},
        {'value': 'penalties', 'label': 'العقوبات'},
        {'value': 'clients', 'label': 'العملاء'},
        {'value': 'transactions', 'label': 'المعاملات المالية'},
        {'value': 'invoices', 'label': 'الفواتير'},
        {'value': 'employee_detail', 'label': 'تفاصيل الموظف'}
    ]

    return render_template('reports/index.html',
                          title='التقارير',
                          reports=reports,
                          report_types=report_types,
                          search_query=search_query,
                          report_type=report_type,
                          date_from=date_from,
                          date_to=date_to,
                          current_per_page=per_page)

@report_bp.route('/employees')
@login_required
def employees():
    # Check if user has permission to view reports
    if not (current_user.has_role('admin') or current_user.has_role('manager')):
        flash('ليس لديك صلاحية لعرض التقارير', 'danger')
        return redirect(url_for('dashboard.index'))

    # Get all employees
    employees = User.query.all()

    # Get departments for filtering
    departments = Department.query.all()

    return render_template('reports/employees.html', title='تقرير الموظفين', employees=employees, departments=departments)

@report_bp.route('/departments')
@login_required
def departments():
    # Check if user has permission to view reports
    if not (current_user.has_role('admin') or current_user.has_role('manager')):
        flash('ليس لديك صلاحية لعرض التقارير', 'danger')
        return redirect(url_for('dashboard.index'))

    # Get all departments
    departments = Department.query.all()

    return render_template('reports/departments.html', title='تقرير الأقسام', departments=departments)

@report_bp.route('/leave')
@login_required
def leave():
    # Check if user has permission to view reports
    if not (current_user.has_role('admin') or current_user.has_role('manager')):
        flash('ليس لديك صلاحية لعرض التقارير', 'danger')
        return redirect(url_for('dashboard.index'))

    # Get pagination parameters
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 25, type=int)

    # Ensure per_page is within allowed values
    if per_page not in [25, 50, 100]:
        per_page = 25

    # Get paginated leave requests
    leave_requests = LeaveRequest.query.order_by(LeaveRequest.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )

    # Get employees for filtering
    employees = User.query.all()

    return render_template('reports/leave.html', title='تقرير الإجازات',
                          leave_requests=leave_requests, employees=employees, per_page=per_page)

@report_bp.route('/meetings')
@login_required
def meetings():
    # Check if user has permission to view reports
    if not (current_user.has_role('admin') or current_user.has_role('manager')):
        flash('ليس لديك صلاحية لعرض التقارير', 'danger')
        return redirect(url_for('dashboard.index'))

    # Get all meetings
    meetings = Meeting.query.order_by(Meeting.date.desc()).all()

    # Get employees and clients for filtering
    employees = User.query.all()
    clients = Client.query.all()

    return render_template('reports/meetings.html', title='تقرير الاجتماعات', meetings=meetings, employees=employees, clients=clients)

@report_bp.route('/penalties')
@login_required
def penalties():
    # Check if user has permission to view reports
    if not (current_user.has_role('admin') or current_user.has_role('manager')):
        flash('ليس لديك صلاحية لعرض التقارير', 'danger')
        return redirect(url_for('dashboard.index'))

    # Get all penalties
    penalties = Penalty.query.order_by(Penalty.created_at.desc()).all()

    # Get employees for filtering
    employees = User.query.all()

    return render_template('reports/penalties.html', title='تقرير العقوبات', penalties=penalties, employees=employees)

@report_bp.route('/clients')
@login_required
def clients():
    # Check if user has permission to view reports
    if not (current_user.has_role('admin') or current_user.has_role('manager')):
        flash('ليس لديك صلاحية لعرض التقارير', 'danger')
        return redirect(url_for('dashboard.index'))

    # Get pagination parameters
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 25, type=int)

    # Ensure per_page is within allowed values
    if per_page not in [25, 50, 100]:
        per_page = 25

    # Get paginated clients
    clients = Client.query.order_by(Client.name).paginate(
        page=page, per_page=per_page, error_out=False
    )

    return render_template('reports/clients.html', title='تقرير العملاء',
                          clients=clients, per_page=per_page)

@report_bp.route('/transactions')
@login_required
def transactions():
    # Check if user has permission to view reports
    if not (current_user.has_role('admin') or current_user.has_role('manager')):
        flash('ليس لديك صلاحية لعرض التقارير', 'danger')
        return redirect(url_for('dashboard.index'))

    # Get pagination parameters
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 25, type=int)

    # Ensure per_page is within allowed values
    if per_page not in [25, 50, 100]:
        per_page = 25

    # Get filter parameters
    status = request.args.get('status', 'all')
    date_from = request.args.get('date_from', '')
    date_to = request.args.get('date_to', '')

    # Build query for new financial transactions system
    from app.models.finance import FinancialTransaction
    query = FinancialTransaction.query

    # Apply filters
    if status and status != 'all':
        query = query.filter_by(status=status)

    if date_from:
        try:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
            query = query.filter(FinancialTransaction.created_at >= date_from_obj)
        except ValueError:
            pass

    if date_to:
        try:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d')
            query = query.filter(FinancialTransaction.created_at <= date_to_obj)
        except ValueError:
            pass

    # Get paginated transactions
    transactions = query.order_by(FinancialTransaction.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )

    return render_template('reports/transactions.html', title='تقرير المعاملات المالية',
                          transactions=transactions, per_page=per_page,
                          status=status, date_from=date_from, date_to=date_to)

@report_bp.route('/invoices')
@login_required
def invoices():
    # Check if user has permission to view reports
    if not (current_user.has_role('admin') or current_user.has_role('manager')):
        flash('ليس لديك صلاحية لعرض التقارير', 'danger')
        return redirect(url_for('dashboard.index'))

    # Get pagination parameters
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 25, type=int)

    # Ensure per_page is within allowed values
    if per_page not in [25, 50, 100]:
        per_page = 25

    # Get filter parameters
    status = request.args.get('status', 'all')
    client_id = request.args.get('client_id', 'all')
    project_id = request.args.get('project_id', 'all')
    start_date = request.args.get('start_date', '')
    end_date = request.args.get('end_date', '')

    # Build query
    query = Invoice.query

    # Apply filters
    if status and status != 'all':
        query = query.filter_by(status=status)

    if client_id and client_id != 'all':
        query = query.filter_by(client_id=client_id)

    if project_id and project_id != 'all':
        query = query.filter_by(project_id=project_id)

    if start_date:
        try:
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
            query = query.filter(Invoice.issue_date >= start_date_obj)
        except ValueError:
            pass

    if end_date:
        try:
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
            query = query.filter(Invoice.issue_date <= end_date_obj)
        except ValueError:
            pass

    # Get paginated invoices
    invoices = query.order_by(Invoice.issue_date.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )

    # Get clients and projects for filtering
    clients = Client.query.all()
    projects = Project.query.all()

    return render_template('reports/invoices.html', title='تقرير الفواتير',
                          invoices=invoices, clients=clients, projects=projects,
                          per_page=per_page, status=status, client_id=client_id,
                          project_id=project_id, start_date=start_date, end_date=end_date)

@report_bp.route('/employee_salaries')
@login_required
def employee_salaries():
    # Check if user has permission to view reports
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance')):
        flash('ليس لديك صلاحية لعرض تقارير الرواتب', 'danger')
        return redirect(url_for('dashboard.index'))

    # Get pagination parameters
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 25, type=int)

    # Ensure per_page is within allowed values
    if per_page not in [25, 50, 100]:
        per_page = 25

    # Get filter parameters
    employee_id = request.args.get('employee_id', 'all')
    status = request.args.get('status', 'all')
    date_from = request.args.get('date_from', '')
    date_to = request.args.get('date_to', '')

    # Build query
    from app.models.finance import EmployeeSalary
    query = EmployeeSalary.query

    # Apply filters
    if employee_id and employee_id != 'all':
        query = query.filter_by(employee_id=employee_id)

    if status and status != 'all':
        query = query.filter_by(status=status)

    if date_from:
        try:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
            query = query.filter(EmployeeSalary.created_at >= date_from_obj)
        except ValueError:
            pass

    if date_to:
        try:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d')
            query = query.filter(EmployeeSalary.created_at <= date_to_obj)
        except ValueError:
            pass

    # Get paginated salaries
    salaries = query.order_by(EmployeeSalary.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )

    # Get employees for filtering
    employees = User.query.filter_by(is_active=True).all()

    return render_template('reports/employee_salaries.html', title='تقرير رواتب الموظفين',
                          salaries=salaries, employees=employees, per_page=per_page,
                          employee_id=employee_id, status=status,
                          date_from=date_from, date_to=date_to)

@report_bp.route('/accounts_tree')
@login_required
def accounts_tree():
    # Check if user has permission to view reports
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance')):
        flash('ليس لديك صلاحية لعرض تقارير الحسابات', 'danger')
        return redirect(url_for('dashboard.index'))

    # Get account folders and accounts
    from app.models.finance import AccountFolder, Account
    folders = AccountFolder.query.all()

    # Calculate statistics
    total_accounts = Account.query.count()
    active_accounts = Account.query.filter_by(is_active=True).count()
    total_balance = sum(account.balance for account in Account.query.all())

    return render_template('reports/accounts_tree.html', title='تقرير شجرة الحسابات',
                          folders=folders, total_accounts=total_accounts,
                          active_accounts=active_accounts, total_balance=total_balance)

@report_bp.route('/employee_detail')
@login_required
def employee_detail():
    # Check if user has permission to view reports
    if not (current_user.has_role('admin') or current_user.has_role('manager')):
        flash('ليس لديك صلاحية لعرض التقارير', 'danger')
        return redirect(url_for('dashboard.index'))

    # Get all employees
    employees = User.query.all()

    return render_template('reports/employee_detail.html', title='تقرير تفاصيل الموظف', employees=employees)

@report_bp.route('/export/<report_type>', methods=['POST'])
@login_required
def export(report_type):
    # Check if user has permission to export reports
    if not (current_user.has_role('admin') or current_user.has_role('manager')):
        flash('ليس لديك صلاحية لتصدير التقارير', 'danger')
        return redirect(url_for('dashboard.index'))

    # Get export format
    export_format = request.form.get('export_format', 'pdf')

    # Get report parameters
    parameters = {}
    for key, value in request.form.items():
        if key != 'export_format' and key != 'csrf_token':
            parameters[key] = value

    # Generate report data based on report type
    if report_type == 'employees':
        data = generate_employees_report(parameters)
        title = 'تقرير الموظفين'
    elif report_type == 'departments':
        data = generate_departments_report(parameters)
        title = 'تقرير الأقسام'
    elif report_type == 'leave':
        data = generate_leave_report(parameters)
        title = 'تقرير الإجازات'
    elif report_type == 'meetings':
        data = generate_meetings_report(parameters)
        title = 'تقرير الاجتماعات'
    elif report_type == 'penalties':
        data = generate_penalties_report(parameters)
        title = 'تقرير العقوبات'
    elif report_type == 'clients':
        data = generate_clients_report(parameters)
        title = 'تقرير العملاء'
    elif report_type == 'transactions':
        data = generate_transactions_report(parameters)
        title = 'تقرير المعاملات المالية'
    elif report_type == 'invoices':
        data = generate_invoices_report(parameters)
        title = 'تقرير الفواتير'
    elif report_type == 'employee_salaries':
        data = generate_employee_salaries_report(parameters)
        title = 'تقرير رواتب الموظفين'
    elif report_type == 'accounts_tree':
        data = generate_accounts_tree_report(parameters)
        title = 'تقرير شجرة الحسابات'
    elif report_type == 'employee_detail':
        data = generate_employee_detail_report(parameters)
        title = 'تقرير تفاصيل الموظف'
    else:
        flash('نوع التقرير غير صالح', 'danger')
        return redirect(url_for('report.index'))

    # Generate report file
    if export_format == 'pdf':
        return export_to_pdf(data, title, parameters, report_type)
    else:  # excel
        return export_to_excel(data, title, parameters, report_type)

def generate_employees_report(parameters):
    # Get filter parameters
    department_id = parameters.get('department_id')
    date_from = parameters.get('date_from')
    date_to = parameters.get('date_to')

    # Build query - only active employees
    query = User.query.filter(User.is_active == True)

    if department_id and department_id != 'all':
        query = query.filter_by(department_id=department_id)

    # Get employees
    employees = query.order_by(User.first_name, User.last_name).all()

    # Prepare data for report
    data = {
        'headers': ['#', 'الاسم', 'الأقسام', 'إجمالي المشاريع', 'المشاريع المكتملة', 'المشاريع الجارية', 'المشاريع المعلقة', 'تاريخ الانضمام'],
        'rows': []
    }

    for i, employee in enumerate(employees, 1):
        # Get project statistics
        from app.models.project import Project
        projects_query = Project.query.join(Project.members).filter(User.id == employee.id)

        # Apply date filter if provided
        if date_from or date_to:
            if date_from:
                date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
                projects_query = projects_query.filter(Project.created_at >= date_from_obj)
            if date_to:
                date_to_obj = datetime.strptime(date_to, '%Y-%m-%d')
                projects_query = projects_query.filter(Project.created_at <= date_to_obj)

        total_projects = projects_query.count()
        completed_projects = projects_query.filter(Project.status == 'completed').count()
        in_progress_projects = projects_query.filter(Project.status == 'in_progress').count()
        pending_projects = projects_query.filter(Project.status == 'pending').count()

        # Get all departments for employee
        departments = []
        if employee.department:
            departments.append(employee.department.name)
        departments_str = ', '.join(departments) if departments else 'غير محدد'

        data['rows'].append([
            i,
            employee.get_full_name(),
            departments_str,
            total_projects,
            completed_projects,
            in_progress_projects,
            pending_projects,
            employee.date_joined.strftime('%Y-%m-%d') if employee.date_joined else ''
        ])

    return data

def generate_departments_report(parameters):
    # Get filter parameters
    date_from = parameters.get('date_from')
    date_to = parameters.get('date_to')

    # Build query
    query = Department.query

    # Get all departments
    departments = query.all()

    # Prepare data for report
    data = {
        'headers': ['#', 'اسم القسم', 'الوصف', 'عدد الموظفين', 'إجمالي المشاريع', 'المشاريع المكتملة', 'المشاريع الجارية', 'المشاريع المعلقة', 'رئيس القسم'],
        'rows': []
    }

    for i, department in enumerate(departments, 1):
        # Count employees in this department
        from app.models.user import User
        employee_count = User.query.filter_by(department_id=department.id, is_active=True).count()

        # Get project statistics for department employees
        from app.models.project import Project
        department_employees = User.query.filter_by(department_id=department.id, is_active=True).all()

        # Count projects involving department employees
        projects_query = Project.query.join(Project.members).filter(User.department_id == department.id)

        # Apply date filter if provided
        if date_from or date_to:
            if date_from:
                date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
                projects_query = projects_query.filter(Project.created_at >= date_from_obj)
            if date_to:
                date_to_obj = datetime.strptime(date_to, '%Y-%m-%d')
                projects_query = projects_query.filter(Project.created_at <= date_to_obj)

        total_projects = projects_query.distinct().count()
        completed_projects = projects_query.filter(Project.status == 'completed').distinct().count()
        in_progress_projects = projects_query.filter(Project.status == 'in_progress').distinct().count()
        pending_projects = projects_query.filter(Project.status == 'pending').distinct().count()

        head = department.head.get_full_name() if department.head else 'غير محدد'

        data['rows'].append([
            i,
            department.name,
            department.description or 'لا يوجد وصف',
            employee_count,
            total_projects,
            completed_projects,
            in_progress_projects,
            pending_projects,
            head
        ])

    return data

def generate_leave_report(parameters):
    # Get filter parameters
    employee_id = parameters.get('employee_id')
    status = parameters.get('status')
    start_date = parameters.get('start_date')
    end_date = parameters.get('end_date')

    # Build query
    query = LeaveRequest.query

    if employee_id and employee_id != 'all':
        query = query.filter_by(user_id=employee_id)

    if status and status != 'all':
        query = query.filter_by(status=status)

    if start_date:
        query = query.filter(LeaveRequest.start_date >= datetime.strptime(start_date, '%Y-%m-%d').date())

    if end_date:
        query = query.filter(LeaveRequest.end_date <= datetime.strptime(end_date, '%Y-%m-%d').date())

    # Get leave requests
    leave_requests = query.order_by(LeaveRequest.created_at.desc()).all()

    # Prepare data for report
    data = {
        'headers': ['#', 'الموظف', 'تاريخ البداية', 'تاريخ النهاية', 'المدة', 'السبب', 'الحالة', 'تمت الموافقة بواسطة'],
        'rows': []
    }

    for i, leave in enumerate(leave_requests, 1):
        duration = (leave.end_date - leave.start_date).days + 1
        status_map = {
            'pending': 'قيد الانتظار',
            'approved': 'تمت الموافقة',
            'rejected': 'مرفوض'
        }
        status = status_map.get(leave.status, leave.status)

        # Get reviewer name
        reviewer_name = 'غير محدد'
        if leave.reviewed_by_id and leave.status != 'pending':
            from app.models.user import User
            reviewer = User.query.get(leave.reviewed_by_id)
            if reviewer:
                reviewer_name = reviewer.get_full_name()

        data['rows'].append([
            i,
            leave.user.get_full_name(),
            leave.start_date.strftime('%Y-%m-%d'),
            leave.end_date.strftime('%Y-%m-%d'),
            f'{duration} يوم',
            leave.reason,
            status,
            reviewer_name
        ])

    return data

def generate_meetings_report(parameters):
    # Get filter parameters
    employee_id = parameters.get('employee_id')
    client_id = parameters.get('client_id')
    start_date = parameters.get('start_date')
    end_date = parameters.get('end_date')

    # Build query
    query = Meeting.query

    if employee_id and employee_id != 'all':
        query = query.join(Meeting.attendees).filter(User.id == employee_id)

    if client_id and client_id != 'all':
        query = query.join(Meeting.clients).filter(Client.id == client_id)

    if start_date:
        query = query.filter(Meeting.date >= datetime.strptime(start_date, '%Y-%m-%d').date())

    if end_date:
        query = query.filter(Meeting.date <= datetime.strptime(end_date, '%Y-%m-%d').date())

    # Get meetings
    meetings = query.order_by(Meeting.date.desc()).all()

    # Prepare data for report
    data = {
        'headers': ['#', 'العنوان', 'التاريخ', 'الوقت', 'المكان', 'الحاضرون', 'رابط خارجي'],
        'rows': []
    }

    for i, meeting in enumerate(meetings, 1):
        attendees = ', '.join([attendee.get_full_name() for attendee in meeting.attendees])
        clients = ', '.join([client.name for client in meeting.clients])

        # Format meeting time in 12-hour format
        meeting_time = 'غير محدد'
        if hasattr(meeting, 'start_time') and meeting.start_time and hasattr(meeting, 'end_time') and meeting.end_time:
            start_time = meeting.start_time.strftime('%I:%M %p')
            end_time = meeting.end_time.strftime('%I:%M %p')
            meeting_time = f"{start_time} - {end_time}"
        elif hasattr(meeting, 'meeting_time') and meeting.meeting_time:
            meeting_time = meeting.meeting_time.strftime('%I:%M %p')

        # Format attendees with better formatting for export
        attendees_list = []
        if attendees:
            attendees_list.append(f"الموظفون:\n• {attendees.replace(', ', '\n• ')}")
        if clients:
            attendees_list.append(f"العملاء:\n• {clients.replace(', ', '\n• ')}")
        if meeting.external_attendees:
            external_attendees = meeting.external_attendees.replace(',', '\n• ')
            attendees_list.append(f"الحاضرون الخارجيون:\n• {external_attendees}")

        attendees_display = '\n\n'.join(attendees_list) if attendees_list else 'لا يوجد حاضرون'

        # Format external link
        external_link = meeting.external_link or 'لا يوجد'

        data['rows'].append([
            i,
            meeting.title,
            meeting.date.strftime('%Y-%m-%d'),
            meeting_time,
            meeting.location or 'غير محدد',
            attendees_display,
            external_link
        ])

    return data

def generate_penalties_report(parameters):
    # Get filter parameters
    employee_id = parameters.get('employee_id')
    penalty_type = parameters.get('penalty_type')
    start_date = parameters.get('start_date')
    end_date = parameters.get('end_date')

    # Build query
    query = Penalty.query

    if employee_id and employee_id != 'all':
        query = query.filter_by(user_id=employee_id)

    if penalty_type and penalty_type != 'all':
        query = query.filter_by(penalty_type=penalty_type)

    if start_date:
        query = query.filter(Penalty.start_date >= datetime.strptime(start_date, '%Y-%m-%d').date())

    if end_date:
        query = query.filter(Penalty.start_date <= datetime.strptime(end_date, '%Y-%m-%d').date())

    # Get penalties
    penalties = query.order_by(Penalty.created_at.desc()).all()

    # Prepare data for report
    data = {
        'headers': ['#', 'الموظف', 'نوع العقوبة', 'السبب', 'تاريخ البداية', 'تاريخ النهاية', 'الخصم من الراتب', 'صادرة بواسطة'],
        'rows': []
    }

    for i, penalty in enumerate(penalties, 1):
        end_date = penalty.end_date.strftime('%Y-%m-%d') if penalty.end_date else 'غير محدد'
        salary_deduction = f'${penalty.salary_deduction}' if penalty.salary_deduction else 'لا يوجد'

        # Get issuer name
        issuer_name = 'غير محدد'
        if penalty.issued_by_id:
            issuer_name = penalty.issued_by.get_full_name()

        data['rows'].append([
            i,
            penalty.user.get_full_name(),
            penalty.get_penalty_type_display(),
            penalty.reason,
            penalty.start_date.strftime('%Y-%m-%d'),
            end_date,
            salary_deduction,
            issuer_name
        ])

    return data

def generate_clients_report(parameters):
    # Get filter parameters
    date_from = parameters.get('date_from')
    date_to = parameters.get('date_to')
    search = parameters.get('search', '')

    # Build query
    query = Client.query

    # Apply search filter if provided
    if search:
        search_term = f"%{search}%"
        query = query.filter(
            (Client.name.like(search_term)) |
            (Client.email.like(search_term))
        )

    # Get all clients
    clients = query.all()

    # Prepare data for report
    data = {
        'headers': ['#', 'اسم العميل', 'البريد الإلكتروني', 'رقم الهاتف', 'العنوان', 'إجمالي المشاريع', 'المشاريع المكتملة', 'المشاريع الجارية', 'المشاريع المعلقة'],
        'rows': []
    }

    for i, client in enumerate(clients, 1):
        # Get project statistics for client
        projects_query = client.projects

        # Apply date filter if provided
        if date_from or date_to:
            from app.models.project import Project
            projects_query = Project.query.filter(Project.client_id == client.id)
            if date_from:
                date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
                projects_query = projects_query.filter(Project.created_at >= date_from_obj)
            if date_to:
                date_to_obj = datetime.strptime(date_to, '%Y-%m-%d')
                projects_query = projects_query.filter(Project.created_at <= date_to_obj)

            total_projects = projects_query.count()
            completed_projects = projects_query.filter(Project.status == 'completed').count()
            in_progress_projects = projects_query.filter(Project.status == 'in_progress').count()
            pending_projects = projects_query.filter(Project.status == 'pending').count()
        else:
            total_projects = len(client.projects)
            completed_projects = len([p for p in client.projects if p.status == 'completed'])
            in_progress_projects = len([p for p in client.projects if p.status == 'in_progress'])
            pending_projects = len([p for p in client.projects if p.status == 'pending'])

        data['rows'].append([
            i,
            client.name,
            client.email or 'غير محدد',
            client.phone or 'غير محدد',
            client.address or 'غير محدد',
            total_projects,
            completed_projects,
            in_progress_projects,
            pending_projects
        ])

    return data

def generate_employee_salaries_report(parameters):
    # Get filter parameters
    employee_id = parameters.get('employee_id')
    status = parameters.get('status')
    date_from = parameters.get('date_from')
    date_to = parameters.get('date_to')

    # Build query
    from app.models.finance import EmployeeSalary
    query = EmployeeSalary.query

    if employee_id and employee_id != 'all':
        query = query.filter_by(employee_id=employee_id)

    if status and status != 'all':
        query = query.filter_by(status=status)

    if date_from:
        date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
        query = query.filter(EmployeeSalary.created_at >= date_from_obj)

    if date_to:
        date_to_obj = datetime.strptime(date_to, '%Y-%m-%d')
        query = query.filter(EmployeeSalary.created_at <= date_to_obj)

    # Get salaries
    salaries = query.order_by(EmployeeSalary.created_at.desc()).all()

    # Prepare data for report
    data = {
        'headers': ['#', 'الموظف', 'الشهر', 'السنة', 'الراتب الأساسي', 'البدلات', 'الخصومات', 'صافي الراتب', 'الحالة', 'تاريخ الدفع'],
        'rows': []
    }

    for i, salary in enumerate(salaries, 1):
        # Format status
        status_display = {
            'pending': 'قيد الانتظار',
            'paid': 'مدفوع',
            'cancelled': 'ملغي'
        }.get(salary.status, salary.status)

        data['rows'].append([
            i,
            salary.employee.get_full_name(),
            salary.month,
            salary.year,
            f"${salary.base_salary:.2f}",
            f"${(salary.allowances or 0):.2f}",
            f"${(salary.deductions or 0):.2f}",
            f"${salary.net_salary:.2f}",
            status_display,
            salary.payment_date.strftime('%Y-%m-%d') if salary.payment_date else 'غير محدد'
        ])

    return data

def generate_accounts_tree_report(parameters):
    # Get filter parameters
    folder_id = parameters.get('folder_id')
    include_balances = parameters.get('include_balances', 'yes')

    # Build query
    from app.models.finance import AccountFolder, Account

    if folder_id and folder_id != 'all':
        folders = AccountFolder.query.filter_by(id=folder_id).all()
    else:
        folders = AccountFolder.query.all()

    # Prepare data for report
    data = {
        'headers': ['مجلد الحساب', 'اسم الحساب', 'نوع الحساب', 'الرصيد الحالي', 'حالة الحساب'],
        'rows': []
    }

    row_index = 1
    for folder in folders:
        for account in folder.accounts:
            # Format account type
            account_type_display = {
                'asset': 'أصل',
                'liability': 'التزام',
                'equity': 'حقوق ملكية',
                'revenue': 'إيراد',
                'expense': 'مصروف'
            }.get(account.account_type, account.account_type)

            # Format balance
            balance_display = f"${account.balance:.2f}" if include_balances == 'yes' else 'مخفي'

            # Format status
            status_display = 'نشط' if account.is_active else 'غير نشط'

            data['rows'].append([
                folder.name,
                account.name,
                account_type_display,
                balance_display,
                status_display
            ])
            row_index += 1

    return data

def generate_transactions_report(parameters):
    # Get filter parameters
    status_filter = parameters.get('status')
    date_from = parameters.get('date_from')
    date_to = parameters.get('date_to')

    # Build query for new financial transactions system
    from app.models.finance import FinancialTransaction
    query = FinancialTransaction.query

    if status_filter and status_filter != 'all':
        query = query.filter_by(status=status_filter)

    if date_from:
        date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
        query = query.filter(FinancialTransaction.created_at >= date_from_obj)

    if date_to:
        date_to_obj = datetime.strptime(date_to, '%Y-%m-%d')
        query = query.filter(FinancialTransaction.created_at <= date_to_obj)

    # Get transactions
    transactions = query.order_by(FinancialTransaction.created_at.desc()).all()

    # Prepare data for report
    data = {
        'headers': ['#', 'رقم المعاملة', 'اسم المعاملة', 'حالة المعاملة', 'عدد البنود', 'تاريخ الإنشاء', 'آخر تحديث'],
        'rows': []
    }

    for i, transaction in enumerate(transactions, 1):
        # Format status
        status_display = {
            'draft': 'مسودة',
            'pending': 'معلقة',
            'paid': 'مدفوعة',
            'cancelled': 'ملغية'
        }.get(transaction.status, transaction.status)

        # Count transaction items
        items_count = transaction.items.count() if transaction.items else 0

        data['rows'].append([
            i,
            transaction.transaction_number or 'غير محدد',
            transaction.name or 'غير محدد',
            status_display,
            items_count,
            transaction.created_at.strftime('%Y-%m-%d %H:%M') if transaction.created_at else 'غير محدد',
            transaction.updated_at.strftime('%Y-%m-%d %H:%M') if transaction.updated_at else 'غير محدد'
        ])

    return data

def generate_invoices_report(parameters):
    # Get filter parameters
    status = parameters.get('status')
    client_id = parameters.get('client_id')
    project_id = parameters.get('project_id')
    start_date = parameters.get('start_date')
    end_date = parameters.get('end_date')

    # Build query
    query = Invoice.query

    if status and status != 'all':
        query = query.filter_by(status=status)

    if client_id and client_id != 'all':
        query = query.filter_by(client_id=client_id)

    if project_id and project_id != 'all':
        query = query.filter_by(project_id=project_id)

    if start_date:
        query = query.filter(Invoice.issue_date >= datetime.strptime(start_date, '%Y-%m-%d').date())

    if end_date:
        query = query.filter(Invoice.issue_date <= datetime.strptime(end_date, '%Y-%m-%d').date())

    # Get invoices
    invoices = query.order_by(Invoice.issue_date.desc()).all()

    # Prepare data for report
    data = {
        'headers': ['#', 'رقم الفاتورة', 'العميل', 'المشروع', 'تاريخ الإصدار', 'تاريخ الاستحقاق', 'المبلغ الإجمالي', 'الحالة'],
        'rows': []
    }

    for i, invoice in enumerate(invoices, 1):
        # Get client name
        client_name = 'غير محدد'
        if invoice.client:
            client_name = invoice.client.name

        # Get project name
        project_name = 'غير محدد'
        if invoice.project:
            project_name = invoice.project.name

        # Format status
        status_map = {
            'draft': 'مسودة',
            'pending': 'قيد الانتظار',
            'paid': 'مدفوعة',
            'overdue': 'متأخرة',
            'cancelled': 'ملغاة'
        }
        status = status_map.get(invoice.status, invoice.status)

        # Format total amount
        total_amount = f"${invoice.calculate_total():.2f}"

        data['rows'].append([
            i,
            invoice.invoice_number,
            client_name,
            project_name,
            invoice.issue_date.strftime('%Y-%m-%d'),
            invoice.due_date.strftime('%Y-%m-%d') if invoice.due_date else 'غير محدد',
            total_amount,
            status
        ])

    return data

def generate_employee_detail_report(parameters):
    # Get employee ID
    employee_id = parameters.get('employee_id')

    if not employee_id:
        return {
            'headers': ['خطأ'],
            'rows': [['لم يتم تحديد الموظف']],
            'is_employee_detail': True,
            'employee': None
        }

    # Get employee
    employee = User.query.get(employee_id)

    if not employee:
        return {
            'headers': ['خطأ'],
            'rows': [['الموظف غير موجود']],
            'is_employee_detail': True,
            'employee': None
        }

    # Calculate project statistics
    projects = employee.projects
    total_projects = len(projects)
    completed_projects = sum(1 for p in projects if p.status == 'completed')
    in_progress_projects = sum(1 for p in projects if p.status == 'in_progress')
    pending_projects = sum(1 for p in projects if p.status == 'pending')

    # Get tasks assigned to this employee
    tasks = employee.tasks.all()
    total_tasks = len(tasks)
    completed_tasks = sum(1 for t in tasks if t.status == 'completed')
    in_progress_tasks = sum(1 for t in tasks if t.status == 'in_progress')
    pending_tasks = sum(1 for t in tasks if t.status == 'pending')

    # Get ID documents
    id_documents = employee.id_documents.all()

    # Check if employee is currently on leave
    from app.models.leave import LeaveRequest
    active_leave = LeaveRequest.query.filter(
        LeaveRequest.user_id == employee.id,
        LeaveRequest.status == 'approved',
        LeaveRequest.start_date <= datetime.now().date(),
        LeaveRequest.end_date >= datetime.now().date()
    ).first()

    # Prepare data for report - this is a special format for the employee detail report
    data = {
        'headers': ['معلومات الموظف'],
        'rows': [],
        'is_employee_detail': True,  # Special flag for the employee detail report
        'employee': employee,
        'projects': projects[:5],  # Limit to 5 recent projects
        'tasks': tasks[:5],  # Limit to 5 recent tasks
        'id_documents': id_documents,
        'active_leave': active_leave,
        'statistics': {
            'total_projects': total_projects,
            'completed_projects': completed_projects,
            'in_progress_projects': in_progress_projects,
            'pending_projects': pending_projects,
            'total_tasks': total_tasks,
            'completed_tasks': completed_tasks,
            'in_progress_tasks': in_progress_tasks,
            'pending_tasks': pending_tasks
        }
    }

    return data

def export_to_pdf(data, title, parameters, report_type):
    # Check if this is an employee detail report
    if report_type == 'employee_detail' and data.get('is_employee_detail'):
        return export_employee_detail_to_pdf(data, title, parameters)

    # Create HTML content for PDF
    html_content = f'''
    <!DOCTYPE html>
    <html dir="rtl">
    <head>
        <meta charset="UTF-8">
        <title>{title}</title>
        <style>
            @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap');

            body {{
                font-family: 'Tajawal', 'Arial', sans-serif;
                direction: rtl;
                text-align: right;
                margin: 0;
                padding: 0;
                background-color: #ffffff;
                color: #303030;
                width: 210mm; /* A4 width */
                min-height: 297mm; /* A4 height */
                margin: 0 auto;
            }}

            .report-container {{
                width: 100%;
                margin: 0 auto;
                background-color: #ffffff;
                padding: 20mm 15mm; /* Margins for A4 */
            }}

            .report-header {{
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 30px;
                padding-bottom: 20px;
                border-bottom: 2px solid #EABF54;
            }}

            .logo {{
                max-width: 120px;
                height: auto;
            }}

            .title-container {{
                text-align: right;
            }}

            h1 {{
                color: #1765A0;
                margin: 0;
                font-size: 32px;
                font-weight: 700;
            }}

            table {{
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 30px;
                page-break-inside: auto;
            }}

            tr {{
                page-break-inside: avoid;
                page-break-after: auto;
            }}

            thead {{
                display: table-header-group;
            }}

            tfoot {{
                display: table-footer-group;
            }}

            th, td {{
                border: 1px solid #e0e0e0;
                padding: 8px;
                text-align: right;
            }}

            th {{
                background-color: #1765A0;
                color: white;
                font-weight: 500;
            }}

            tr:nth-child(even) {{
                background-color: #f9f9f9;
            }}

            .report-info {{
                margin-bottom: 30px;
                padding: 15px;
                background-color: #f9f9f9;
                border-radius: 4px;
                border: 1px solid #e0e0e0;
            }}

            .report-info p {{
                margin: 8px 0;
                font-size: 14px;
            }}

            .report-info strong {{
                color: #1765A0;
            }}

            .footer {{
                text-align: center;
                margin-top: 40px;
                padding-top: 20px;
                border-top: 1px solid #e0e0e0;
                font-size: 12px;
                color: #777;
                page-break-inside: avoid;
            }}

            @page {{
                size: A4;
                margin: 0;
            }}

            @media print {{
                html, body {{
                    width: 210mm;
                    height: 297mm;
                }}
                .report-container {{
                    margin: 0;
                    border: initial;
                    border-radius: initial;
                    width: initial;
                    min-height: initial;
                    box-shadow: initial;
                    background: initial;
                    page-break-after: always;
                }}
            }}
        </style>
    </head>
    <body>
        <div class="report-container">
            <div class="report-header">
                <div class="title-container">
                    <h1>{title}</h1>
                </div>
                <img src="data:image/png;base64,{get_logo_base64()}" class="logo" alt="Sparkle Media Agency">
            </div>

            <div class="report-info">
                <p><strong>تاريخ التقرير:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M')}</p>
                <p><strong>تم إنشاؤه بواسطة:</strong> {current_user.get_full_name()}</p>
    '''

    # Add filter information based on report type
    if report_type == 'employees':
        department_id = parameters.get('department_id', 'all')
        status = parameters.get('status', 'all')

        department_name = 'الكل'
        if department_id != 'all':
            department = Department.query.get(department_id)
            if department:
                department_name = department.name

        status_name = 'الكل'
        if status == 'active':
            status_name = 'نشط'
        elif status == 'inactive':
            status_name = 'غير نشط'

        html_content += f'''
            <p><strong>القسم:</strong> {department_name}</p>
            <p><strong>الحالة:</strong> {status_name}</p>
        '''

    elif report_type in ['leave', 'meetings', 'penalties']:
        start_date = parameters.get('start_date', '')
        end_date = parameters.get('end_date', '')

        if start_date:
            html_content += f'<p><strong>من تاريخ:</strong> {start_date}</p>'

        if end_date:
            html_content += f'<p><strong>إلى تاريخ:</strong> {end_date}</p>'

    html_content += '''
        </div>

        <table>
            <thead>
                <tr>
    '''

    # Add table headers
    for header in data['headers']:
        html_content += f'<th>{header}</th>'

    html_content += '''
                </tr>
            </thead>
            <tbody>
    '''

    # Add table rows
    for row in data['rows']:
        html_content += '<tr>'
        for cell in row:
            html_content += f'<td>{cell}</td>'
        html_content += '</tr>'

    html_content += '''
            </tbody>
        </table>

        <div class="footer">
            <p>© 2025 Sparkle Media Agency. جميع الحقوق محفوظة.</p>
            <p>تم إنشاء هذا التقرير بواسطة نظام إدارة شركة Sparkle Media Agency</p>
        </div>
        </div>
    </body>
    </html>
    '''

    # Instead of generating a PDF, we'll create an HTML file
    with tempfile.NamedTemporaryFile(suffix='.html', delete=False, mode='w', encoding='utf-8') as temp_file:
        temp_file.write(html_content)
        html_path = temp_file.name

    # For now, we'll just return the HTML file
    pdf_path = html_path

    # Save report to database
    report = Report(
        title=title,
        description=f'تم إنشاؤه في {datetime.now().strftime("%Y-%m-%d %H:%M")}',
        report_type=report_type,
        parameters=json.dumps(parameters),
        file_path=pdf_path,
        created_by_id=current_user.id
    )

    db.session.add(report)
    db.session.commit()

    # Send the file
    return send_file(
        pdf_path,
        as_attachment=True,
        download_name=f'{title}_{datetime.now().strftime("%Y%m%d_%H%M")}.html',
        mimetype='text/html'
    )

def export_employee_detail_to_pdf(data, title, parameters):
    """
    Generate a CV-like PDF report for an employee
    """
    employee = data.get('employee')

    if not employee:
        # Return error report if employee not found
        html_content = f'''
        <!DOCTYPE html>
        <html dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>{title}</title>
            <style>
                body {{ font-family: 'Arial', sans-serif; direction: rtl; text-align: center; padding: 50px; }}
                .error {{ color: red; font-size: 24px; margin-bottom: 20px; }}
            </style>
        </head>
        <body>
            <div class="error">خطأ: لم يتم العثور على الموظف</div>
            <p>يرجى العودة واختيار موظف صالح.</p>
        </body>
        </html>
        '''

        # Create HTML file
        with tempfile.NamedTemporaryFile(suffix='.html', delete=False, mode='w', encoding='utf-8') as temp_file:
            temp_file.write(html_content)
            html_path = temp_file.name

        # Save report to database
        report = Report(
            title=title,
            description=f'تم إنشاؤه في {datetime.now().strftime("%Y-%m-%d %H:%M")}',
            report_type='employee_detail',
            parameters=json.dumps(parameters),
            file_path=html_path,
            created_by_id=current_user.id
        )

        db.session.add(report)
        db.session.commit()

        return send_file(
            html_path,
            as_attachment=True,
            download_name=f'{title}_{datetime.now().strftime("%Y%m%d_%H%M")}.html',
            mimetype='text/html'
        )

    # Get employee data
    statistics = data.get('statistics', {})
    projects = data.get('projects', [])
    tasks = data.get('tasks', [])
    id_documents = data.get('id_documents', [])
    active_leave = data.get('active_leave')

    # Create HTML content for the employee detail report
    html_content = f'''
    <!DOCTYPE html>
    <html dir="rtl">
    <head>
        <meta charset="UTF-8">
        <title>تقرير تفاصيل الموظف - {employee.get_full_name()}</title>
        <style>
            @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap');

            body {{
                font-family: 'Tajawal', 'Arial', sans-serif;
                direction: rtl;
                text-align: right;
                margin: 0;
                padding: 0;
                background-color: #ffffff;
                color: #303030;
                width: 210mm; /* A4 width */
                min-height: 297mm; /* A4 height */
                margin: 0 auto;
            }}

            .report-container {{
                width: 100%;
                margin: 0 auto;
                background-color: #ffffff;
                padding: 20mm 15mm; /* Margins for A4 */
            }}

            .report-header {{
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 30px;
                padding-bottom: 20px;
                border-bottom: 2px solid #EABF54;
            }}

            .logo {{
                max-width: 120px;
                height: auto;
            }}

            .title-container {{
                text-align: right;
            }}

            h1 {{
                color: #1765A0;
                margin: 0;
                font-size: 32px;
                font-weight: 700;
            }}

            h2 {{
                color: #1765A0;
                margin: 20px 0 10px 0;
                font-size: 24px;
                font-weight: 600;
                border-bottom: 1px solid #EABF54;
                padding-bottom: 5px;
            }}

            h3 {{
                color: #333;
                margin: 15px 0 10px 0;
                font-size: 18px;
                font-weight: 600;
            }}

            .employee-profile {{
                display: flex;
                margin-bottom: 30px;
                background-color: #f9f9f9;
                border-radius: 8px;
                padding: 20px;
                box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            }}

            .profile-image {{
                width: 150px;
                height: 150px;
                border-radius: 50%;
                object-fit: cover;
                border: 3px solid #1765A0;
                margin-left: 20px;
            }}

            .profile-info {{
                flex: 1;
            }}

            .profile-name {{
                font-size: 28px;
                font-weight: 700;
                color: #1765A0;
                margin-bottom: 5px;
            }}

            .profile-title {{
                font-size: 18px;
                color: #666;
                margin-bottom: 15px;
            }}

            .profile-details {{
                margin-bottom: 15px;
            }}

            .profile-details p {{
                margin: 5px 0;
                display: flex;
                align-items: center;
            }}

            .profile-details i {{
                width: 20px;
                margin-left: 10px;
                color: #1765A0;
            }}

            .badge {{
                display: inline-block;
                padding: 5px 10px;
                border-radius: 4px;
                font-size: 14px;
                font-weight: 500;
                margin-left: 5px;
                color: white;
                background-color: #1765A0;
            }}

            .badge-success {{
                background-color: #28a745;
            }}

            .badge-danger {{
                background-color: #dc3545;
            }}

            .badge-warning {{
                background-color: #ffc107;
                color: #212529;
            }}

            .stats-container {{
                display: flex;
                flex-wrap: wrap;
                margin-bottom: 30px;
                justify-content: space-between;
            }}

            .stat-box {{
                width: 22%;
                background-color: #f9f9f9;
                border-radius: 8px;
                padding: 15px;
                margin-bottom: 15px;
                box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                text-align: center;
            }}

            .stat-value {{
                font-size: 24px;
                font-weight: 700;
                color: #1765A0;
                margin-bottom: 5px;
            }}

            .stat-label {{
                font-size: 14px;
                color: #666;
            }}

            .section {{
                margin-bottom: 30px;
                background-color: #f9f9f9;
                border-radius: 8px;
                padding: 20px;
                box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            }}

            table {{
                width: 100%;
                border-collapse: collapse;
                margin-top: 15px;
            }}

            th, td {{
                border: 1px solid #e0e0e0;
                padding: 10px;
                text-align: right;
            }}

            th {{
                background-color: #1765A0;
                color: white;
                font-weight: 500;
            }}

            tr:nth-child(even) {{
                background-color: #f2f2f2;
            }}

            .cv-content {{
                white-space: pre-line;
                line-height: 1.6;
            }}

            .footer {{
                text-align: center;
                margin-top: 40px;
                padding-top: 20px;
                border-top: 1px solid #e0e0e0;
                font-size: 12px;
                color: #777;
            }}

            @page {{
                size: A4;
                margin: 0;
            }}

            @media print {{
                html, body {{
                    width: 210mm;
                    height: 297mm;
                }}
                .report-container {{
                    margin: 0;
                    border: initial;
                    border-radius: initial;
                    width: initial;
                    min-height: initial;
                    box-shadow: initial;
                    background: initial;
                    page-break-after: always;
                }}
            }}
        </style>
    </head>
    <body>
        <div class="report-container">
            <div class="report-header">
                <div class="title-container">
                    <h1>تقرير تفاصيل الموظف</h1>
                </div>
                <img src="data:image/png;base64,{get_logo_base64()}" class="logo" alt="Sparkle Media Agency">
            </div>

            <!-- Employee Profile -->
            <div class="employee-profile">
                <div class="profile-image-container">
                    {f"""<img src="data:image/jpeg;base64,{base64.b64encode(employee.profile_image_data).decode('utf-8')}" alt="{employee.get_full_name()}" class="profile-image">""" if employee.profile_image_data else
                    f"""<img src="data:image/png;base64,{get_default_profile_image_base64()}" alt="{employee.get_full_name()}" class="profile-image">"""}
                </div>
                <div class="profile-info">
                    <div class="profile-name">{employee.get_full_name()}</div>
                    <div class="profile-title">
                        {employee.department.name if employee.department else 'غير محدد'} |
                        {', '.join([role.name for role in employee.roles])}
                    </div>
                    <div class="profile-details">
                        <p><i class="fas fa-envelope"></i> {employee.email}</p>
                        {f'<p><i class="fas fa-phone"></i> {employee.phone}</p>' if employee.phone else ''}
                        {f'<p><i class="fas fa-birthday-cake"></i> تاريخ الميلاد: {employee.birth_date.strftime("%Y-%m-%d") if employee.birth_date else "غير محدد"}</p>' if employee.birth_date else ''}
                        {f'<p><i class="fas fa-flag"></i> الجنسية: {employee.nationality}</p>' if employee.nationality else ''}
                        <p><i class="fas fa-calendar-alt"></i> تاريخ الانضمام: {employee.date_joined.strftime('%Y-%m-%d')}</p>
                    </div>
                    <div>
                        <span class="badge {'badge-success' if employee.is_active else 'badge-danger'}">
                            {'نشط' if employee.is_active else 'غير نشط'}
                        </span>
                        {f'<span class="badge badge-warning">في إجازة</span>' if active_leave else ''}
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <h2>إحصائيات الموظف</h2>
            <div class="stats-container">
                <div class="stat-box">
                    <div class="stat-value">{statistics.get('total_projects', 0)}</div>
                    <div class="stat-label">إجمالي المشاريع</div>
                </div>
                <div class="stat-box">
                    <div class="stat-value">{statistics.get('completed_projects', 0)}</div>
                    <div class="stat-label">المشاريع المكتملة</div>
                </div>
                <div class="stat-box">
                    <div class="stat-value">{statistics.get('in_progress_projects', 0)}</div>
                    <div class="stat-label">المشاريع الجارية</div>
                </div>
                <div class="stat-box">
                    <div class="stat-value">{statistics.get('pending_projects', 0)}</div>
                    <div class="stat-label">المشاريع المعلقة</div>
                </div>
                <div class="stat-box">
                    <div class="stat-value">{statistics.get('total_tasks', 0)}</div>
                    <div class="stat-label">إجمالي المهام</div>
                </div>
                <div class="stat-box">
                    <div class="stat-value">{statistics.get('completed_tasks', 0)}</div>
                    <div class="stat-label">المهام المكتملة</div>
                </div>
                <div class="stat-box">
                    <div class="stat-value">{statistics.get('in_progress_tasks', 0)}</div>
                    <div class="stat-label">المهام الجارية</div>
                </div>
                <div class="stat-box">
                    <div class="stat-value">{statistics.get('pending_tasks', 0)}</div>
                    <div class="stat-label">المهام المعلقة</div>
                </div>
            </div>

            <!-- CV/Resume Section -->
            <h2>السيرة الذاتية</h2>
            <div class="section">
                <div class="cv-content">
                    {employee.cv if employee.cv else 'لا توجد سيرة ذاتية متاحة.'}
                </div>
            </div>

            <!-- ID Documents Section -->
            <h2>الاثباتات الوطنية</h2>
            <div class="section">
                {f"""
                <table>
                    <thead>
                        <tr>
                            <th>نوع الوثيقة</th>
                            <th>الرقم</th>
                            <th>تاريخ الإصدار</th>
                            <th>تاريخ الانتهاء</th>
                            <th>بلد الإصدار</th>
                        </tr>
                    </thead>
                    <tbody>
                        {''.join([f"""
                        <tr>
                            <td>{doc.document_type}</td>
                            <td>{doc.document_number}</td>
                            <td>{doc.issue_date.strftime('%Y-%m-%d') if doc.issue_date else 'غير محدد'}</td>
                            <td>{doc.expiry_date.strftime('%Y-%m-%d') if doc.expiry_date else 'غير محدد'}</td>
                            <td>{doc.issuing_country}</td>
                        </tr>
                        """ for doc in id_documents])}
                    </tbody>
                </table>
                """ if id_documents else '<p>لا توجد وثائق هوية مسجلة.</p>'}
            </div>

            <!-- Projects Section -->
            <h2>المشاريع المشارك فيها</h2>
            <div class="section">
                {f"""
                <table>
                    <thead>
                        <tr>
                            <th>اسم المشروع</th>
                            <th>الحالة</th>
                            <th>تاريخ البدء</th>
                            <th>تاريخ الانتهاء</th>
                        </tr>
                    </thead>
                    <tbody>
                        {''.join([f"""
                        <tr>
                            <td>{project.name}</td>
                            <td>
                                {{'معلق' if project.status == 'pending' else
                                  'قيد التنفيذ' if project.status == 'in_progress' else
                                  'مكتمل' if project.status == 'completed' else
                                  'ملغي' if project.status == 'cancelled' else project.status}}
                            </td>
                            <td>{project.start_date.strftime('%Y-%m-%d')}</td>
                            <td>{project.end_date.strftime('%Y-%m-%d') if project.end_date else 'غير محدد'}</td>
                        </tr>
                        """ for project in projects])}
                    </tbody>
                </table>
                """ if projects else '<p>لا يوجد مشاريع مشارك فيها حاليًا.</p>'}
            </div>

            <!-- Tasks Section -->
            <h2>المهام المسندة</h2>
            <div class="section">
                {f"""
                <table>
                    <thead>
                        <tr>
                            <th>عنوان المهمة</th>
                            <th>المشروع</th>
                            <th>الحالة</th>
                            <th>تاريخ الاستحقاق</th>
                        </tr>
                    </thead>
                    <tbody>
                        {''.join([f"""
                        <tr>
                            <td>{task.title}</td>
                            <td>{task.project.name if task.project else 'غير محدد'}</td>
                            <td>
                                {{'معلق' if task.status == 'pending' else
                                  'قيد التنفيذ' if task.status == 'in_progress' else
                                  'مكتمل' if task.status == 'completed' else
                                  'ملغي' if task.status == 'cancelled' else task.status}}
                            </td>
                            <td>{task.due_date.strftime('%Y-%m-%d') if task.due_date else 'غير محدد'}</td>
                        </tr>
                        """ for task in tasks])}
                    </tbody>
                </table>
                """ if tasks else '<p>لا يوجد مهام مسندة حاليًا.</p>'}
            </div>

            <!-- Bank Account Section -->
            {f"""
            <h2>حساب التحصيل المالي</h2>
            <div class="section">
                <p>{employee.bank_account}</p>
            </div>
            """ if employee.bank_account else ''}

            <!-- Active Leave Section -->
            {f"""
            <h2>إجازة حالية</h2>
            <div class="section">
                <div class="row">
                    <p><strong>تاريخ البداية:</strong> {active_leave.start_date.strftime('%Y-%m-%d')}</p>
                    <p><strong>تاريخ النهاية:</strong> {active_leave.end_date.strftime('%Y-%m-%d')}</p>
                    <p><strong>المدة:</strong> {(active_leave.end_date - active_leave.start_date).days + 1} يوم</p>
                    <p><strong>السبب:</strong> {active_leave.reason}</p>
                </div>
            </div>
            """ if active_leave else ''}

            <div class="footer">
                <p>© 2025 Sparkle Media Agency. جميع الحقوق محفوظة.</p>
                <p>تم إنشاء هذا التقرير بواسطة نظام إدارة شركة Sparkle Media Agency</p>
                <p>تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M')}</p>
            </div>
        </div>
    </body>
    </html>
    '''

    # Create HTML file
    with tempfile.NamedTemporaryFile(suffix='.html', delete=False, mode='w', encoding='utf-8') as temp_file:
        temp_file.write(html_content)
        html_path = temp_file.name

    # Save report to database
    report = Report(
        title=f'تقرير تفاصيل الموظف - {employee.get_full_name()}',
        description=f'تم إنشاؤه في {datetime.now().strftime("%Y-%m-%d %H:%M")}',
        report_type='employee_detail',
        parameters=json.dumps(parameters),
        file_path=html_path,
        created_by_id=current_user.id
    )

    db.session.add(report)
    db.session.commit()

    # Send the file
    return send_file(
        html_path,
        as_attachment=True,
        download_name=f'تقرير_تفاصيل_الموظف_{employee.get_full_name()}_{datetime.now().strftime("%Y%m%d_%H%M")}.html',
        mimetype='text/html'
    )

def export_to_excel(data, title, parameters, report_type):
    # Create DataFrame for Excel
    df = pd.DataFrame(data['rows'], columns=data['headers'])

    # Create a BytesIO object to store the Excel file
    excel_io = BytesIO()

    # Create Excel writer
    with pd.ExcelWriter(excel_io, engine='xlsxwriter') as writer:
        # Convert DataFrame to Excel
        df.to_excel(writer, sheet_name=title, index=False, startrow=3)  # Start at row 3 to leave space for title

        # Get the xlsxwriter workbook and worksheet objects
        workbook = writer.book
        worksheet = writer.sheets[title]

        # Set RTL direction for the worksheet
        worksheet.right_to_left()

        # Set column widths
        for i, width in enumerate([8, 25, 25, 15, 15, 30, 15, 25]):  # Adjust widths as needed
            worksheet.set_column(i, i, width)

        # Add formats
        header_format = workbook.add_format({
            'bold': True,
            'text_wrap': True,
            'valign': 'top',
            'fg_color': '#1765A0',  # شركة ثيم لون
            'font_color': 'white',
            'border': 1,
            'align': 'right',
            'font_size': 12
        })

        # Add title format
        title_format = workbook.add_format({
            'bold': True,
            'font_size': 24,
            'align': 'right',
            'valign': 'vcenter',
            'font_color': '#1765A0',
            'text_wrap': True
        })

        # Add subtitle format
        subtitle_format = workbook.add_format({
            'bold': True,
            'font_size': 12,
            'align': 'right',
            'valign': 'vcenter',
            'font_color': '#303030',
            'text_wrap': True
        })

        # Add cell format for alternating rows
        even_row_format = workbook.add_format({
            'bg_color': '#f9f9f9',
            'border': 1,
            'border_color': '#e0e0e0',
            'align': 'right'
        })

        # Add regular cell format
        cell_format = workbook.add_format({
            'border': 1,
            'border_color': '#e0e0e0',
            'align': 'right'
        })

        # Add title to the worksheet
        max_cols = min(len(data['headers']), 8)  # Limit to 8 columns or actual header count
        title_range = f'A1:{chr(65 + max_cols - 1)}1'  # A1:H1 for 8 columns
        worksheet.merge_range(title_range, title, title_format)

        # Add logo
        # Insert a logo image in cell H1 (adjust if needed)
        logo_cell = f'{chr(65 + max_cols - 1)}1'  # H1 for 8 columns
        try:
            worksheet.insert_image(logo_cell, 'app/static/img/logo.png', {'x_scale': 0.5, 'y_scale': 0.5, 'x_offset': 10, 'y_offset': 5})
        except Exception:
            # If logo insertion fails, add a text placeholder
            worksheet.write(logo_cell, 'Sparkle Media Agency')

        # Add space after title
        worksheet.set_row(1, 20)

        # Write the column headers with the defined format
        for col_num, value in enumerate(data['headers']):
            worksheet.write(3, col_num, value, header_format)

        # Apply formatting to all cells
        for row_num, row in enumerate(data['rows']):
            for col_num, cell_value in enumerate(row):
                if row_num % 2 == 1:  # Odd rows (0-indexed)
                    worksheet.write(row_num + 4, col_num, cell_value, even_row_format)
                else:
                    worksheet.write(row_num + 4, col_num, cell_value, cell_format)

        # Add report information
        info_row = len(data['rows']) + 6

        # Add a border around the report info section
        info_border_format = workbook.add_format({
            'border': 1,
            'border_color': '#e0e0e0',
            'bg_color': '#f9f9f9'
        })

        # Create a merged cell for the report info title
        worksheet.merge_range(f'A{info_row}:C{info_row}', 'معلومات التقرير:', subtitle_format)

        # Add report info details
        info_text_format = workbook.add_format({
            'align': 'right',
            'border': 1,
            'border_color': '#e0e0e0'
        })

        info_value_format = workbook.add_format({
            'align': 'right',
            'border': 1,
            'border_color': '#e0e0e0',
            'bg_color': '#ffffff'
        })

        worksheet.write(info_row + 1, 0, 'تاريخ التقرير:', info_text_format)
        worksheet.write(info_row + 1, 1, datetime.now().strftime('%Y-%m-%d %H:%M'), info_value_format)
        worksheet.write(info_row + 2, 0, 'تم إنشاؤه بواسطة:', info_text_format)
        worksheet.write(info_row + 2, 1, current_user.get_full_name(), info_value_format)

        # Add filter information based on report type
        if report_type == 'employees':
            department_id = parameters.get('department_id', 'all')
            status = parameters.get('status', 'all')

            department_name = 'الكل'
            if department_id != 'all':
                department = Department.query.get(department_id)
                if department:
                    department_name = department.name

            status_name = 'الكل'
            if status == 'active':
                status_name = 'نشط'
            elif status == 'inactive':
                status_name = 'غير نشط'

            worksheet.write(info_row + 3, 0, 'القسم:')
            worksheet.write(info_row + 3, 1, department_name)
            worksheet.write(info_row + 4, 0, 'الحالة:')
            worksheet.write(info_row + 4, 1, status_name)

        elif report_type in ['leave', 'meetings', 'penalties']:
            start_date = parameters.get('start_date', '')
            end_date = parameters.get('end_date', '')

            if start_date:
                worksheet.write(info_row + 3, 0, 'من تاريخ:')
                worksheet.write(info_row + 3, 1, start_date)

            if end_date:
                worksheet.write(info_row + 4, 0, 'إلى تاريخ:')
                worksheet.write(info_row + 4, 1, end_date)

        # Add footer
        footer_row = info_row + 6
        footer_format = workbook.add_format({
            'align': 'center',
            'font_color': '#777777',
            'font_size': 10
        })
        worksheet.merge_range(f'A{footer_row}:G{footer_row}', '© 2025 Sparkle Media Agency. جميع الحقوق محفوظة.', footer_format)
        worksheet.merge_range(f'A{footer_row+1}:G{footer_row+1}', 'تم إنشاء هذا التقرير بواسطة نظام إدارة شركة Sparkle Media Agency', footer_format)

    # Create a temporary file for the Excel
    with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as temp_file:
        excel_path = temp_file.name

    # Save the Excel file to the temporary file
    with open(excel_path, 'wb') as f:
        f.write(excel_io.getvalue())

    # Save report to database
    report = Report(
        title=title,
        description=f'تم إنشاؤه في {datetime.now().strftime("%Y-%m-%d %H:%M")}',
        report_type=report_type,
        parameters=json.dumps(parameters),
        file_path=excel_path,
        created_by_id=current_user.id
    )

    db.session.add(report)
    db.session.commit()

    # Send the file
    return send_file(
        excel_path,
        as_attachment=True,
        download_name=f'{title}_{datetime.now().strftime("%Y%m%d_%H%M")}.xlsx',
        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )

@report_bp.route('/delete-multiple', methods=['POST'])
@login_required
def delete_multiple():
    """حذف تقارير متعددة"""
    if not (current_user.has_role('admin') or current_user.has_role('manager')):
        return jsonify({'success': False, 'message': 'لا تملك صلاحية حذف التقارير'})

    try:
        data = request.get_json()
        report_ids = data.get('report_ids', [])

        if not report_ids:
            return jsonify({'success': False, 'message': 'لم يتم تحديد أي تقارير للحذف'})

        # حذف التقارير المحددة
        deleted_count = 0
        for report_id in report_ids:
            report = Report.query.get(report_id)
            if report:
                db.session.delete(report)
                deleted_count += 1

        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'تم حذف {deleted_count} تقرير بنجاح'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})
