{% extends 'base.html' %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>أعضاء مشروع {{ project.name }}</h1>
    <div>
        {% if current_user.has_role('admin') or current_user.has_role('manager') or
            current_user.id == project.manager_id or
            (current_user.has_role('department_head') and current_user.managed_department.id == project.department_id) %}
        <a href="{{ url_for('project.add_member', id=project.id) }}" class="btn btn-primary me-2">
            <i class="fas fa-user-plus me-1"></i>إضافة أعضاء
        </a>
        {% endif %}
        <a href="{{ url_for('project.view', id=project.id) }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i>العودة للمشروع
        </a>
    </div>
</div>

<div class="card shadow">
    <div class="card-header bg-light">
        <h5 class="mb-0">قائمة الأعضاء</h5>
    </div>
    <div class="card-body">
        {% if project.members.count() > 0 %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>الاسم</th>
                        <th>البريد الإلكتروني</th>
                        <th>القسم</th>
                        <th>الصلاحيات</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for member in project.members %}
                    <tr>
                        <td>
                            {% if member.id == project.manager_id %}
                            <i class="fas fa-crown text-warning me-1" data-bs-toggle="tooltip" title="مدير المشروع الرئيسي"></i>
                            {% elif member in project.managers %}
                            <i class="fas fa-crown text-info me-1" data-bs-toggle="tooltip" title="مدير مشارك"></i>
                            {% endif %}
                            {% if member.is_currently_on_leave %}
                            <i class="fas fa-calendar-times text-danger me-1" data-bs-toggle="tooltip" title="في إجازة"></i>
                            {% endif %}
                            {{ member.get_full_name() }}
                            {% if member.is_currently_on_leave %}
                            <br><small class="text-danger">
                                <i class="fas fa-exclamation-triangle me-1"></i>في إجازة عمل
                            </small>
                            {% endif %}
                        </td>
                        <td>{{ member.email }}</td>
                        <td>{{ member.department.name if member.department else 'غير محدد' }}</td>
                        <td>
                            {% for role in member.roles %}
                            <span class="badge bg-secondary me-1">{{ role.name }}</span>
                            {% endfor %}
                        </td>
                        <td>
                            <a href="{{ url_for('employee.view', id=member.id) }}" class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="عرض">
                                <i class="fas fa-eye"></i>
                            </a>

                            {% if (current_user.has_role('admin') or current_user.has_role('manager') or
                                current_user.id == project.manager_id or
                                (current_user.has_role('department_head') and current_user.managed_department.id == project.department_id)) and
                                member.id != project.manager_id %}
                            <a href="{{ url_for('project.remove_member', project_id=project.id, user_id=member.id) }}" class="btn btn-sm btn-danger" data-bs-toggle="tooltip" title="إزالة من المشروع">
                                <i class="fas fa-user-minus"></i>
                            </a>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="alert alert-info">
            لا يوجد أعضاء في هذا المشروع حاليًا.
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
