from datetime import datetime
from app import db

# Association table for user honorary ranks
UserHonoraryRank = db.Table('user_honorary_ranks',
    db.<PERSON>umn('user_id', db.<PERSON>teger, db.<PERSON><PERSON>('users.id'), primary_key=True),
    db.<PERSON>umn('honorary_rank_id', db.<PERSON>, db.<PERSON>('honorary_ranks.id'), primary_key=True),
    db.<PERSON>('assigned_at', db.DateTime, default=datetime.utcnow),
    db.<PERSON>umn('assigned_by_id', db.Integer, db.<PERSON>ey('users.id'))
)

class HonoraryRank(db.Model):
    __tablename__ = 'honorary_ranks'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    color = db.Column(db.String(20), default='#007bff')  # Default color (bootstrap primary)
    icon = db.Column(db.String(50), default='fa-award')  # Default icon

    # Premium rank fields
    is_premium = db.Column(db.Bo<PERSON>an, default=False)  # Is this a premium rank with gradient
    color_start = db.Column(db.String(20))  # Start color for gradient
    color_end = db.Column(db.String(20))  # End color for gradient
    gradient_direction = db.Column(db.String(20), default='to right')  # Gradient direction
    animated_gradient = db.Column(db.Boolean, default=False)  # Animated gradient effect

    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    created_by_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    created_by = db.relationship('User', backref='created_honorary_ranks')
    
    def get_style(self):
        """
        Get CSS style for the rank badge
        """
        if self.is_premium and self.color_start and self.color_end:
            gradient = f"linear-gradient({self.gradient_direction}, {self.color_start}, {self.color_end})"
            style = f"background: {gradient}; color: white;"
            if self.animated_gradient:
                style += " background-size: 200% 200%; animation: gradientShift 3s ease infinite;"
            return style
        else:
            return f"background-color: {self.color}; color: white;"

    def get_css_class(self):
        """
        Get CSS class for the rank badge
        """
        if self.is_premium and self.animated_gradient:
            return "animated-gradient"
        return ""

    def __repr__(self):
        return f'<HonoraryRank {self.name}>'
