{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-folder-plus me-2"></i>{{ title }}
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">اسم المجلد <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="sort_order" class="form-label">ترتيب العرض</label>
                                    <input type="number" class="form-control" id="sort_order" name="sort_order" value="0">
                                    <div class="form-text">الرقم الأصغر يظهر أولاً</div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">الوصف</label>
                            <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="icon" class="form-label">الأيقونة</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i id="icon-preview" class="fas fa-folder"></i>
                                        </span>
                                        <select class="form-select" id="icon" name="icon">
                                            <option value="fas fa-folder">📁 مجلد عادي</option>
                                            <option value="fas fa-folder-open">📂 مجلد مفتوح</option>
                                            <option value="fas fa-money-bill-wave">💰 مالي</option>
                                            <option value="fas fa-chart-line">📈 إيرادات</option>
                                            <option value="fas fa-shopping-cart">🛒 مصروفات</option>
                                            <option value="fas fa-users">👥 عملاء</option>
                                            <option value="fas fa-building">🏢 أصول</option>
                                            <option value="fas fa-university">🏦 بنوك</option>
                                            <option value="fas fa-credit-card">💳 بطاقات</option>
                                            <option value="fas fa-coins">🪙 عملات</option>
                                            <option value="fas fa-piggy-bank">🐷 مدخرات</option>
                                            <option value="fas fa-handshake">🤝 شراكات</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="color" class="form-label">لون المجلد</label>
                                    <div class="input-group">
                                        <input type="color" class="form-control form-control-color" id="color" name="color" value="#6c757d">
                                        <input type="text" class="form-control" id="color_text" value="#6c757d" readonly>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="parent_id" class="form-label">المجلد الأب</label>
                            <select class="form-select" id="parent_id" name="parent_id">
                                <option value="">مجلد رئيسي</option>
                                {% for folder in all_folders %}
                                <option value="{{ folder.id }}">{{ folder.name }}</option>
                                {% endfor %}
                            </select>
                            <div class="form-text">اختر المجلد الأب لجعل هذا مجلداً فرعياً</div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('finance.folders') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>حفظ المجلد
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تحديث معاينة الأيقونة
    const iconSelect = document.getElementById('icon');
    const iconPreview = document.getElementById('icon-preview');
    
    iconSelect.addEventListener('change', function() {
        iconPreview.className = this.value;
    });
    
    // تحديث لون النص
    const colorPicker = document.getElementById('color');
    const colorText = document.getElementById('color_text');
    
    colorPicker.addEventListener('change', function() {
        colorText.value = this.value;
    });
});
</script>
{% endblock %}
