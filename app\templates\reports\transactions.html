{% extends 'base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">تقرير المعاملات المالية</h1>
        <div>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#exportModal">
                <i class="fas fa-file-export me-1"></i>تصدير التقرير
            </button>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">فلترة المعاملات المالية</h6>
            <button class="btn btn-sm btn-outline-primary" type="button" data-bs-toggle="collapse" data-bs-target="#filterCollapse" aria-expanded="false" aria-controls="filterCollapse">
                <i class="fas fa-filter me-1"></i>عرض/إخفاء الفلاتر
            </button>
        </div>
        <div class="collapse" id="filterCollapse">
            <div class="card-body">
                <form id="filterForm" action="{{ url_for('report.transactions') }}" method="GET">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label for="status" class="form-label">حالة المعاملة</label>
                            <select class="form-select" id="status" name="status">
                                <option value="all" {% if status == 'all' %}selected{% endif %}>جميع الحالات</option>
                                <option value="draft" {% if status == 'draft' %}selected{% endif %}>مسودة</option>
                                <option value="pending" {% if status == 'pending' %}selected{% endif %}>معلقة</option>
                                <option value="paid" {% if status == 'paid' %}selected{% endif %}>مدفوعة</option>
                                <option value="cancelled" {% if status == 'cancelled' %}selected{% endif %}>ملغية</option>
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="date_from" class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="date_from" name="date_from" value="{{ date_from }}">
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="date_to" class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="date_to" name="date_to" value="{{ date_to }}">
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="export_format" class="form-label">تنسيق التصدير</label>
                            <select class="form-select" id="export_format" name="export_format">
                                <option value="pdf" selected>PDF</option>
                                <option value="excel">Excel</option>
                            </select>
                        </div>
                    </div>
                    <div class="d-flex justify-content-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-file-export me-1"></i>تصدير التقرير
                        </button>
                        <a href="{{ url_for('report.transactions') }}" class="btn btn-secondary">
                            <i class="fas fa-redo me-1"></i>إعادة تعيين
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">المعاملات المالية</h6>
            <div class="d-flex align-items-center">
                <select class="form-select form-select-sm me-2" onchange="changePerPage(this.value)" style="width: auto;">
                    <option value="25" {% if per_page == 25 %}selected{% endif %}>25 معاملة</option>
                    <option value="50" {% if per_page == 50 %}selected{% endif %}>50 معاملة</option>
                    <option value="100" {% if per_page == 100 %}selected{% endif %}>100 معاملة</option>
                </select>
            </div>
        </div>
        <div class="card-body">
            {% if transactions %}
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>رقم المعاملة</th>
                            <th>اسم المعاملة</th>
                            <th>حالة المعاملة</th>
                            <th>عدد البنود</th>
                            <th>تاريخ الإنشاء</th>
                            <th>آخر تحديث</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% if transactions.items %}
                            {% for transaction in transactions.items %}
                            <tr>
                                <td>{{ loop.index + ((transactions.page - 1) * transactions.per_page) }}</td>
                                <td>{{ transaction.transaction_number or 'غير محدد' }}</td>
                                <td>{{ transaction.name or 'غير محدد' }}</td>
                                <td>
                                    {% if transaction.status == 'draft' %}
                                    <span class="badge bg-secondary">مسودة</span>
                                    {% elif transaction.status == 'pending' %}
                                    <span class="badge bg-warning">معلقة</span>
                                    {% elif transaction.status == 'paid' %}
                                    <span class="badge bg-success">مدفوعة</span>
                                    {% elif transaction.status == 'cancelled' %}
                                    <span class="badge bg-danger">ملغية</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ transaction.items|length }}</span>
                                </td>
                                <td>{{ transaction.created_at.strftime('%Y-%m-%d %H:%M') if transaction.created_at else 'غير محدد' }}</td>
                                <td>{{ transaction.updated_at.strftime('%Y-%m-%d %H:%M') if transaction.updated_at else 'غير محدد' }}</td>
                            </tr>
                            {% endfor %}
                        {% else %}
                            {% for transaction in transactions %}
                            <tr>
                                <td>{{ loop.index }}</td>
                                <td>{{ transaction.transaction_number or 'غير محدد' }}</td>
                                <td>{{ transaction.name or 'غير محدد' }}</td>
                                <td>
                                    {% if transaction.status == 'draft' %}
                                    <span class="badge bg-secondary">مسودة</span>
                                    {% elif transaction.status == 'pending' %}
                                    <span class="badge bg-warning">معلقة</span>
                                    {% elif transaction.status == 'paid' %}
                                    <span class="badge bg-success">مدفوعة</span>
                                    {% elif transaction.status == 'cancelled' %}
                                    <span class="badge bg-danger">ملغية</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ transaction.items|length }}</span>
                                </td>
                                <td>{{ transaction.created_at.strftime('%Y-%m-%d %H:%M') if transaction.created_at else 'غير محدد' }}</td>
                                <td>{{ transaction.updated_at.strftime('%Y-%m-%d %H:%M') if transaction.updated_at else 'غير محدد' }}</td>
                            </tr>
                            {% endfor %}
                        {% endif %}
                    </tbody>
                </table>
            </div>

            <!-- تقسيم الصفحات -->
            {% if transactions.items and transactions.pages > 1 %}
            <div class="d-flex justify-content-between align-items-center mt-3">
                <div>
                    <small class="text-muted">
                        عرض {{ transactions.per_page * (transactions.page - 1) + 1 }} إلى
                        {{ transactions.per_page * (transactions.page - 1) + transactions.items|length }}
                        من {{ transactions.total }} معاملة
                    </small>
                </div>
                <nav aria-label="تقسيم صفحات المعاملات">
                    <ul class="pagination pagination-sm mb-0">
                        {% if transactions.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('report.transactions', page=transactions.prev_num, per_page=per_page) }}">السابق</a>
                        </li>
                        {% endif %}

                        {% for page_num in transactions.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != transactions.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('report.transactions', page=page_num, per_page=per_page) }}">{{ page_num }}</a>
                                </li>
                                {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                                {% endif %}
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">…</span>
                            </li>
                            {% endif %}
                        {% endfor %}

                        {% if transactions.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('report.transactions', page=transactions.next_num, per_page=per_page) }}">التالي</a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
            {% endif %}
            {% else %}
            <div class="text-center py-4">
                <p class="text-muted mb-0">لا توجد معاملات مالية لعرضها</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Export Modal -->
<div class="modal fade" id="exportModal" tabindex="-1" aria-labelledby="exportModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form action="{{ url_for('report.export', report_type='transactions') }}" method="POST">
                <div class="modal-header">
                    <h5 class="modal-title" id="exportModalLabel">تصدير تقرير المعاملات المالية</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="export_format" class="form-label">صيغة التصدير</label>
                        <select class="form-select" id="export_format" name="export_format">
                            <option value="pdf">PDF</option>
                            <option value="excel">Excel</option>
                        </select>
                    </div>

                    <!-- Hidden fields for filters -->
                    <input type="hidden" id="hidden_transaction_type" name="transaction_type" value="{{ transaction_type }}">
                    <input type="hidden" id="hidden_category" name="category" value="{{ category }}">
                    <input type="hidden" id="hidden_start_date" name="start_date" value="{{ start_date }}">
                    <input type="hidden" id="hidden_end_date" name="end_date" value="{{ end_date }}">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">تصدير</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        $('#dataTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json"
            },
            "order": [[1, "desc"]]
        });

        // Update hidden fields when form is submitted
        $('#filterForm').on('submit', function() {
            $('#hidden_transaction_type').val($('#transaction_type').val());
            $('#hidden_category').val($('#category').val());
            $('#hidden_start_date').val($('#start_date').val());
            $('#hidden_end_date').val($('#end_date').val());
        });

        // Update hidden fields when export button is clicked
        $('#exportModal').on('show.bs.modal', function() {
            $('#hidden_transaction_type').val($('#transaction_type').val());
            $('#hidden_category').val($('#category').val());
            $('#hidden_start_date').val($('#start_date').val());
            $('#hidden_end_date').val($('#end_date').val());
        });
    });

function changePerPage(perPage) {
    const url = new URL(window.location);
    url.searchParams.set('per_page', perPage);
    url.searchParams.set('page', 1); // Reset to first page
    window.location.href = url.toString();
}
</script>
{% endblock %}
