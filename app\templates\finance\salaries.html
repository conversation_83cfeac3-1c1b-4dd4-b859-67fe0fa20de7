{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
        <div>
            {% if current_user.has_role('admin') or current_user.has_role('manager') %}
            <a href="{{ url_for('finance.salary_settings') }}" class="btn btn-info me-2">
                <i class="fas fa-cog me-1"></i>الإعدادات
            </a>
            {% endif %}
            {% if current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance') %}
            <a href="{{ url_for('finance.add_salary') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>إضافة راتب جديد
            </a>
            {% endif %}
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header bg-light">
                    <h5 class="mb-0">رواتب الموظفين</h5>
                </div>
                <div class="card-body">
                    <!-- Search and Filter Form -->
                    <form method="GET" action="{{ url_for('finance.salaries') }}" class="mb-4">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="input-group">
                                    <input type="text" class="form-control" name="employee" 
                                           value="{{ employee_filter }}" placeholder="البحث بالموظف...">
                                    <button class="btn btn-outline-secondary" type="submit">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" name="status" onchange="this.form.submit()">
                                    <option value="">جميع الحالات</option>
                                    <option value="draft" {% if status_filter == 'draft' %}selected{% endif %}>مسودة</option>
                                    <option value="pending" {% if status_filter == 'pending' %}selected{% endif %}>معلق</option>
                                    <option value="paid" {% if status_filter == 'paid' %}selected{% endif %}>مدفوع</option>
                                    <option value="cancelled" {% if status_filter == 'cancelled' %}selected{% endif %}>ملغي</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                {% if status_filter or employee_filter %}
                                <a href="{{ url_for('finance.salaries') }}" class="btn btn-secondary">
                                    <i class="fas fa-times me-1"></i>إزالة الفلاتر
                                </a>
                                {% endif %}
                            </div>
                        </div>
                    </form>

                    {% if salaries.items %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>الموظف</th>
                                    <th>المبلغ</th>
                                    <th>الحالة</th>
                                    <th>موعد الحوالة</th>
                                    <th>المشرف</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for salary in salaries.items %}
                                <tr>
                                    <td>
                                        {% if salary.employee %}
                                        <a href="{{ url_for('employee.view', id=salary.employee.id) }}">
                                            {{ salary.employee.first_name }} {{ salary.employee.last_name }}
                                        </a>
                                        {% else %}
                                        {{ salary.employee_name }}
                                        {% if salary.employee_manual %}
                                        <small class="text-muted">({{ salary.employee_manual }})</small>
                                        {% endif %}
                                        {% endif %}
                                    </td>
                                    <td>
                                        <strong>${{ "{:,.2f}".format(salary.total_amount) }}</strong>
                                        {% if salary.commission_amount > 0 %}
                                        <br><small class="text-muted">عمولة: ${{ "{:,.2f}".format(salary.commission_amount) }}</small>
                                        <br><small class="text-success">صافي: ${{ "{:,.2f}".format(salary.net_amount) }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if salary.status == 'draft' %}
                                        <span class="badge bg-secondary">مسودة</span>
                                        {% elif salary.status == 'pending' %}
                                        <span class="badge bg-warning">معلق</span>
                                        {% elif salary.status == 'paid' %}
                                        <span class="badge bg-success">مدفوع</span>
                                        {% elif salary.status == 'cancelled' %}
                                        <span class="badge bg-danger">ملغي</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if salary.transfer_date %}
                                        {{ salary.transfer_date.strftime('%Y-%m-%d %H:%M') }}
                                        {% else %}
                                        <span class="text-muted">غير محدد</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if salary.supervisor %}
                                        {{ salary.supervisor.first_name }} {{ salary.supervisor.last_name }}
                                        {% else %}
                                        <span class="text-muted">غير محدد</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ salary.created_at.strftime('%Y-%m-%d') }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ url_for('finance.view_salary', id=salary.id) }}" 
                                               class="btn btn-sm btn-outline-primary" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            {% if current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance') %}
                                            <a href="{{ url_for('finance.edit_salary', id=salary.id) }}" 
                                               class="btn btn-sm btn-outline-warning" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            {% endif %}
                                            {% if current_user.has_role('admin') or current_user.has_role('finance') %}
                                            <a href="{{ url_for('finance.delete_salary', id=salary.id) }}"
                                               class="btn btn-sm btn-outline-danger" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if salaries.pages > 1 %}
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center">
                            {% if salaries.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('finance.salaries', page=salaries.prev_num, status=status_filter, employee=employee_filter) }}">السابق</a>
                            </li>
                            {% endif %}
                            
                            {% for page_num in salaries.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != salaries.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('finance.salaries', page=page_num, status=status_filter, employee=employee_filter) }}">{{ page_num }}</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if salaries.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('finance.salaries', page=salaries.next_num, status=status_filter, employee=employee_filter) }}">التالي</a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-money-bill-wave fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد رواتب مضافة بعد</p>
                        {% if current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance') %}
                        <a href="{{ url_for('finance.add_salary') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>إضافة أول راتب
                        </a>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>


{% endblock %}
