{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
        <a href="{{ url_for('finance.financial_transactions') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i>العودة إلى المعاملات المالية
        </a>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-plus me-2"></i>إنشاء معاملة مالية جديدة
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" id="transactionForm">
                        <!-- معلومات المعاملة الأساسية -->
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">معلومات المعاملة الأساسية</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="transaction_name" class="form-label">اسم المعاملة <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="transaction_name" name="transaction_name" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="status" class="form-label">حالة المعاملة</label>
                                            <select class="form-select" id="status" name="status">
                                                <option value="draft" selected>مسودة</option>
                                                <option value="pending">معلقة</option>
                                                <option value="paid">مدفوعة</option>
                                                <option value="cancelled">ملغية</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="mb-3">
                                            <label for="notes" class="form-label">ملاحظات</label>
                                            <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- بنود المعاملة -->
                        <div class="card mb-4">
                            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">بنود المعاملة</h6>
                                <button type="button" class="btn btn-sm btn-success" onclick="addTransactionItem()">
                                    <i class="fas fa-plus me-1"></i>إضافة بند
                                </button>
                            </div>
                            <div class="card-body">
                                <div id="transaction-items">
                                    <!-- سيتم إضافة البنود هنا ديناميكياً -->
                                </div>
                            </div>
                        </div>

                        <!-- المرفقات والروابط -->
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">المرفقات والروابط (اختياري)</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="attachments" class="form-label">المرفقات</label>
                                            <input type="file" class="form-control" id="attachments" name="attachments[]" multiple accept="image/*,.pdf,.doc,.docx">
                                            <div class="form-text">يمكنك اختيار أكثر من ملف</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="links" class="form-label">الروابط</label>
                                            <div id="links-container">
                                                <div class="input-group mb-2">
                                                    <input type="url" class="form-control" name="links[]" placeholder="https://example.com">
                                                    <button type="button" class="btn btn-outline-success" onclick="addLinkField()">
                                                        <i class="fas fa-plus"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('finance.financial_transactions') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>حفظ المعاملة
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- قالب بند المعاملة -->
<template id="transaction-item-template">
    <div class="transaction-item border rounded p-3 mb-3">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h6 class="mb-0">بند المعاملة</h6>
            <button type="button" class="btn btn-sm btn-danger" onclick="removeTransactionItem(this)">
                <i class="fas fa-trash"></i>
            </button>
        </div>
        <div class="row">
            <div class="col-md-4">
                <div class="mb-3">
                    <label class="form-label">الحساب <span class="text-danger">*</span></label>
                    <select class="form-select" name="item_account_id[]" required>
                        <option value="">اختر الحساب</option>
                        {% for account in accounts %}
                        <option value="{{ account.id }}">{{ account.full_path }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            <div class="col-md-2">
                <div class="mb-3">
                    <label class="form-label">الإجراء</label>
                    <select class="form-select" name="item_action[]">
                        <option value="add">إضافة</option>
                        <option value="subtract">خصم</option>
                    </select>
                </div>
            </div>
            <div class="col-md-3">
                <div class="mb-3">
                    <label class="form-label">المبلغ <span class="text-danger">*</span></label>
                    <div class="input-group">
                        <input type="number" step="0.01" class="form-control" name="item_amount[]" min="0" required>
                        <span class="input-group-text">$</span>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="mb-3">
                    <label class="form-label">تاريخ ووقت الإجراء</label>
                    <input type="datetime-local" class="form-control" name="item_date[]">
                </div>
            </div>
        </div>
    </div>
</template>
{% endblock %}

{% block scripts %}
<script>
let itemCounter = 0;

function addTransactionItem() {
    const template = document.getElementById('transaction-item-template');
    const clone = template.content.cloneNode(true);
    
    // Set current datetime as default
    const datetimeInput = clone.querySelector('input[type="datetime-local"]');
    const now = new Date();
    now.setMinutes(now.getMinutes() - now.getTimezoneOffset());
    datetimeInput.value = now.toISOString().slice(0, 16);
    
    document.getElementById('transaction-items').appendChild(clone);
    itemCounter++;
}

function removeTransactionItem(button) {
    button.closest('.transaction-item').remove();
}

function addLinkField() {
    const container = document.getElementById('links-container');
    const newField = document.createElement('div');
    newField.className = 'input-group mb-2';
    newField.innerHTML = `
        <input type="url" class="form-control" name="links[]" placeholder="https://example.com">
        <button type="button" class="btn btn-outline-danger" onclick="this.parentElement.remove()">
            <i class="fas fa-minus"></i>
        </button>
    `;
    container.appendChild(newField);
}

document.addEventListener('DOMContentLoaded', function() {
    // Add first transaction item by default
    addTransactionItem();
    
    // Form validation
    const form = document.getElementById('transactionForm');
    form.addEventListener('submit', function(e) {
        const transactionName = document.getElementById('transaction_name').value;
        const items = document.querySelectorAll('.transaction-item');
        
        if (!transactionName.trim()) {
            e.preventDefault();
            alert('اسم المعاملة مطلوب');
            return false;
        }
        
        if (items.length === 0) {
            e.preventDefault();
            alert('يجب إضافة بند واحد على الأقل');
            return false;
        }
        
        // Validate each item
        let hasValidItem = false;
        items.forEach(item => {
            const account = item.querySelector('select[name="item_account_id[]"]').value;
            const amount = item.querySelector('input[name="item_amount[]"]').value;
            
            if (account && amount && parseFloat(amount) > 0) {
                hasValidItem = true;
            }
        });
        
        if (!hasValidItem) {
            e.preventDefault();
            alert('يجب أن يحتوي بند واحد على الأقل على حساب ومبلغ صحيح');
            return false;
        }
    });
});
</script>
{% endblock %}
