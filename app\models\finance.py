from datetime import datetime
from app import db

# نموذج المجلدات العامة لتنظيم الحسابات
class AccountFolder(db.Model):
    __tablename__ = 'account_folders'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text)
    icon = db.Column(db.String(50), default='fas fa-folder')  # أيقونة المجلد
    color = db.Column(db.String(7), default='#6c757d')  # لون المجلد
    is_expanded = db.Column(db.<PERSON>, default=True)  # حالة التوسيع/الطي
    sort_order = db.Column(db.Integer, default=0)  # ترتيب المجلد
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات الهرمية للمجلدات
    parent_id = db.Column(db.Integer, db.ForeignKey('account_folders.id'))
    parent = db.relationship('AccountFolder', remote_side=[id], backref='children')

    def __repr__(self):
        return f'<AccountFolder {self.name}>'

# نموذج شجرة الحسابات
class Account(db.Model):
    __tablename__ = 'accounts'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255), nullable=False)
    code = db.Column(db.String(50), unique=True)  # رمز الحساب
    description = db.Column(db.Text)
    balance = db.Column(db.Float, default=0.0)  # رصيد الحساب
    account_type = db.Column(db.String(50), default='general')  # نوع الحساب
    currency_id = db.Column(db.Integer, db.ForeignKey('currencies.id'), default=1)  # العملة
    account_color = db.Column(db.String(7), default='#007bff')  # لون الحساب
    text_color = db.Column(db.String(7), default='#ffffff')  # لون الخط
    folder_id = db.Column(db.Integer, db.ForeignKey('account_folders.id'))  # المجلد
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات الهرمية للحسابات
    parent_id = db.Column(db.Integer, db.ForeignKey('accounts.id'))
    parent = db.relationship('Account', remote_side=[id], backref='children')

    # العلاقات مع النماذج الأخرى
    currency = db.relationship('Currency', backref='accounts')
    folder = db.relationship('AccountFolder', backref='accounts')

    def __repr__(self):
        return f'<Account {self.name}>'

    @property
    def full_path(self):
        """إرجاع المسار الكامل للحساب"""
        if self.parent:
            return f"{self.parent.full_path} > {self.name}"
        return self.name

    def get_all_children(self):
        """إرجاع جميع الحسابات الفرعية"""
        children = []
        for child in self.children:
            children.append(child)
            children.extend(child.get_all_children())
        return children

    def update_balance(self, amount, operation='add'):
        """تحديث رصيد الحساب"""
        if operation == 'add':
            self.balance += amount
        elif operation == 'subtract':
            self.balance -= amount
        self.updated_at = datetime.utcnow()

class Transaction(db.Model):
    __tablename__ = 'transactions'

    id = db.Column(db.Integer, primary_key=True)
    amount = db.Column(db.Float, nullable=False)
    transaction_type = db.Column(db.String(20), nullable=False)  # income, expense
    category = db.Column(db.String(50))  # salary, rent, service, etc.
    description = db.Column(db.Text)
    date = db.Column(db.DateTime, default=datetime.utcnow)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # New fields
    supervisor_id = db.Column(db.Integer, db.ForeignKey('users.id'))  # Supervisor of the transaction
    recipient = db.Column(db.String(255))  # Recipient of the transaction

    # Alternative currency fields
    alt_amount = db.Column(db.Float)  # Amount in alternative currency
    currency_id = db.Column(db.Integer, db.ForeignKey('currencies.id'))  # Alternative currency

    # Relationships
    invoice_id = db.Column(db.Integer, db.ForeignKey('invoices.id'))
    recorded_by_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    recorded_by = db.relationship('User', foreign_keys=[recorded_by_id], backref='transactions')
    supervisor = db.relationship('User', foreign_keys=[supervisor_id], backref='supervised_transactions')
    currency = db.relationship('Currency', backref='transactions')

    # Transaction attachments and verification links
    attachments = db.relationship('TransactionAttachment', backref='transaction', lazy='dynamic', cascade='all, delete-orphan')
    verification_links = db.relationship('TransactionVerificationLink', backref='transaction', lazy='dynamic', cascade='all, delete-orphan')

    def __repr__(self):
        return f'<Transaction {self.id}: {self.amount}>'

# نموذج رواتب الموظفين
class EmployeeSalary(db.Model):
    __tablename__ = 'employee_salaries'

    id = db.Column(db.Integer, primary_key=True)
    employee_name = db.Column(db.String(255), nullable=False)  # اسم الموظف
    employee_manual = db.Column(db.String(255))  # موظف يدوي من شركة أخرى
    # تم نقل المبلغ إلى جدول البنود
    commission_type = db.Column(db.String(20), default='percentage')  # percentage أو fixed
    commission_value = db.Column(db.Float, default=0.0)  # قيمة العمولة
    commission_account_id = db.Column(db.Integer, db.ForeignKey('accounts.id'))  # حساب عمولة الشركة
    status = db.Column(db.String(20), default='draft')  # draft, pending, paid, cancelled
    transfer_date = db.Column(db.DateTime)  # موعد الحوالة (اختياري)
    notes = db.Column(db.Text)
    links = db.Column(db.Text)  # الروابط مفصولة بفواصل
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات
    employee_id = db.Column(db.Integer, db.ForeignKey('users.id'))  # الموظف من النظام
    employee = db.relationship('User', foreign_keys=[employee_id], backref='salaries')

    supervisor_id = db.Column(db.Integer, db.ForeignKey('users.id'))  # الموظف المشرف للحوالة
    supervisor = db.relationship('User', foreign_keys=[supervisor_id], backref='supervised_salaries')

    created_by_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    created_by = db.relationship('User', foreign_keys=[created_by_id], backref='created_salaries')

    # الحسابات المرتبطة
    pending_account_id = db.Column(db.Integer, db.ForeignKey('accounts.id'))  # حساب المال المعلق
    deduction_account_id = db.Column(db.Integer, db.ForeignKey('accounts.id'))  # حساب الخصم
    paid_account_id = db.Column(db.Integer, db.ForeignKey('accounts.id'))  # حساب الرواتب المدفوعة

    # المشاريع والمهام المرتبطة
    projects = db.relationship('Project', secondary='salary_projects', backref='related_salaries')
    tasks = db.relationship('Task', secondary='salary_tasks', backref='related_salaries')

    # المرفقات والروابط - تم نقلها لحقول نصية بسيطة

    # العلاقات مع النماذج الأخرى
    employee = db.relationship('User', foreign_keys=[employee_id], backref='employee_salaries')
    supervisor = db.relationship('User', foreign_keys=[supervisor_id], backref='supervised_salaries')
    created_by = db.relationship('User', foreign_keys=[created_by_id], backref='created_salaries')
    pending_account = db.relationship('Account', foreign_keys=[pending_account_id], backref='salary_pending_items')
    deduction_account = db.relationship('Account', foreign_keys=[deduction_account_id], backref='salary_deduction_items')
    paid_account = db.relationship('Account', foreign_keys=[paid_account_id], backref='salary_paid_items')
    commission_account = db.relationship('Account', foreign_keys=[commission_account_id], backref='salary_commission_items')

    @property
    def total_amount(self):
        """حساب إجمالي مبلغ الراتب من البنود"""
        return sum(item.amount for item in self.items)

    @property
    def commission_amount(self):
        """حساب مبلغ العمولة"""
        if self.commission_type == 'percentage':
            return self.total_amount * (self.commission_value / 100)
        else:
            return self.commission_value

    @property
    def net_amount(self):
        """المبلغ الصافي بعد خصم العمولة"""
        return self.total_amount - self.commission_amount

    def update_account_balances(self, old_status=None):
        """تحديث أرصدة الحسابات بناءً على حالة الراتب"""
        # استخدام المبلغ الصافي بدلاً من المبلغ الإجمالي
        amount_to_use = self.net_amount

        if old_status and old_status != 'draft':
            # إزالة التأثير السابق
            if old_status == 'pending' and self.pending_account_id:
                pending_account = Account.query.get(self.pending_account_id)
                if pending_account:
                    pending_account.update_balance(amount_to_use, 'subtract')
            elif old_status == 'paid':
                if self.paid_account_id:
                    paid_account = Account.query.get(self.paid_account_id)
                    if paid_account:
                        paid_account.update_balance(amount_to_use, 'subtract')
                if self.deduction_account_id:
                    deduction_account = Account.query.get(self.deduction_account_id)
                    if deduction_account:
                        deduction_account.update_balance(amount_to_use, 'add')

        # تطبيق التأثير الجديد
        if self.status == 'pending' and self.pending_account_id:
            pending_account = Account.query.get(self.pending_account_id)
            if pending_account:
                pending_account.update_balance(amount_to_use, 'add')
        elif self.status == 'paid':
            # خصم المبلغ الصافي من حساب الخصم (الحساب الذي يتم الدفع منه)
            if self.deduction_account_id:
                deduction_account = Account.query.get(self.deduction_account_id)
                if deduction_account:
                    deduction_account.update_balance(amount_to_use, 'subtract')

            # إضافة إلى حساب الرواتب المدفوعة (للمتابعة المحاسبية)
            if self.paid_account_id:
                paid_account = Account.query.get(self.paid_account_id)
                if paid_account:
                    paid_account.update_balance(amount_to_use, 'add')

            # إضافة العمولة إلى حساب عمولة الشركة
            if self.commission_amount > 0 and self.commission_account_id:
                commission_account = Account.query.get(self.commission_account_id)
                if commission_account:
                    commission_account.update_balance(self.commission_amount, 'add')

    def revert_account_balances(self):
        """إلغاء تأثير الراتب على أرصدة الحسابات عند الحذف"""
        amount_to_use = self.net_amount

        if self.status == 'pending' and self.pending_account_id:
            pending_account = Account.query.get(self.pending_account_id)
            if pending_account:
                pending_account.update_balance(amount_to_use, 'subtract')
        elif self.status == 'paid':
            # إعادة المبلغ الصافي إلى حساب الخصم
            if self.deduction_account_id:
                deduction_account = Account.query.get(self.deduction_account_id)
                if deduction_account:
                    deduction_account.update_balance(amount_to_use, 'add')

            # إزالة من حساب الرواتب المدفوعة
            if self.paid_account_id:
                paid_account = Account.query.get(self.paid_account_id)
                if paid_account:
                    paid_account.update_balance(amount_to_use, 'subtract')

            # إزالة العمولة من حساب عمولة الشركة
            if self.commission_amount > 0 and self.commission_account_id:
                commission_account = Account.query.get(self.commission_account_id)
                if commission_account:
                    commission_account.update_balance(self.commission_amount, 'subtract')

    def __repr__(self):
        return f'<EmployeeSalary {self.employee_name}: {self.total_amount}>'

# نموذج بنود الراتب
class SalaryItem(db.Model):
    __tablename__ = 'salary_items'

    id = db.Column(db.Integer, primary_key=True)
    salary_id = db.Column(db.Integer, db.ForeignKey('employee_salaries.id'), nullable=False)
    project_id = db.Column(db.Integer, db.ForeignKey('projects.id'))
    task_id = db.Column(db.Integer, db.ForeignKey('tasks.id'))
    amount = db.Column(db.Float, nullable=False)
    description = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    salary = db.relationship('EmployeeSalary', backref='items')
    project = db.relationship('Project', backref='salary_items')
    task = db.relationship('Task', backref='salary_items')

    def __repr__(self):
        return f'<SalaryItem {self.amount}>'

# جداول الربط للرواتب مع المشاريع والمهام
salary_projects = db.Table('salary_projects',
    db.Column('salary_id', db.Integer, db.ForeignKey('employee_salaries.id'), primary_key=True),
    db.Column('project_id', db.Integer, db.ForeignKey('projects.id'), primary_key=True)
)

salary_tasks = db.Table('salary_tasks',
    db.Column('salary_id', db.Integer, db.ForeignKey('employee_salaries.id'), primary_key=True),
    db.Column('task_id', db.Integer, db.ForeignKey('tasks.id'), primary_key=True)
)

# نموذج مرفقات الرواتب
class SalaryAttachment(db.Model):
    __tablename__ = 'salary_attachments'

    id = db.Column(db.Integer, primary_key=True)
    filename = db.Column(db.String(255), nullable=False)
    filepath = db.Column(db.String(255), nullable=False)
    file_type = db.Column(db.String(50))
    file_size = db.Column(db.Integer)
    uploaded_at = db.Column(db.DateTime, default=datetime.utcnow)

    salary_id = db.Column(db.Integer, db.ForeignKey('employee_salaries.id'))
    uploaded_by_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    uploaded_by = db.relationship('User', backref='salary_attachments')

# نموذج روابط الرواتب
class SalaryLink(db.Model):
    __tablename__ = 'salary_links'

    id = db.Column(db.Integer, primary_key=True)
    url = db.Column(db.String(500), nullable=False)
    description = db.Column(db.String(255))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    salary_id = db.Column(db.Integer, db.ForeignKey('employee_salaries.id'))
    added_by_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    added_by = db.relationship('User', backref='salary_links')

# نموذج المعاملات المالية
class FinancialTransaction(db.Model):
    __tablename__ = 'financial_transactions'

    id = db.Column(db.Integer, primary_key=True)
    transaction_number = db.Column(db.String(50), unique=True, nullable=False)
    transaction_name = db.Column(db.String(255), nullable=False)
    status = db.Column(db.String(20), default='draft')  # draft, pending, paid, cancelled
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات
    created_by_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    created_by = db.relationship('User', backref='created_financial_transactions')

    # بنود المعاملة
    items = db.relationship('FinancialTransactionItem', backref='transaction', lazy='dynamic', cascade='all, delete-orphan')

    # المرفقات والروابط
    attachments = db.relationship('FinancialTransactionAttachment', backref='transaction', lazy='dynamic', cascade='all, delete-orphan')
    links = db.relationship('FinancialTransactionLink', backref='transaction', lazy='dynamic', cascade='all, delete-orphan')

    def __repr__(self):
        return f'<FinancialTransaction {self.transaction_number}: {self.transaction_name}>'

    def calculate_total_amount(self):
        """حساب إجمالي مبلغ المعاملة"""
        return sum(item.amount for item in self.items)

    def update_account_balances(self, old_status=None):
        """تحديث أرصدة الحسابات بناءً على حالة المعاملة"""
        if old_status and old_status == 'paid':
            # إزالة التأثير السابق
            for item in self.items:
                account = Account.query.get(item.account_id)
                if account:
                    if item.action == 'add':
                        account.update_balance(item.amount, 'subtract')
                    elif item.action == 'subtract':
                        account.update_balance(item.amount, 'add')

        # تطبيق التأثير الجديد
        if self.status == 'paid':
            for item in self.items:
                account = Account.query.get(item.account_id)
                if account:
                    if item.action == 'add':
                        account.update_balance(item.amount, 'add')
                    elif item.action == 'subtract':
                        account.update_balance(item.amount, 'subtract')

    def revert_account_balances(self):
        """إلغاء تأثير المعاملة على أرصدة الحسابات"""
        if self.status == 'paid':
            for item in self.items:
                account = Account.query.get(item.account_id)
                if account:
                    if item.action == 'add':
                        account.update_balance(item.amount, 'subtract')
                    elif item.action == 'subtract':
                        account.update_balance(item.amount, 'add')

# نموذج بنود المعاملات المالية
class FinancialTransactionItem(db.Model):
    __tablename__ = 'financial_transaction_items'

    id = db.Column(db.Integer, primary_key=True)
    amount = db.Column(db.Float, nullable=False)
    action = db.Column(db.String(20), nullable=False)  # add, subtract
    transaction_date = db.Column(db.DateTime, default=datetime.utcnow)
    notes = db.Column(db.Text)

    # حقول جديدة
    item_notes = db.Column(db.Text)  # ملاحظة البند
    currency_id = db.Column(db.Integer, db.ForeignKey('currencies.id'), nullable=False)  # عملة البند

    # العلاقات
    transaction_id = db.Column(db.Integer, db.ForeignKey('financial_transactions.id'))
    account_id = db.Column(db.Integer, db.ForeignKey('accounts.id'))
    account = db.relationship('Account', backref='transaction_items')
    currency = db.relationship('Currency', backref='transaction_items')

    def __repr__(self):
        return f'<FinancialTransactionItem {self.action}: {self.amount}>'

# نموذج مرفقات المعاملات المالية
class FinancialTransactionAttachment(db.Model):
    __tablename__ = 'financial_transaction_attachments'

    id = db.Column(db.Integer, primary_key=True)
    filename = db.Column(db.String(255), nullable=False)
    filepath = db.Column(db.String(255), nullable=False)
    file_type = db.Column(db.String(50))
    file_size = db.Column(db.Integer)
    uploaded_at = db.Column(db.DateTime, default=datetime.utcnow)

    transaction_id = db.Column(db.Integer, db.ForeignKey('financial_transactions.id'))
    uploaded_by_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    uploaded_by = db.relationship('User', backref='financial_transaction_attachments')

# نموذج روابط المعاملات المالية
class FinancialTransactionLink(db.Model):
    __tablename__ = 'financial_transaction_links'

    id = db.Column(db.Integer, primary_key=True)
    url = db.Column(db.String(500), nullable=False)
    description = db.Column(db.String(255))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    transaction_id = db.Column(db.Integer, db.ForeignKey('financial_transactions.id'))
    added_by_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    added_by = db.relationship('User', backref='financial_transaction_links')

class Invoice(db.Model):
    __tablename__ = 'invoices'

    id = db.Column(db.Integer, primary_key=True)
    invoice_number = db.Column(db.String(20), unique=True, nullable=False)
    issue_date = db.Column(db.DateTime, default=datetime.utcnow)
    due_date = db.Column(db.DateTime)
    approval_date = db.Column(db.DateTime)

    # Nuevos campos para comisiones, impuestos y descuentos
    transfer_fee_type = db.Column(db.String(10), default='percentage')  # percentage, fixed
    transfer_fee_value = db.Column(db.Float, default=0)
    tax_type = db.Column(db.String(10), default='percentage')  # percentage, fixed
    tax_value = db.Column(db.Float, default=0)
    discount_type = db.Column(db.String(10), default='percentage')  # percentage, fixed
    discount_value = db.Column(db.Float, default=0)

    subtotal = db.Column(db.Float, default=0)  # Suma de los elementos de línea
    total_amount = db.Column(db.Float, default=0)  # Total final después de comisiones, impuestos y descuentos

    status = db.Column(db.String(20), default='unpaid')  # unpaid, paid, overdue, cancelled
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # الحسابات المرتبطة بالفواتير
    pending_account_id = db.Column(db.Integer, db.ForeignKey('accounts.id'))  # حساب المال المعلق
    paid_account_id = db.Column(db.Integer, db.ForeignKey('accounts.id'))  # حساب الفواتير المدفوعة
    overdue_account_id = db.Column(db.Integer, db.ForeignKey('accounts.id'))  # حساب المبالغ المتأخرة

    # Relationships
    client_id = db.Column(db.Integer, db.ForeignKey('clients.id'))
    project_id = db.Column(db.Integer, db.ForeignKey('projects.id'))
    created_by_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    created_by = db.relationship('User', backref='created_invoices')

    # العلاقات مع الحسابات
    pending_account = db.relationship('Account', foreign_keys=[pending_account_id])
    paid_account = db.relationship('Account', foreign_keys=[paid_account_id])
    overdue_account = db.relationship('Account', foreign_keys=[overdue_account_id])

    # Invoice items
    items = db.relationship('InvoiceItem', backref='invoice', lazy='dynamic', cascade='all, delete-orphan')

    # Invoice attachments and verification links
    attachments = db.relationship('InvoiceAttachment', backref='invoice', lazy='dynamic', cascade='all, delete-orphan')
    verification_links = db.relationship('InvoiceVerificationLink', backref='invoice', lazy='dynamic', cascade='all, delete-orphan')

    # Transactions related to this invoice
    transactions = db.relationship('Transaction', backref='invoice', lazy='dynamic')

    def __repr__(self):
        return f'<Invoice {self.invoice_number}>'

    def is_overdue(self):
        if self.due_date and self.status == 'unpaid':
            return datetime.utcnow() > self.due_date
        return False

    def calculate_subtotal(self):
        """Calcula el subtotal sumando todos los elementos de línea"""
        return sum(item.total_price for item in self.items)

    def calculate_transfer_fee(self):
        """Calcula la comisión de transferencia"""
        if self.transfer_fee_type == 'percentage':
            return (self.transfer_fee_value / 100) * self.subtotal
        else:
            return self.transfer_fee_value

    def calculate_tax(self):
        """Calcula el impuesto"""
        if self.tax_type == 'percentage':
            return (self.tax_value / 100) * self.subtotal
        else:
            return self.tax_value

    def calculate_discount(self):
        """Calcula el descuento"""
        if self.discount_type == 'percentage':
            return (self.discount_value / 100) * self.subtotal
        else:
            return self.discount_value

    def calculate_total(self):
        """Calcula el total final"""
        self.subtotal = self.calculate_subtotal()
        transfer_fee = self.calculate_transfer_fee()
        tax = self.calculate_tax()
        discount = self.calculate_discount()

        total = self.subtotal + transfer_fee + tax - discount
        return max(0, total)  # Asegurarse de que el total no sea negativo

    def update_total(self):
        """Actualiza los campos subtotal y total_amount"""
        self.subtotal = self.calculate_subtotal()
        self.total_amount = self.calculate_total()

    def update_account_balances(self, old_status=None):
        """تحديث أرصدة الحسابات بناءً على حالة الفاتورة"""
        if old_status and old_status != 'unpaid':
            # إزالة التأثير السابق
            if old_status == 'pending' and self.pending_account_id:
                pending_account = Account.query.get(self.pending_account_id)
                if pending_account:
                    pending_account.update_balance(self.total_amount, 'subtract')
            elif old_status == 'paid' and self.paid_account_id:
                paid_account = Account.query.get(self.paid_account_id)
                if paid_account:
                    paid_account.update_balance(self.total_amount, 'subtract')
            elif old_status == 'overdue' and self.overdue_account_id:
                overdue_account = Account.query.get(self.overdue_account_id)
                if overdue_account:
                    overdue_account.update_balance(self.total_amount, 'subtract')

        # تطبيق التأثير الجديد
        if self.status == 'pending' and self.pending_account_id:
            pending_account = Account.query.get(self.pending_account_id)
            if pending_account:
                pending_account.update_balance(self.total_amount, 'add')
        elif self.status == 'paid' and self.paid_account_id:
            paid_account = Account.query.get(self.paid_account_id)
            if paid_account:
                paid_account.update_balance(self.total_amount, 'add')
        elif self.status == 'overdue' and self.overdue_account_id:
            overdue_account = Account.query.get(self.overdue_account_id)
            if overdue_account:
                overdue_account.update_balance(self.total_amount, 'add')

    def mark_as_paid(self):
        old_status = self.status
        self.status = 'paid'
        self.update_account_balances(old_status)

        # Create a transaction for this payment
        transaction = Transaction(
            amount=self.total_amount,
            transaction_type='income',
            category='invoice_payment',
            description=f'Payment for invoice {self.invoice_number}',
            invoice_id=self.id
        )
        db.session.add(transaction)

class InvoiceItem(db.Model):
    __tablename__ = 'invoice_items'

    id = db.Column(db.Integer, primary_key=True)
    description = db.Column(db.String(255), nullable=False)
    quantity = db.Column(db.Float, default=1)
    unit_price = db.Column(db.Float, nullable=False)

    # Relationships
    invoice_id = db.Column(db.Integer, db.ForeignKey('invoices.id'))
    supervisor_name = db.Column(db.String(255))  # اسم الموظف المشرف
    supervisor_manual = db.Column(db.String(255))  # موظف يدوي من شركة أخرى

    @property
    def total_price(self):
        return self.quantity * self.unit_price

    def __repr__(self):
        return f'<InvoiceItem {self.description}>'


class InvoiceAttachment(db.Model):
    __tablename__ = 'invoice_attachments'

    id = db.Column(db.Integer, primary_key=True)
    filename = db.Column(db.String(255), nullable=False)
    file_path = db.Column(db.String(255), nullable=False)
    file_type = db.Column(db.String(50))
    file_size = db.Column(db.Integer)  # Size in bytes
    uploaded_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    invoice_id = db.Column(db.Integer, db.ForeignKey('invoices.id'))
    uploaded_by_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    uploaded_by = db.relationship('User', backref='uploaded_attachments')

    def __repr__(self):
        return f'<InvoiceAttachment {self.filename}>'


class InvoiceVerificationLink(db.Model):
    __tablename__ = 'invoice_verification_links'

    id = db.Column(db.Integer, primary_key=True)
    url = db.Column(db.String(500), nullable=False)
    description = db.Column(db.String(255))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    invoice_id = db.Column(db.Integer, db.ForeignKey('invoices.id'), nullable=False)
    added_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    added_by = db.relationship('User', backref='invoice_verification_links')

    def __repr__(self):
        return f'<InvoiceVerificationLink {self.url}>'


class TransactionAttachment(db.Model):
    __tablename__ = 'transaction_attachments'

    id = db.Column(db.Integer, primary_key=True)
    filename = db.Column(db.String(255), nullable=False)
    file_path = db.Column(db.String(255), nullable=False)
    file_type = db.Column(db.String(50))
    file_size = db.Column(db.Integer)  # Size in bytes
    uploaded_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    transaction_id = db.Column(db.Integer, db.ForeignKey('transactions.id'), nullable=False)
    uploaded_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    uploaded_by = db.relationship('User', backref='transaction_attachments')

    def __repr__(self):
        return f'<TransactionAttachment {self.filename}>'


class TransactionVerificationLink(db.Model):
    __tablename__ = 'transaction_verification_links'

    id = db.Column(db.Integer, primary_key=True)
    url = db.Column(db.String(500), nullable=False)
    description = db.Column(db.String(255))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    transaction_id = db.Column(db.Integer, db.ForeignKey('transactions.id'), nullable=False)
    added_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    added_by = db.relationship('User', backref='transaction_verification_links')

    def __repr__(self):
        return f'<TransactionVerificationLink {self.url}>'
