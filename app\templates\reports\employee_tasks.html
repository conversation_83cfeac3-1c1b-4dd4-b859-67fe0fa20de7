{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
        <a href="{{ url_for('report.index') }}" class="d-none d-sm-inline-block btn btn-sm btn-primary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> العودة للتقارير
        </a>
    </div>

    <!-- تقرير مهام الموظفين -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">خيارات التقرير</h6>
        </div>
        <div class="card-body">
            <form action="{{ url_for('report.export', report_type='employee_tasks') }}" method="POST">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label for="employee_id" class="form-label">الموظف</label>
                        <select class="form-select" id="employee_id" name="employee_id">
                            <option value="all" selected>جميع الموظفين</option>
                            {% for employee in all_employees %}
                            <option value="{{ employee.id }}">{{ employee.get_full_name() }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="date_from" class="form-label">من تاريخ</label>
                        <input type="date" class="form-control" id="date_from" name="date_from">
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="date_to" class="form-label">إلى تاريخ</label>
                        <input type="date" class="form-control" id="date_to" name="date_to">
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="export_format" class="form-label">تنسيق التصدير</label>
                        <select class="form-select" id="export_format" name="export_format">
                            <option value="pdf" selected>PDF</option>
                            <option value="excel">Excel</option>
                        </select>
                    </div>
                </div>
                <div class="d-flex justify-content-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-file-export me-1"></i>تصدير التقرير
                    </button>
                    <a href="{{ url_for('report.employee_tasks') }}" class="btn btn-secondary">
                        <i class="fas fa-redo me-1"></i>إعادة تعيين
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- معاينة البيانات -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">معاينة البيانات</h6>
            <div class="d-flex align-items-center">
                <select class="form-select form-select-sm me-2" onchange="changePerPage(this.value)" style="width: auto;">
                    <option value="25" {% if per_page == 25 %}selected{% endif %}>25 موظف</option>
                    <option value="50" {% if per_page == 50 %}selected{% endif %}>50 موظف</option>
                    <option value="100" {% if per_page == 100 %}selected{% endif %}>100 موظف</option>
                </select>
            </div>
        </div>
        <div class="card-body">
            {% if employees %}
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>#</th>
                            <th>الاسم</th>
                            <th>إجمالي المهام</th>
                            <th>المهام المكتملة</th>
                            <th>المهام الجارية</th>
                            <th>المهام المعلقة</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% if employees.items %}
                            {% for employee in employees.items %}
                            <tr>
                                <td>{{ loop.index + ((employees.page - 1) * employees.per_page) }}</td>
                                <td>{{ employee.get_full_name() }}</td>
                                <td>
                                    <span class="badge bg-info">{{ employee.total_tasks }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-success">{{ employee.completed_tasks }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-warning">{{ employee.in_progress_tasks }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-secondary">{{ employee.pending_tasks }}</span>
                                </td>
                            </tr>
                            {% endfor %}
                        {% else %}
                            {% for employee in employees %}
                            <tr>
                                <td>{{ loop.index }}</td>
                                <td>{{ employee.get_full_name() }}</td>
                                <td>
                                    <span class="badge bg-info">{{ employee.total_tasks }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-success">{{ employee.completed_tasks }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-warning">{{ employee.in_progress_tasks }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-secondary">{{ employee.pending_tasks }}</span>
                                </td>
                            </tr>
                            {% endfor %}
                        {% endif %}
                    </tbody>
                </table>
            </div>
            
            <!-- تقسيم الصفحات -->
            {% if employees.items and employees.pages > 1 %}
            <div class="d-flex justify-content-between align-items-center mt-3">
                <div>
                    <small class="text-muted">
                        عرض {{ employees.per_page * (employees.page - 1) + 1 }} إلى 
                        {{ employees.per_page * (employees.page - 1) + employees.items|length }} 
                        من {{ employees.total }} موظف
                    </small>
                </div>
                <nav aria-label="تقسيم صفحات الموظفين">
                    <ul class="pagination pagination-sm mb-0">
                        {% if employees.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('report.employee_tasks', page=employees.prev_num, per_page=per_page) }}">السابق</a>
                        </li>
                        {% endif %}
                        
                        {% for page_num in employees.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != employees.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('report.employee_tasks', page=page_num, per_page=per_page) }}">{{ page_num }}</a>
                                </li>
                                {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                                {% endif %}
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">…</span>
                            </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if employees.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('report.employee_tasks', page=employees.next_num, per_page=per_page) }}">التالي</a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
            {% endif %}
            {% else %}
            <div class="text-center py-4">
                <p class="text-muted mb-0">لا توجد بيانات مهام لعرضها</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function changePerPage(perPage) {
    const url = new URL(window.location);
    url.searchParams.set('per_page', perPage);
    url.searchParams.set('page', 1); // Reset to first page
    window.location.href = url.toString();
}

$(document).ready(function() {
    // Initialize select2
    $('#employee_id, #export_format').select2({
        theme: 'bootstrap-5',
        width: '100%',
        dir: 'rtl'
    });
});
</script>
{% endblock %}
