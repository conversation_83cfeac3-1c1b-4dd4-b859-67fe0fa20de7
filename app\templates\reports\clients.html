{% extends 'base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">تقرير العملاء</h1>
        <a href="{{ url_for('report.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i>العودة إلى التقارير
        </a>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">خيارات التقرير</h5>
                </div>
                <div class="card-body">
                    <form action="{{ url_for('report.export', report_type='clients') }}" method="POST">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <label for="date_from" class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="date_from" name="date_from">
                                <small class="text-muted">فلترة المشاريع من هذا التاريخ</small>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="date_to" class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="date_to" name="date_to">
                                <small class="text-muted">فلترة المشاريع حتى هذا التاريخ</small>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="search" class="form-label">بحث</label>
                                <input type="text" class="form-control" id="search" name="search" placeholder="بحث بالاسم أو البريد الإلكتروني">
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="export_format" class="form-label">تنسيق التصدير</label>
                                <select class="form-select" id="export_format" name="export_format">
                                    <option value="pdf" selected>PDF</option>
                                    <option value="excel">Excel</option>
                                </select>
                            </div>
                        </div>
                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-file-export me-1"></i>تصدير التقرير
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">معاينة البيانات</h5>
                    <div class="d-flex align-items-center">
                        <select class="form-select form-select-sm me-2" onchange="changePerPage(this.value)" style="width: auto;">
                            <option value="25" {% if per_page == 25 %}selected{% endif %}>25 عميل</option>
                            <option value="50" {% if per_page == 50 %}selected{% endif %}>50 عميل</option>
                            <option value="100" {% if per_page == 100 %}selected{% endif %}>100 عميل</option>
                        </select>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>#</th>
                                    <th>اسم العميل</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>رقم الهاتف</th>
                                    <th>العنوان</th>
                                    <th>إجمالي المشاريع</th>
                                    <th>المشاريع المكتملة</th>
                                    <th>المشاريع الجارية</th>
                                    <th>المشاريع المعلقة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if clients.items %}
                                    {% for client in clients.items %}
                                    <tr>
                                        <td>{{ loop.index + ((clients.page - 1) * clients.per_page) }}</td>
                                        <td>{{ client.name }}</td>
                                        <td>{{ client.email or 'غير محدد' }}</td>
                                        <td>{{ client.phone or 'غير محدد' }}</td>
                                        <td>{{ client.address or 'غير محدد' }}</td>
                                        <td>
                                            <span class="badge bg-info">{{ client.projects|length }}</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-success">{{ client.projects|selectattr('status', 'equalto', 'completed')|list|length }}</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-warning">{{ client.projects|selectattr('status', 'equalto', 'in_progress')|list|length }}</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">{{ client.projects|selectattr('status', 'equalto', 'pending')|list|length }}</span>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    {% for client in clients %}
                                    <tr>
                                        <td>{{ loop.index }}</td>
                                        <td>{{ client.name }}</td>
                                        <td>{{ client.email or 'غير محدد' }}</td>
                                        <td>{{ client.phone or 'غير محدد' }}</td>
                                        <td>{{ client.address or 'غير محدد' }}</td>
                                        <td>
                                            <span class="badge bg-info">{{ client.projects|length }}</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-success">{{ client.projects|selectattr('status', 'equalto', 'completed')|list|length }}</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-warning">{{ client.projects|selectattr('status', 'equalto', 'in_progress')|list|length }}</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">{{ client.projects|selectattr('status', 'equalto', 'pending')|list|length }}</span>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                {% endif %}
                            </tbody>
                        </table>
                    </div>

                    <!-- تقسيم الصفحات -->
                    {% if clients.items and clients.pages > 1 %}
                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <div>
                            <small class="text-muted">
                                عرض {{ clients.per_page * (clients.page - 1) + 1 }} إلى
                                {{ clients.per_page * (clients.page - 1) + clients.items|length }}
                                من {{ clients.total }} عميل
                            </small>
                        </div>
                        <nav aria-label="تقسيم صفحات العملاء">
                            <ul class="pagination pagination-sm mb-0">
                                {% if clients.has_prev %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('report.clients', page=clients.prev_num, per_page=per_page) }}">السابق</a>
                                </li>
                                {% endif %}

                                {% for page_num in clients.iter_pages() %}
                                    {% if page_num %}
                                        {% if page_num != clients.page %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('report.clients', page=page_num, per_page=per_page) }}">{{ page_num }}</a>
                                        </li>
                                        {% else %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ page_num }}</span>
                                        </li>
                                        {% endif %}
                                    {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link">…</span>
                                    </li>
                                    {% endif %}
                                {% endfor %}

                                {% if clients.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('report.clients', page=clients.next_num, per_page=per_page) }}">التالي</a>
                                </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function changePerPage(perPage) {
    const url = new URL(window.location);
    url.searchParams.set('per_page', perPage);
    url.searchParams.set('page', 1); // Reset to first page
    window.location.href = url.toString();
}

$(document).ready(function() {
    // Initialize select2
    $('#has_projects, #export_format').select2({
        theme: 'bootstrap-5',
        width: '100%',
        dir: 'rtl'
    });
});
</script>
{% endblock %}
