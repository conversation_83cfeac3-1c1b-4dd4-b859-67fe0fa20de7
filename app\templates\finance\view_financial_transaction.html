{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
        <div>
            {% if current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance') %}
            <a href="{{ url_for('finance.edit_financial_transaction', id=transaction.id) }}" class="btn btn-warning me-2">
                <i class="fas fa-edit me-1"></i>تعديل المعاملة
            </a>
            {% endif %}
            <a href="{{ url_for('finance.financial_transactions') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-1"></i>العودة إلى المعاملات
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-exchange-alt me-2"></i>تفاصيل المعاملة المالية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-muted">معلومات المعاملة</h6>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>رقم المعاملة:</strong></td>
                                    <td>{{ transaction.transaction_number }}</td>
                                </tr>
                                <tr>
                                    <td><strong>اسم المعاملة:</strong></td>
                                    <td>{{ transaction.transaction_name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>الحالة:</strong></td>
                                    <td>
                                        {% if transaction.status == 'draft' %}
                                        <span class="badge bg-secondary">مسودة</span>
                                        {% elif transaction.status == 'pending' %}
                                        <span class="badge bg-warning">معلقة</span>
                                        {% elif transaction.status == 'paid' %}
                                        <span class="badge bg-success">مدفوعة</span>
                                        {% elif transaction.status == 'cancelled' %}
                                        <span class="badge bg-danger">ملغية</span>
                                        {% endif %}
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted">معلومات إضافية</h6>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>أنشأ بواسطة:</strong></td>
                                    <td>{{ transaction.created_by.first_name }} {{ transaction.created_by.last_name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>تاريخ الإنشاء:</strong></td>
                                    <td>{{ transaction.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                </tr>
                                <tr>
                                    <td><strong>آخر تحديث:</strong></td>
                                    <td>{{ transaction.updated_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    {% if transaction.notes %}
                    <div class="row mt-3">
                        <div class="col-md-12">
                            <h6 class="text-muted">الملاحظات</h6>
                            <div class="alert alert-info">
                                {{ transaction.notes }}
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- بنود المعاملة -->
            <div class="card shadow mt-4">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-list me-2"></i>بنود المعاملة ({{ transaction.items.count() }})
                    </h6>
                </div>
                <div class="card-body">
                    {% if transaction.items.count() > 0 %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>الحساب</th>
                                    <th>الإجراء</th>
                                    <th>المبلغ والعملة</th>
                                    <th>ملاحظة البند</th>
                                    <th>تاريخ الإجراء</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in transaction.items %}
                                <tr>
                                    <td>{{ loop.index }}</td>
                                    <td>
                                        <div>
                                            <strong>{{ item.account.name }}</strong>
                                            {% if item.account.account_number %}
                                            <br><small class="text-muted">{{ item.account.account_number }}</small>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        {% if item.action == 'add' %}
                                        <span class="badge bg-success">إضافة</span>
                                        {% else %}
                                        <span class="badge bg-danger">خصم</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <strong class="{% if item.action == 'add' %}text-success{% else %}text-danger{% endif %}">
                                            {% if item.action == 'add' %}+{% else %}-{% endif %}{{ "{:,.2f}".format(item.amount) }}
                                        </strong>
                                        <br><small class="text-muted">{{ item.currency.code if item.currency else 'USD' }}</small>
                                    </td>
                                    <td>
                                        {% if item.item_notes %}
                                        <small class="text-muted">{{ item.item_notes }}</small>
                                        {% else %}
                                        <small class="text-muted">-</small>
                                        {% endif %}
                                    </td>
                                    <td>{{ item.transaction_date.strftime('%Y-%m-%d %H:%M') }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                            <tfoot>
                                <tr class="table-light">
                                    <td colspan="3" class="text-end"><strong>إجمالي التأثير:</strong></td>
                                    <td>
                                        {% set total_add = transaction.items.filter_by(action='add').all() | sum(attribute='amount') %}
                                        {% set total_subtract = transaction.items.filter_by(action='subtract').all() | sum(attribute='amount') %}
                                        <strong class="{% if total_add >= total_subtract %}text-success{% else %}text-danger{% endif %}">
                                            ${{ "{:,.2f}".format(total_add - total_subtract) }}
                                        </strong>
                                    </td>
                                    <td></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-list fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد بنود في هذه المعاملة</p>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- المرفقات والروابط -->
            {% if transaction.attachments.count() > 0 or transaction.links.count() > 0 %}
            <div class="card shadow mt-4">
                <div class="card-header bg-secondary text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-paperclip me-2"></i>المرفقات والروابط
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% if transaction.attachments.count() > 0 %}
                        <div class="col-md-6">
                            <h6 class="text-muted">المرفقات ({{ transaction.attachments.count() }})</h6>
                            <div class="list-group">
                                {% for attachment in transaction.attachments %}
                                <a href="{{ url_for('static', filename='uploads/' + attachment.filepath) }}" 
                                   class="list-group-item list-group-item-action" target="_blank">
                                    <i class="fas fa-file me-2"></i>{{ attachment.filename }}
                                    <small class="text-muted d-block">
                                        {{ attachment.uploaded_at.strftime('%Y-%m-%d %H:%M') }}
                                    </small>
                                </a>
                                {% endfor %}
                            </div>
                        </div>
                        {% endif %}
                        {% if transaction.links.count() > 0 %}
                        <div class="col-md-6">
                            <h6 class="text-muted">الروابط ({{ transaction.links.count() }})</h6>
                            <div class="list-group">
                                {% for link in transaction.links %}
                                <a href="{{ link.url }}" class="list-group-item list-group-item-action" target="_blank">
                                    <i class="fas fa-link me-2"></i>
                                    {% if link.description %}
                                    {{ link.description }}
                                    {% else %}
                                    {{ link.url }}
                                    {% endif %}
                                    <small class="text-muted d-block">
                                        {{ link.created_at.strftime('%Y-%m-%d %H:%M') }}
                                    </small>
                                </a>
                                {% endfor %}
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endif %}
        </div>

        <div class="col-md-4">
            <!-- إجراءات سريعة -->
            {% if current_user.has_role('admin') or current_user.has_role('finance') %}
            <div class="card shadow">
                <div class="card-header bg-warning text-dark">
                    <h6 class="mb-0">إجراءات سريعة</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        {% if transaction.status == 'draft' %}
                        <button class="btn btn-warning btn-sm" onclick="changeStatus('pending')">
                            <i class="fas fa-clock me-1"></i>تعليق المعاملة
                        </button>
                        <button class="btn btn-success btn-sm" onclick="changeStatus('paid')">
                            <i class="fas fa-check me-1"></i>تأكيد الدفع
                        </button>
                        {% elif transaction.status == 'pending' %}
                        <button class="btn btn-success btn-sm" onclick="changeStatus('paid')">
                            <i class="fas fa-check me-1"></i>تأكيد الدفع
                        </button>
                        <button class="btn btn-secondary btn-sm" onclick="changeStatus('draft')">
                            <i class="fas fa-edit me-1"></i>إرجاع للمسودة
                        </button>
                        {% elif transaction.status == 'paid' %}
                        <button class="btn btn-warning btn-sm" onclick="changeStatus('pending')">
                            <i class="fas fa-undo me-1"></i>إلغاء الدفع
                        </button>
                        {% endif %}
                        {% if transaction.status != 'cancelled' %}
                        <button class="btn btn-danger btn-sm" onclick="changeStatus('cancelled')">
                            <i class="fas fa-times me-1"></i>إلغاء المعاملة
                        </button>
                        {% endif %}
                        <hr>
                        <a href="{{ url_for('finance.delete_financial_transaction', id=transaction.id) }}" 
                           class="btn btn-outline-danger btn-sm">
                            <i class="fas fa-trash me-1"></i>حذف المعاملة
                        </a>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- ملخص التأثير على الحسابات -->
            {% if transaction.status == 'paid' %}
            <div class="card shadow mt-4">
                <div class="card-header bg-success text-white">
                    <h6 class="mb-0">تأثير المعاملة على الحسابات</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        تم تطبيق هذه المعاملة على أرصدة الحسابات
                    </div>
                    {% for item in transaction.items %}
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <div>
                            <small class="text-muted">{{ item.account.name }}</small>
                        </div>
                        <div>
                            <span class="badge {% if item.action == 'add' %}bg-success{% else %}bg-danger{% endif %}">
                                {% if item.action == 'add' %}+{% else %}-{% endif %}${{ "{:,.2f}".format(item.amount) }}
                            </span>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function changeStatus(newStatus) {
    if (confirm('هل أنت متأكد من تغيير حالة المعاملة؟')) {
        // يمكن إضافة AJAX request هنا لتغيير الحالة
        window.location.href = `{{ url_for('finance.edit_financial_transaction', id=transaction.id) }}?status=${newStatus}`;
    }
}
</script>
{% endblock %}
