from app import db
from datetime import datetime

class SystemMessage(db.Model):
    """نموذج الرسائل المثبتة في النظام"""
    __tablename__ = 'system_messages'

    id = db.Column(db.Integer, primary_key=True)
    page_name = db.Column(db.String(100), nullable=False)  # اسم الصفحة
    message_type = db.Column(db.String(20), default='info')  # info, warning, success, danger
    title = db.Column(db.String(200), nullable=False)  # عنوان الرسالة
    content = db.Column(db.Text, nullable=False)  # محتوى الرسالة
    is_active = db.Column(db.<PERSON>, default=True)  # هل الرسالة نشطة
    is_dismissible = db.Column(db.<PERSON>, default=True)  # هل يمكن إغلاق الرسالة
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by_id = db.Column(db.Integer, db.ForeignKey('users.id'))

    # العلاقات
    created_by = db.relationship('User', backref='system_messages')

    def __repr__(self):
        return f'<SystemMessage {self.page_name}: {self.title}>'

    @staticmethod
    def get_message_for_page(page_name):
        """الحصول على الرسالة النشطة لصفحة معينة"""
        return SystemMessage.query.filter_by(
            page_name=page_name,
            is_active=True
        ).first()

    def get_bootstrap_class(self):
        """الحصول على فئة Bootstrap للرسالة"""
        type_mapping = {
            'info': 'alert-info',
            'warning': 'alert-warning',
            'success': 'alert-success',
            'danger': 'alert-danger'
        }
        return type_mapping.get(self.message_type, 'alert-info')

    def get_icon_class(self):
        """الحصول على أيقونة الرسالة"""
        icon_mapping = {
            'info': 'fas fa-info-circle',
            'warning': 'fas fa-exclamation-triangle',
            'success': 'fas fa-check-circle',
            'danger': 'fas fa-exclamation-circle'
        }
        return icon_mapping.get(self.message_type, 'fas fa-info-circle')
