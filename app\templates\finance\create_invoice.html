{% extends 'base.html' %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1>إنشاء فاتورة جديدة</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('dashboard.index') }}">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('finance.index') }}">لوحة المالية</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('finance.invoices') }}">الفواتير</a></li>
                    <li class="breadcrumb-item active" aria-current="page">إنشاء فاتورة جديدة</li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- إعدادات الحسابات -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow border-info">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-cog me-2"></i>إعدادات الحسابات
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="pending_account_id" class="form-label">حساب المال المعلق</label>
                                <select class="form-select" id="pending_account_id" name="pending_account_id">
                                    <option value="">اختر الحساب</option>
                                    {% for account in accounts %}
                                    <option value="{{ account.id }}">{{ account.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="paid_account_id" class="form-label">حساب الفواتير المدفوعة</label>
                                <select class="form-select" id="paid_account_id" name="paid_account_id">
                                    <option value="">اختر الحساب</option>
                                    {% for account in accounts %}
                                    <option value="{{ account.id }}">{{ account.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="overdue_account_id" class="form-label">حساب المبالغ المتأخرة</label>
                                <select class="form-select" id="overdue_account_id" name="overdue_account_id">
                                    <option value="">اختر الحساب</option>
                                    {% for account in accounts %}
                                    <option value="{{ account.id }}">{{ account.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header bg-light">
                    <h5 class="mb-0">إنشاء فاتورة جديدة</h5>
                </div>
                <div class="card-body">
                    <form action="{{ url_for('finance.add_invoice') }}" method="POST" enctype="multipart/form-data">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="invoice_number" class="form-label">رقم الفاتورة</label>
                                <input type="text" class="form-control" id="invoice_number" name="invoice_number" required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="client_id" class="form-label">العميل</label>
                                <div class="position-relative">
                                    <input type="text" class="form-control" id="client_search" placeholder="ابحث عن عميل..." autocomplete="off">
                                    <div id="client_search_results" class="position-absolute w-100 mt-1 shadow-sm d-none" style="max-height: 200px; overflow-y: auto; z-index: 1000; background-color: white; border: 1px solid #ced4da; border-radius: 0.25rem;"></div>
                                </div>
                                <select class="form-select d-none" id="client_id" name="client_id" required>
                                    {% for client in clients %}
                                    <option value="{{ client.id }}">{{ client.name }}</option>
                                    {% endfor %}
                                </select>
                                <div id="selected_client" class="mt-2 d-none">
                                    <div class="d-flex align-items-center">
                                        <span class="badge bg-success me-2" id="selected_client_name"></span>
                                        <button type="button" class="btn btn-sm btn-outline-danger" id="clear_client">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="project_id" class="form-label">المشروع (اختياري)</label>
                                <div class="position-relative">
                                    <input type="text" class="form-control" id="project_search" placeholder="ابحث عن مشروع..." autocomplete="off">
                                    <div id="project_search_results" class="position-absolute w-100 mt-1 shadow-sm d-none" style="max-height: 200px; overflow-y: auto; z-index: 1000; background-color: white; border: 1px solid #ced4da; border-radius: 0.25rem;"></div>
                                </div>
                                <select class="form-select d-none" id="project_id" name="project_id">
                                    <option value="">-- بدون مشروع --</option>
                                    {% for project in projects %}
                                    <option value="{{ project.id }}">{{ project.name }}</option>
                                    {% endfor %}
                                </select>
                                <div id="selected_project" class="mt-2 d-none">
                                    <div class="d-flex align-items-center">
                                        <span class="badge bg-primary me-2" id="selected_project_name"></span>
                                        <button type="button" class="btn btn-sm btn-outline-danger" id="clear_project">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="issue_date" class="form-label">تاريخ الإصدار</label>
                                <input type="date" class="form-control" id="issue_date" name="issue_date" value="{{ now.strftime('%Y-%m-%d') }}" required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="due_date" class="form-label">تاريخ الاستحقاق</label>
                                <input type="date" class="form-control" id="due_date" name="due_date">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="approval_date" class="form-label">تاريخ الموافقة</label>
                                <input type="date" class="form-control" id="approval_date" name="approval_date">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="status" class="form-label">الحالة</label>
                            <select class="form-select" id="status" name="status" required>
                                <option value="pending" selected>معلق</option>
                                <option value="paid">مدفوع</option>
                                <option value="overdue">متأخر</option>
                                <option value="cancelled">ملغي</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="notes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                        </div>

                        <h5 class="mt-4 mb-3">عناصر الفاتورة</h5>
                        <div id="invoice-items">
                            <div class="invoice-item card mb-3">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-12 mb-3">
                                            <label class="form-label">الوصف</label>
                                            <input type="text" class="form-control" name="item_description[]" required>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-3 mb-3">
                                            <label class="form-label">الكمية</label>
                                            <input type="number" step="0.01" class="form-control item-quantity" name="item_quantity[]" value="1" required>
                                        </div>
                                        <div class="col-md-3 mb-3">
                                            <label class="form-label">سعر الوحدة</label>
                                            <div class="input-group">
                                                <span class="input-group-text">$</span>
                                                <input type="number" step="0.01" class="form-control item-price" name="item_unit_price[]" value="0" required>
                                            </div>
                                        </div>

                                        <div class="col-md-3 mb-3">
                                            <label class="form-label">المجموع</label>
                                            <div class="input-group">
                                                <span class="input-group-text">$</span>
                                                <input type="text" class="form-control item-total" value="0" readonly>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">الموظف المشرف</label>
                                            <input type="text" class="form-control" name="item_supervisor_name[]" placeholder="اسم الموظف المشرف">
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">موظف من شركة أخرى (اختياري)</label>
                                            <input type="text" class="form-control" name="item_supervisor_manual[]" placeholder="تفاصيل إضافية">
                                        </div>
                                    </div>
                                    <button type="button" class="btn btn-sm btn-danger remove-item">حذف العنصر</button>
                                </div>
                            </div>
                        </div>

                        <button type="button" class="btn btn-secondary mb-4" id="add-item-btn">
                            <i class="fas fa-plus me-1"></i>إضافة عنصر
                        </button>

                        <h5 class="mt-4 mb-3">الرسوم والضرائب والخصومات</h5>
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label class="form-label">عمولة التحويل</label>
                                <div class="input-group mb-2">
                                    <select class="form-select" name="transfer_fee_type" id="transfer_fee_type">
                                        <option value="percentage" selected>نسبة مئوية</option>
                                        <option value="fixed">مبلغ ثابت</option>
                                    </select>
                                </div>
                                <div class="input-group">
                                    <input type="number" step="0.01" class="form-control" name="transfer_fee_value" id="transfer_fee_value" value="0">
                                    <span class="input-group-text" id="transfer_fee_symbol">%</span>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label">الضريبة</label>
                                <div class="input-group mb-2">
                                    <select class="form-select" name="tax_type" id="tax_type">
                                        <option value="percentage" selected>نسبة مئوية</option>
                                        <option value="fixed">مبلغ ثابت</option>
                                    </select>
                                </div>
                                <div class="input-group">
                                    <input type="number" step="0.01" class="form-control" name="tax_value" id="tax_value" value="0">
                                    <span class="input-group-text" id="tax_symbol">%</span>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label">الخصم</label>
                                <div class="input-group mb-2">
                                    <select class="form-select" name="discount_type" id="discount_type">
                                        <option value="percentage" selected>نسبة مئوية</option>
                                        <option value="fixed">مبلغ ثابت</option>
                                    </select>
                                </div>
                                <div class="input-group">
                                    <input type="number" step="0.01" class="form-control" name="discount_value" id="discount_value" value="0">
                                    <span class="input-group-text" id="discount_symbol">%</span>
                                </div>
                            </div>
                        </div>

                        <h5 class="mt-4 mb-3">المرفقات</h5>
                        <div class="mb-3">
                            <label for="attachments" class="form-label">إضافة مرفقات</label>
                            <input class="form-control" type="file" id="attachments" name="attachments" multiple>
                        </div>

                        <h5 class="mt-4 mb-3">روابط الإثبات (اختياري)</h5>
                        <div class="mb-3">
                            <div id="verification-links-container">
                                <div class="verification-link-item mb-2 row">
                                    <div class="col-md-5">
                                        <input type="url" class="form-control" name="verification_links[]" placeholder="أدخل الرابط هنا">
                                    </div>
                                    <div class="col-md-5">
                                        <input type="text" class="form-control" name="verification_descriptions[]" placeholder="وصف الرابط (اختياري)">
                                    </div>
                                    <div class="col-md-2">
                                        <button type="button" class="btn btn-danger remove-link"><i class="fas fa-times"></i></button>
                                    </div>
                                </div>
                            </div>
                            <button type="button" class="btn btn-sm btn-secondary mt-2" id="add-link-btn">
                                <i class="fas fa-plus me-1"></i>إضافة رابط آخر
                            </button>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('finance.invoices') }}" class="btn btn-secondary">إلغاء</a>
                            <button type="submit" class="btn btn-primary">إنشاء الفاتورة</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Form validation
        const form = document.querySelector('form');
        form.addEventListener('submit', function(event) {
            // Validate client selection
            const clientSelect = document.getElementById('client_id');
            if (!clientSelect.value) {
                event.preventDefault();
                alert('يرجى اختيار عميل');
                document.getElementById('client_search').focus();
                return false;
            }

            // Validate other required fields
            const requiredFields = form.querySelectorAll('input[required], select[required], textarea[required]');
            let isValid = true;

            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    isValid = false;
                    field.classList.add('is-invalid');
                } else {
                    field.classList.remove('is-invalid');
                }
            });

            if (!isValid) {
                event.preventDefault();
                alert('يرجى ملء جميع الحقول المطلوبة');
                return false;
            }
        });
        // Add new item
        const addItemBtn = document.getElementById('add-item-btn');
        const invoiceItems = document.getElementById('invoice-items');

        addItemBtn.addEventListener('click', function() {
            const itemTemplate = `
                <div class="invoice-item card mb-3">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label class="form-label">الوصف</label>
                                <input type="text" class="form-control" name="item_description[]" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <label class="form-label">الكمية</label>
                                <input type="number" step="0.01" class="form-control item-quantity" name="item_quantity[]" value="1" required>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label class="form-label">سعر الوحدة</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" step="0.01" class="form-control item-price" name="item_unit_price[]" value="0" required>
                                </div>
                            </div>

                            <div class="col-md-3 mb-3">
                                <label class="form-label">المجموع</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="text" class="form-control item-total" value="0" readonly>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الموظف المشرف</label>
                                <input type="text" class="form-control" name="item_supervisor_name[]" placeholder="اسم الموظف المشرف">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">موظف من شركة أخرى (اختياري)</label>
                                <input type="text" class="form-control" name="item_supervisor_manual[]" placeholder="تفاصيل إضافية">
                            </div>
                        </div>
                        <button type="button" class="btn btn-sm btn-danger remove-item">حذف العنصر</button>
                    </div>
                </div>
            `;

            // Add new item to the list
            invoiceItems.insertAdjacentHTML('beforeend', itemTemplate);

            // Add event listeners to the new item
            setupItemEventListeners(invoiceItems.lastElementChild);
        });

        // Setup event listeners for existing items
        document.querySelectorAll('.invoice-item').forEach(item => {
            setupItemEventListeners(item);
        });

        function setupItemEventListeners(item) {
            // Remove item
            item.querySelector('.remove-item').addEventListener('click', function() {
                item.remove();
                updateTotals();
            });

            // Update item total when quantity or price changes
            const quantityInput = item.querySelector('.item-quantity');
            const priceInput = item.querySelector('.item-price');
            const totalInput = item.querySelector('.item-total');

            function updateItemTotal() {
                const quantity = parseFloat(quantityInput.value) || 0;
                const price = parseFloat(priceInput.value) || 0;
                const total = quantity * price;
                totalInput.value = total.toFixed(2);
                updateTotals();
            }

            quantityInput.addEventListener('input', updateItemTotal);
            priceInput.addEventListener('input', updateItemTotal);

            // Update company profit symbol
            const companyProfitType = item.querySelector('.company-profit-type');
            const companyProfitSymbol = item.querySelector('.company-profit-symbol');

            if (companyProfitType && companyProfitSymbol) {
                companyProfitType.addEventListener('change', function() {
                    companyProfitSymbol.textContent = this.value === 'percentage' ? '%' : '$';
                });
            }
        }

        // Update fee, tax, and discount symbols
        const transferFeeType = document.getElementById('transfer_fee_type');
        const transferFeeSymbol = document.getElementById('transfer_fee_symbol');
        const taxType = document.getElementById('tax_type');
        const taxSymbol = document.getElementById('tax_symbol');
        const discountType = document.getElementById('discount_type');
        const discountSymbol = document.getElementById('discount_symbol');

        transferFeeType.addEventListener('change', function() {
            transferFeeSymbol.textContent = this.value === 'percentage' ? '%' : '$';
        });

        taxType.addEventListener('change', function() {
            taxSymbol.textContent = this.value === 'percentage' ? '%' : '$';
        });

        discountType.addEventListener('change', function() {
            discountSymbol.textContent = this.value === 'percentage' ? '%' : '$';
        });

        // Calculate totals
        function updateTotals() {
            // This function would calculate subtotal, fees, taxes, discounts, and total
            // For now, we'll just update the item totals
            document.querySelectorAll('.invoice-item').forEach(item => {
                const quantity = parseFloat(item.querySelector('.item-quantity').value) || 0;
                const price = parseFloat(item.querySelector('.item-price').value) || 0;
                const total = quantity * price;
                item.querySelector('.item-total').value = total.toFixed(2);
            });
        }

        // Initial update
        updateTotals();

        // Handle verification links
        const addLinkBtn = document.getElementById('add-link-btn');
        const linksContainer = document.getElementById('verification-links-container');

        // Add new link field
        addLinkBtn.addEventListener('click', function() {
            const linkItem = document.createElement('div');
            linkItem.className = 'verification-link-item mb-2 row';
            linkItem.innerHTML = `
                <div class="col-md-5">
                    <input type="url" class="form-control" name="verification_links[]" placeholder="أدخل الرابط هنا">
                </div>
                <div class="col-md-5">
                    <input type="text" class="form-control" name="verification_descriptions[]" placeholder="وصف الرابط (اختياري)">
                </div>
                <div class="col-md-2">
                    <button type="button" class="btn btn-danger remove-link"><i class="fas fa-times"></i></button>
                </div>
            `;
            linksContainer.appendChild(linkItem);

            // Add event listener to the new remove button
            const removeBtn = linkItem.querySelector('.remove-link');
            removeBtn.addEventListener('click', function() {
                linksContainer.removeChild(linkItem);
            });
        });

        // Add event listeners to existing remove buttons
        document.querySelectorAll('.remove-link').forEach(button => {
            button.addEventListener('click', function() {
                const linkItem = this.closest('.verification-link-item');
                linksContainer.removeChild(linkItem);
            });
        });

        // Client search functionality
        const clientSearch = document.getElementById('client_search');
        const clientSearchResults = document.getElementById('client_search_results');
        const clientSelect = document.getElementById('client_id');
        const selectedClient = document.getElementById('selected_client');
        const selectedClientName = document.getElementById('selected_client_name');
        const clearClient = document.getElementById('clear_client');

        // Create an array of client objects from the select options
        const clients = Array.from(clientSelect.options).map(option => {
            return {
                id: option.value,
                name: option.textContent
            };
        });

        // Function to filter clients based on search term
        function filterClients(searchTerm) {
            searchTerm = searchTerm.toLowerCase();
            return clients.filter(client =>
                client.name.toLowerCase().includes(searchTerm)
            );
        }

        // Function to display client search results
        function displayClientSearchResults(results) {
            clientSearchResults.innerHTML = '';

            if (results.length === 0) {
                clientSearchResults.innerHTML = '<div class="p-2 text-muted">لا توجد نتائج</div>';
                return;
            }

            results.forEach(client => {
                const resultItem = document.createElement('div');
                resultItem.className = 'p-2 border-bottom client-result';
                resultItem.textContent = client.name;
                resultItem.dataset.id = client.id;
                resultItem.dataset.name = client.name;
                resultItem.style.cursor = 'pointer';

                resultItem.addEventListener('click', function() {
                    // Set the selected client in the hidden select
                    clientSelect.value = this.dataset.id;

                    // Display the selected client
                    selectedClientName.textContent = this.dataset.name;
                    selectedClient.classList.remove('d-none');

                    // Clear the search input and hide results
                    clientSearch.value = '';
                    clientSearchResults.classList.add('d-none');
                });

                clientSearchResults.appendChild(resultItem);
            });
        }

        // Event listener for client search input
        clientSearch.addEventListener('input', function() {
            const searchTerm = this.value.trim();

            if (searchTerm.length > 0) {
                const results = filterClients(searchTerm);
                displayClientSearchResults(results);
                clientSearchResults.classList.remove('d-none');
            } else {
                clientSearchResults.classList.add('d-none');
            }
        });

        // Event listener for clear client button
        clearClient.addEventListener('click', function() {
            clientSelect.value = '';
            selectedClient.classList.add('d-none');
            clientSearch.value = '';
        });

        // Close client search results when clicking outside
        document.addEventListener('click', function(event) {
            if (!clientSearch.contains(event.target) && !clientSearchResults.contains(event.target)) {
                clientSearchResults.classList.add('d-none');
            }
        });

        // Focus event to show all clients when focusing on empty search
        clientSearch.addEventListener('focus', function() {
            if (this.value.trim() === '') {
                displayClientSearchResults(clients);
                clientSearchResults.classList.remove('d-none');
            }
        });

        // Project search functionality
        const projectSearch = document.getElementById('project_search');
        const projectSearchResults = document.getElementById('project_search_results');
        const projectSelect = document.getElementById('project_id');
        const selectedProject = document.getElementById('selected_project');
        const selectedProjectName = document.getElementById('selected_project_name');
        const clearProject = document.getElementById('clear_project');

        // Create an array of project objects from the select options
        const projects = Array.from(projectSelect.options).slice(1).map(option => {
            return {
                id: option.value,
                name: option.textContent
            };
        });

        // Function to filter projects based on search term
        function filterProjects(searchTerm) {
            searchTerm = searchTerm.toLowerCase();
            return projects.filter(project =>
                project.name.toLowerCase().includes(searchTerm)
            );
        }

        // Function to display search results
        function displaySearchResults(results) {
            projectSearchResults.innerHTML = '';

            if (results.length === 0) {
                projectSearchResults.innerHTML = '<div class="p-2 text-muted">لا توجد نتائج</div>';
                return;
            }

            results.forEach(project => {
                const resultItem = document.createElement('div');
                resultItem.className = 'p-2 border-bottom project-result';
                resultItem.textContent = project.name;
                resultItem.dataset.id = project.id;
                resultItem.dataset.name = project.name;
                resultItem.style.cursor = 'pointer';

                resultItem.addEventListener('click', function() {
                    // Set the selected project in the hidden select
                    projectSelect.value = this.dataset.id;

                    // Display the selected project
                    selectedProjectName.textContent = this.dataset.name;
                    selectedProject.classList.remove('d-none');

                    // Clear the search input and hide results
                    projectSearch.value = '';
                    projectSearchResults.classList.add('d-none');
                });

                projectSearchResults.appendChild(resultItem);
            });
        }

        // Event listener for project search input
        projectSearch.addEventListener('input', function() {
            const searchTerm = this.value.trim();

            if (searchTerm.length > 0) {
                const results = filterProjects(searchTerm);
                displaySearchResults(results);
                projectSearchResults.classList.remove('d-none');
            } else {
                projectSearchResults.classList.add('d-none');
            }
        });

        // Event listener for clear project button
        clearProject.addEventListener('click', function() {
            projectSelect.value = '';
            selectedProject.classList.add('d-none');
            projectSearch.value = '';
        });

        // Close search results when clicking outside
        document.addEventListener('click', function(event) {
            if (!projectSearch.contains(event.target) && !projectSearchResults.contains(event.target)) {
                projectSearchResults.classList.add('d-none');
            }
        });

        // Focus event to show all projects when focusing on empty search
        projectSearch.addEventListener('focus', function() {
            if (this.value.trim() === '') {
                displaySearchResults(projects);
                projectSearchResults.classList.remove('d-none');
            }
        });

        // Live calculation preview
        function updateCalculations() {
            let subtotal = 0;

            // Calculate subtotal from all items
            document.querySelectorAll('.invoice-item').forEach(item => {
                const quantity = parseFloat(item.querySelector('.item-quantity').value) || 0;
                const price = parseFloat(item.querySelector('.item-price').value) || 0;
                subtotal += quantity * price;
            });

            // Get fee and tax values
            const transferFeeType = document.getElementById('transfer_fee_type').value;
            const transferFeeValue = parseFloat(document.getElementById('transfer_fee_value').value) || 0;
            const taxType = document.getElementById('tax_type').value;
            const taxValue = parseFloat(document.getElementById('tax_value').value) || 0;
            const discountType = document.getElementById('discount_type').value;
            const discountValue = parseFloat(document.getElementById('discount_value').value) || 0;

            // Calculate transfer fee
            let transferFee = 0;
            if (transferFeeType === 'percentage') {
                transferFee = subtotal * (transferFeeValue / 100);
            } else {
                transferFee = transferFeeValue;
            }

            // Calculate tax
            let tax = 0;
            if (taxType === 'percentage') {
                tax = subtotal * (taxValue / 100);
            } else {
                tax = taxValue;
            }

            // Calculate discount
            let discount = 0;
            if (discountType === 'percentage') {
                discount = subtotal * (discountValue / 100);
            } else {
                discount = discountValue;
            }

            // Calculate total
            const total = subtotal + transferFee + tax - discount;

            // Update preview
            document.getElementById('preview-subtotal').textContent = '$' + subtotal.toFixed(2);
            document.getElementById('preview-transfer-fee').textContent = '$' + transferFee.toFixed(2);
            document.getElementById('preview-tax').textContent = '$' + tax.toFixed(2);
            document.getElementById('preview-discount').textContent = '$' + discount.toFixed(2);
            document.getElementById('preview-total').textContent = '$' + total.toFixed(2);
        }

        // Add event listeners for live calculation
        document.addEventListener('input', function(e) {
            if (e.target.matches('.item-quantity, .item-price, #transfer_fee_value, #tax_value, #discount_value')) {
                updateCalculations();
            }
        });

        document.addEventListener('change', function(e) {
            if (e.target.matches('#transfer_fee_type, #tax_type, #discount_type')) {
                updateCalculations();
            }
        });

        // Initial calculation
        updateCalculations();
    });
</script>

<!-- Live Preview Card -->
<div class="position-fixed" style="top: 100px; left: 20px; z-index: 1000; width: 300px;">
    <div class="card shadow border-success">
        <div class="card-header bg-success text-white">
            <h6 class="mb-0">
                <i class="fas fa-calculator me-2"></i>معاينة مباشرة
            </h6>
        </div>
        <div class="card-body">
            <table class="table table-sm table-borderless">
                <tr>
                    <td>المجموع الفرعي:</td>
                    <td class="text-end"><strong id="preview-subtotal">$0.00</strong></td>
                </tr>
                <tr>
                    <td>عمولة التحويل:</td>
                    <td class="text-end"><strong id="preview-transfer-fee">$0.00</strong></td>
                </tr>
                <tr>
                    <td>الضريبة:</td>
                    <td class="text-end"><strong id="preview-tax">$0.00</strong></td>
                </tr>
                <tr>
                    <td>الخصم:</td>
                    <td class="text-end"><strong id="preview-discount">$0.00</strong></td>
                </tr>
                <tr class="border-top">
                    <td><strong>المجموع الكلي:</strong></td>
                    <td class="text-end"><strong id="preview-total" class="text-success">$0.00</strong></td>
                </tr>
            </table>
        </div>
    </div>
</div>
{% endblock %}
