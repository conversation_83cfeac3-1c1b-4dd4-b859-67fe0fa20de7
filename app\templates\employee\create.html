{% extends 'base.html' %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>إضافة موظف جديد</h1>
    <a href="{{ url_for('employee.index') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-right me-1"></i>العودة للقائمة
    </a>
</div>

<div class="card shadow">
    <div class="card-header bg-light">
        <h5 class="mb-0">بيانات الموظف الجديد</h5>
    </div>
    <div class="card-body">
        <form method="POST" action="{{ url_for('employee.create') }}" enctype="multipart/form-data">
            <div class="row">
                <!-- Basic Information -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header bg-light">
                            <h6 class="mb-0">المعلومات الأساسية</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="username" class="form-label">اسم المستخدم <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="username" name="username" required>
                            </div>
                            <div class="mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>
                            <div class="mb-3">
                                <label for="password" class="form-label">كلمة المرور <span class="text-danger">*</span></label>
                                <input type="password" class="form-control" id="password" name="password" required>
                            </div>
                            <!-- Full name fields - only for admin and manager -->
                            {% if current_user.has_role('admin') or current_user.has_role('manager') %}
                            <div class="mb-3">
                                <label for="full_name_ar" class="form-label">الاسم رباعي كامل بالعربي</label>
                                <input type="text" class="form-control" id="full_name_ar" name="full_name_ar">
                                <small class="text-muted">أدخل الاسم الرباعي الكامل بالعربية</small>
                            </div>
                            <div class="mb-3">
                                <label for="full_name_en" class="form-label">الاسم رباعي كامل بالإنجليزية</label>
                                <input type="text" class="form-control" id="full_name_en" name="full_name_en">
                                <small class="text-muted">أدخل الاسم الرباعي الكامل بالإنجليزية</small>
                            </div>
                            {% endif %}

                            <!-- First and last name fields - for admin, manager, and department_head -->
                            {% if current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('department_head') %}
                            <div class="mb-3">
                                <label for="first_name" class="form-label">الاسم الأول</label>
                                <input type="text" class="form-control" id="first_name" name="first_name">
                            </div>
                            <div class="mb-3">
                                <label for="last_name" class="form-label">الاسم الأخير</label>
                                <input type="text" class="form-control" id="last_name" name="last_name">
                            </div>
                            <div class="mb-3">
                                <label for="first_name_en" class="form-label">الاسم الأول (بالإنجليزية)</label>
                                <input type="text" class="form-control" id="first_name_en" name="first_name_en">
                            </div>
                            <div class="mb-3">
                                <label for="last_name_en" class="form-label">الاسم الأخير (بالإنجليزية)</label>
                                <input type="text" class="form-control" id="last_name_en" name="last_name_en">
                            </div>
                            {% endif %}
                            <div class="mb-3">
                                <label for="birth_date" class="form-label">تاريخ الميلاد</label>
                                <input type="date" class="form-control" id="birth_date" name="birth_date">
                            </div>
                            <div class="mb-3">
                                <label for="nationality" class="form-label">الجنسية</label>
                                <input type="text" class="form-control" id="nationality" name="nationality">
                            </div>
                            <div class="mb-3">
                                <label for="phone" class="form-label">رقم الهاتف</label>
                                <input type="text" class="form-control" id="phone" name="phone">
                            </div>

                            <div class="mb-3">
                                <label for="profile_image" class="form-label">الصورة الشخصية</label>
                                <input type="file" class="form-control" id="profile_image" name="profile_image" accept="image/*">
                                <small class="text-muted">اختر صورة شخصية للموظف (اختياري)</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Department and Roles -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header bg-light">
                            <h6 class="mb-0">القسم والصلاحيات</h6>
                        </div>
                        <div class="card-body">
                            <!-- الأقسام المتعددة -->
                            <div class="mb-3">
                                <label class="form-label">الأقسام</label>
                                <div class="border rounded p-3">
                                    {% for department in departments %}
                                    <div class="form-check mb-2">
                                        <input class="form-check-input department-checkbox" type="checkbox"
                                               id="dept_{{ department.id }}" name="department_ids" value="{{ department.id }}">
                                        <label class="form-check-label" for="dept_{{ department.id }}">
                                            {{ department.name }}
                                        </label>
                                        <div class="form-check form-switch ms-4 mt-1" style="display: none;">
                                            <input class="form-check-input primary-dept-radio" type="radio"
                                                   name="primary_department_id" value="{{ department.id }}"
                                                   id="primary_{{ department.id }}">
                                            <label class="form-check-label small text-muted" for="primary_{{ department.id }}">
                                                القسم الرئيسي
                                            </label>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                                <small class="text-muted">يمكن اختيار أكثر من قسم، مع تحديد القسم الرئيسي</small>
                            </div>



                            <div class="mb-3">
                                <label class="form-label">الصلاحيات</label>
                                <div class="card">
                                    <div class="card-body">
                                        {% for role in roles %}
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="role_{{ role.id }}" name="roles" value="{{ role.id }}">
                                            <label class="form-check-label" for="role_{{ role.id }}">
                                                {{ role.name }}
                                                {% if role.description %}
                                                <small class="text-muted d-block">{{ role.description }}</small>
                                                {% endif %}
                                            </label>
                                        </div>
                                        {% endfor %}

                                        {% if current_user.has_role('manager') %}
                                        <div class="alert alert-info mt-3">
                                            <small>ملاحظة: المدير يمكنه فقط تعيين صلاحيات رئيس قسم وما دونها</small>
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="bank_account" class="form-label">حساب التحصيل المالي</label>
                                <textarea class="form-control" id="bank_account" name="bank_account" rows="3"></textarea>
                                <small class="text-muted">أدخل معلومات الحساب البنكي للموظف</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <a href="{{ url_for('employee.index') }}" class="btn btn-secondary me-md-2">إلغاء</a>
                <button type="submit" class="btn btn-primary">إضافة موظف</button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // إدارة الأقسام المتعددة
    const departmentCheckboxes = document.querySelectorAll('.department-checkbox');
    const primaryRadios = document.querySelectorAll('.primary-dept-radio');

    // عند تغيير حالة checkbox للقسم
    departmentCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const deptId = this.value;
            const primaryRadioContainer = this.parentElement.querySelector('.form-check.form-switch');
            const primaryRadio = document.getElementById(`primary_${deptId}`);

            if (this.checked) {
                // إظهار خيار القسم الرئيسي
                primaryRadioContainer.style.display = 'block';

                // إذا كان هذا أول قسم يتم اختياره، اجعله رئيسياً
                const checkedDepts = document.querySelectorAll('.department-checkbox:checked');
                if (checkedDepts.length === 1) {
                    primaryRadio.checked = true;
                }
            } else {
                // إخفاء خيار القسم الرئيسي
                primaryRadioContainer.style.display = 'none';
                primaryRadio.checked = false;

                // إذا كان هذا القسم الرئيسي، اختر قسم آخر كرئيسي
                const checkedDepts = document.querySelectorAll('.department-checkbox:checked');
                if (checkedDepts.length > 0 && !document.querySelector('.primary-dept-radio:checked')) {
                    checkedDepts[0].parentElement.querySelector('.primary-dept-radio').checked = true;
                }
            }
        });
    });

    // التأكد من وجود قسم رئيسي واحد فقط
    primaryRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.checked) {
                // إلغاء تحديد الأقسام الرئيسية الأخرى
                primaryRadios.forEach(otherRadio => {
                    if (otherRadio !== this) {
                        otherRadio.checked = false;
                    }
                });
            }
        });
    });

    // التحقق من صحة النموذج قبل الإرسال
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        const checkedDepts = document.querySelectorAll('.department-checkbox:checked');

        if (checkedDepts.length === 0) {
            e.preventDefault();
            alert('يجب اختيار قسم واحد على الأقل');
            return false;
        }

        const checkedPrimary = document.querySelector('.primary-dept-radio:checked');
        if (checkedDepts.length > 1 && !checkedPrimary) {
            e.preventDefault();
            alert('يجب تحديد القسم الرئيسي عند اختيار أكثر من قسم');
            return false;
        }
    });
});
</script>
{% endblock %}
