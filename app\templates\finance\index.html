{% extends 'base.html' %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1>لوحة المالية</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('dashboard.index') }}">الرئيسية</a></li>
                    <li class="breadcrumb-item active" aria-current="page">لوحة المالية</li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- النظام المالي الجديد -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>النظام المالي المتقدم
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('finance.accounts') }}" class="text-decoration-none">
                                <div class="card border-left-info h-100 py-3 hover-shadow">
                                    <div class="card-body text-center">
                                        <i class="fas fa-sitemap fa-3x text-info mb-3"></i>
                                        <h6 class="font-weight-bold text-info">شجرة الحسابات</h6>
                                        <p class="text-muted small">إدارة الحسابات الرئيسية والفرعية</p>
                                    </div>
                                </div>
                            </a>
                        </div>

                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('finance.salaries') }}" class="text-decoration-none">
                                <div class="card border-left-success h-100 py-3 hover-shadow">
                                    <div class="card-body text-center">
                                        <i class="fas fa-money-bill-wave fa-3x text-success mb-3"></i>
                                        <h6 class="font-weight-bold text-success">رواتب الموظفين</h6>
                                        <p class="text-muted small">إدارة رواتب الموظفين والحوالات</p>
                                    </div>
                                </div>
                            </a>
                        </div>

                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('finance.financial_transactions') }}" class="text-decoration-none">
                                <div class="card border-left-warning h-100 py-3 hover-shadow">
                                    <div class="card-body text-center">
                                        <i class="fas fa-exchange-alt fa-3x text-warning mb-3"></i>
                                        <h6 class="font-weight-bold text-warning">المعاملات المالية</h6>
                                        <p class="text-muted small">إدارة المعاملات المالية المتقدمة</p>
                                    </div>
                                </div>
                            </a>
                        </div>

                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('finance.invoices') }}" class="text-decoration-none">
                                <div class="card border-left-danger h-100 py-3 hover-shadow">
                                    <div class="card-body text-center">
                                        <i class="fas fa-file-invoice-dollar fa-3x text-danger mb-3"></i>
                                        <h6 class="font-weight-bold text-danger">الفواتير المحدثة</h6>
                                        <p class="text-muted small">إدارة الفواتير مع النظام الجديد</p>
                                    </div>
                                </div>
                            </a>
                        </div>


                    </div>


                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات النظام المالي الجديد -->
    <div class="row mb-4">
        <!-- إحصائيات الحسابات -->
        <div class="col-md-3 mb-3">
            <div class="card shadow border-left-info h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                إجمالي الحسابات</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_accounts }}</div>
                            <div class="text-xs text-muted">نشط: {{ active_accounts }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-sitemap fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائيات الرواتب -->
        <div class="col-md-3 mb-3">
            <div class="card shadow border-left-success h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                رواتب الموظفين</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_salaries }}</div>
                            <div class="text-xs text-muted">معلق: {{ pending_salaries }} | مدفوع: {{ paid_salaries }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-money-bill-wave fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائيات المعاملات المالية -->
        <div class="col-md-3 mb-3">
            <div class="card shadow border-left-warning h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                المعاملات المالية</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_financial_transactions }}</div>
                            <div class="text-xs text-muted">معلق: {{ pending_financial_transactions }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exchange-alt fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائيات الفواتير -->
        <div class="col-md-3 mb-3">
            <div class="card shadow border-left-danger h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                الفواتير</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_invoices }}</div>
                            <div class="text-xs text-muted">معلق: {{ pending_invoices }} | مدفوع: {{ paid_invoices }} | متأخر: {{ overdue_invoices }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-file-invoice-dollar fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الأنشطة الحديثة -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card shadow mb-4">
                <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">أحدث الرواتب</h6>
                    <a href="{{ url_for('finance.salaries') }}" class="btn btn-sm btn-light">عرض الكل</a>
                </div>
                <div class="card-body">
                    {% if recent_salaries %}
                    {% for salary in recent_salaries %}
                    <div class="d-flex justify-content-between align-items-center mb-2 pb-2 border-bottom">
                        <div>
                            <small class="text-muted">{{ salary.employee_name }}</small><br>
                            <strong>${{ "{:,.2f}".format(salary.total_amount) }}</strong>
                            {% if salary.commission_amount > 0 %}
                            <br><small class="text-muted">صافي: ${{ "{:,.2f}".format(salary.net_amount) }}</small>
                            {% endif %}
                        </div>
                        <div class="text-end">
                            {% if salary.status == 'draft' %}
                            <span class="badge bg-secondary">مسودة</span>
                            {% elif salary.status == 'pending' %}
                            <span class="badge bg-warning">معلق</span>
                            {% elif salary.status == 'paid' %}
                            <span class="badge bg-success">مدفوع</span>
                            {% elif salary.status == 'cancelled' %}
                            <span class="badge bg-danger">ملغي</span>
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                    {% else %}
                    <p class="text-muted text-center">لا توجد رواتب حديثة</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card shadow mb-4">
                <div class="card-header bg-warning text-dark d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">أحدث المعاملات المالية</h6>
                    <a href="{{ url_for('finance.financial_transactions') }}" class="btn btn-sm btn-dark">عرض الكل</a>
                </div>
                <div class="card-body">
                    {% if recent_financial_transactions %}
                    {% for transaction in recent_financial_transactions %}
                    <div class="d-flex justify-content-between align-items-center mb-2 pb-2 border-bottom">
                        <div>
                            <small class="text-muted">{{ transaction.transaction_number }}</small><br>
                            <strong>{{ transaction.transaction_name }}</strong>
                        </div>
                        <div class="text-end">
                            {% if transaction.status == 'draft' %}
                            <span class="badge bg-secondary">مسودة</span>
                            {% elif transaction.status == 'pending' %}
                            <span class="badge bg-warning">معلق</span>
                            {% elif transaction.status == 'paid' %}
                            <span class="badge bg-success">مدفوع</span>
                            {% elif transaction.status == 'cancelled' %}
                            <span class="badge bg-danger">ملغي</span>
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                    {% else %}
                    <p class="text-muted text-center">لا توجد معاملات حديثة</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card shadow mb-4">
                <div class="card-header bg-danger text-white d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">أحدث الفواتير</h6>
                    <a href="{{ url_for('finance.invoices') }}" class="btn btn-sm btn-light">عرض الكل</a>
                </div>
                <div class="card-body">
                    {% if recent_invoices %}
                    {% for invoice in recent_invoices %}
                    <div class="d-flex justify-content-between align-items-center mb-2 pb-2 border-bottom">
                        <div>
                            <small class="text-muted">{{ invoice.invoice_number }}</small><br>
                            <strong>${{ "{:,.2f}".format(invoice.total_amount) }}</strong>
                        </div>
                        <div class="text-end">
                            {% if invoice.status == 'unpaid' %}
                            <span class="badge bg-warning">غير مدفوع</span>
                            {% elif invoice.status == 'paid' %}
                            <span class="badge bg-success">مدفوع</span>
                            {% elif invoice.status == 'overdue' %}
                            <span class="badge bg-danger">متأخر</span>
                            {% elif invoice.status == 'cancelled' %}
                            <span class="badge bg-secondary">ملغي</span>
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                    {% else %}
                    <p class="text-muted text-center">لا توجد فواتير حديثة</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>



{% block styles %}
<style>
.hover-shadow {
    transition: all 0.3s ease;
}

.hover-shadow:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.border-left-danger {
    border-left: 0.25rem solid #e74a3b !important;
}

.border-left-purple {
    border-left: 0.25rem solid #6f42c1 !important;
}

.text-info {
    color: #36b9cc !important;
}

.text-success {
    color: #1cc88a !important;
}

.text-purple {
    color: #6f42c1 !important;
}

.text-warning {
    color: #f6c23e !important;
}

.text-danger {
    color: #e74a3b !important;
}
</style>
{% endblock %}
{% endblock %}
