{% extends 'base.html' %}

{% block styles %}
<style>
.member-status-icon {
    font-size: 0.9em;
}

.member-info {
    flex-grow: 1;
}

.manager-badge {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

.on-leave-indicator {
    color: #dc3545 !important;
    animation: blink 1.5s infinite;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.5; }
}
    .chat-container {
        height: 500px;
        display: flex;
        flex-direction: column;
    }

    .chat-messages {
        flex: 1;
        overflow-y: auto;
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 0.25rem;
        margin-bottom: 15px;
    }

    .message {
        margin-bottom: 15px;
        padding: 10px 15px;
        border-radius: 10px;
        max-width: 70%;
        position: relative;
    }

    .message-sent {
        background-color: #dcf8c6;
        margin-left: auto;
    }

    .message-received {
        background-color: #ffffff;
        margin-right: auto;
    }

    .message-info {
        font-size: 0.75rem;
        color: #6c757d;
        margin-top: 5px;
    }

    .message-content {
        word-wrap: break-word;
    }

    .chat-input {
        display: flex;
    }

    .chat-input textarea {
        flex: 1;
        border-radius: 20px;
        padding: 10px 15px;
        resize: none;
    }

    .chat-input button {
        margin-right: 10px;
        margin-left: 10px;
        border-radius: 50%;
        width: 50px;
        height: 50px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1>محادثة المشروع</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('dashboard.index') }}">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('project.index') }}">المشاريع</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('project.view', id=project.id) }}">{{ project.name }}</a></li>
                    <li class="breadcrumb-item active" aria-current="page">المحادثة</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card shadow">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-comments me-2"></i>محادثة المشروع: {{ project.name }}
                    </h5>
                    <a href="{{ url_for('project.view', id=project.id) }}" class="btn btn-sm btn-light">
                        <i class="fas fa-arrow-left me-1"></i>العودة للمشروع
                    </a>
                </div>
                <div class="card-body">
                    <div class="chat-container">
                        <div class="chat-messages" id="chat-messages">
                            {% for message in messages %}
                                <div id="message-{{ message.id }}" class="message {% if message.user_id == current_user.id %}message-sent{% else %}message-received{% endif %}">
                                    <div class="message-content">{{ message.content }}</div>
                                    <div class="message-info">
                                        <span class="message-sender">{{ message.user.get_full_name() }}</span> -
                                        <span class="message-time">{{ message.timestamp.strftime('%Y-%m-%d %H:%M') }}</span>
                                    </div>
                                </div>
                            {% else %}
                                <div class="text-center text-muted">
                                    <i class="fas fa-comments fa-3x mb-3"></i>
                                    <p>لا توجد رسائل بعد. كن أول من يبدأ المحادثة!</p>
                                </div>
                            {% endfor %}
                        </div>
                        <div class="chat-input">
                            <textarea class="form-control" id="message-input" placeholder="اكتب رسالتك هنا..." rows="2"></textarea>
                            <button class="btn btn-primary" id="send-button">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card shadow mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">أعضاء المشروع</h5>
                </div>
                <div class="card-body">
                    <ul class="list-group">
                        {# المدير الرئيسي أولاً #}
                        {% if project.manager and project.manager in project.members %}
                        {% set member = project.manager %}
                        <li class="list-group-item d-flex justify-content-between align-items-center border-warning">
                            <div class="member-info">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-crown text-warning me-2 member-status-icon" data-bs-toggle="tooltip" title="مدير المشروع الرئيسي"></i>

                                    {% if member.is_currently_on_leave %}
                                    <i class="fas fa-calendar-times on-leave-indicator me-2 member-status-icon" data-bs-toggle="tooltip" title="في إجازة"></i>
                                    {% endif %}

                                    <div>
                                        <strong>{{ member.get_full_name() }}</strong>
                                        <small class="text-muted d-block">
                                            <i class="fas fa-star text-warning me-1"></i>مدير المشروع الرئيسي
                                            {% if member.is_currently_on_leave %}
                                            <br><i class="fas fa-exclamation-triangle text-danger me-1"></i><span class="text-danger">في إجازة حالياً</span>
                                            {% endif %}
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <span class="badge bg-warning text-dark rounded-pill manager-badge" data-bs-toggle="tooltip" title="مدير رئيسي">
                                <i class="fas fa-crown me-1"></i>رئيسي
                            </span>
                        </li>
                        {% endif %}

                        {# المدراء المشاركين ثانياً #}
                        {% for member in project.managers %}
                        {% if member.id != project.manager_id %}
                        <li class="list-group-item d-flex justify-content-between align-items-center border-info">
                            <div class="member-info">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-crown text-info me-2 member-status-icon" data-bs-toggle="tooltip" title="مدير مشارك"></i>

                                    {% if member.is_currently_on_leave %}
                                    <i class="fas fa-calendar-times on-leave-indicator me-2 member-status-icon" data-bs-toggle="tooltip" title="في إجازة"></i>
                                    {% endif %}

                                    <div>
                                        <strong>{{ member.get_full_name() }}</strong>
                                        <small class="text-muted d-block">
                                            <i class="fas fa-star text-info me-1"></i>مدير مشارك
                                            {% if member.is_currently_on_leave %}
                                            <br><i class="fas fa-exclamation-triangle text-danger me-1"></i><span class="text-danger">في إجازة حالياً</span>
                                            {% endif %}
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <span class="badge bg-info rounded-pill manager-badge" data-bs-toggle="tooltip" title="مدير مشارك">
                                <i class="fas fa-crown me-1"></i>مشارك
                            </span>
                        </li>
                        {% endif %}
                        {% endfor %}

                        {# الأعضاء العاديين أخيراً #}
                        {% for member in project.members %}
                        {% if member.id != project.manager_id and member not in project.managers %}
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <div class="member-info">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-user text-secondary me-2 member-status-icon"></i>

                                    {% if member.is_currently_on_leave %}
                                    <i class="fas fa-calendar-times on-leave-indicator me-2 member-status-icon" data-bs-toggle="tooltip" title="في إجازة"></i>
                                    {% endif %}

                                    <div>
                                        <strong>{{ member.get_full_name() }}</strong>
                                        <small class="text-muted d-block">
                                            <i class="fas fa-envelope text-muted me-1"></i>{{ member.email }}
                                            {% if member.is_currently_on_leave %}
                                            <br><i class="fas fa-exclamation-triangle text-danger me-1"></i><span class="text-danger">في إجازة حالياً</span>
                                            {% endif %}
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <span class="badge bg-light text-dark rounded-pill">
                                <i class="fas fa-user me-1"></i>عضو
                            </span>
                        </li>
                        {% endif %}
                        {% endfor %}
                    </ul>
                </div>
            </div>

            <div class="card shadow">
                <div class="card-header bg-light">
                    <h5 class="mb-0">معلومات المشروع</h5>
                </div>
                <div class="card-body">
                    <p><strong>الحالة:</strong>
                        {% if project.status == 'pending' %}
                        <span class="badge bg-secondary">قيد الانتظار</span>
                        {% elif project.status == 'in_progress' %}
                        <span class="badge bg-primary">قيد التنفيذ</span>
                        {% elif project.status == 'completed' %}
                        <span class="badge bg-success">مكتمل</span>
                        {% elif project.status == 'cancelled' %}
                        <span class="badge bg-danger">ملغي</span>
                        {% endif %}
                    </p>
                    <p><strong>الأولوية:</strong>
                        {% if project.priority == 'low' %}
                        <span class="badge bg-info">منخفضة</span>
                        {% elif project.priority == 'medium' %}
                        <span class="badge bg-warning">متوسطة</span>
                        {% elif project.priority == 'high' %}
                        <span class="badge bg-danger">عالية</span>
                        {% endif %}
                    </p>
                    <p><strong>تاريخ البدء:</strong> {{ project.start_date.strftime('%Y-%m-%d') }}</p>
                    <p><strong>تاريخ الانتهاء:</strong> {{ project.end_date.strftime('%Y-%m-%d') if project.end_date else 'غير محدد' }}</p>
                    <p><strong>نسبة الإنجاز:</strong> {{ project.get_completion_percentage() }}%</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Scroll to bottom of chat
        function scrollToBottom() {
            const chatMessages = document.getElementById('chat-messages');
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // Initial scroll to bottom
        scrollToBottom();

        // Send message function
        function sendMessage() {
            const messageInput = $('#message-input');
            const content = messageInput.val().trim();

            if (content === '') {
                return;
            }

            $.ajax({
                url: '{{ url_for("chat.send_message", project_id=project.id) }}',
                method: 'POST',
                data: { content: content },
                success: function(response) {
                    if (response.status === 'success') {
                        // Add message to chat
                        const message = response.message;
                        const messageHtml = `
                            <div id="message-${message.id}" class="message message-sent">
                                <div class="message-content">${message.content}</div>
                                <div class="message-info">
                                    <span class="message-sender">${message.user_name}</span> -
                                    <span class="message-time">${message.timestamp}</span>
                                </div>
                            </div>
                        `;
                        $('#chat-messages').append(messageHtml);

                        // Clear input
                        messageInput.val('');

                        // Scroll to bottom
                        scrollToBottom();
                    }
                },
                error: function(xhr) {
                    console.error('Error sending message:', xhr.responseText);
                    alert('حدث خطأ أثناء إرسال الرسالة. يرجى المحاولة مرة أخرى.');
                }
            });
        }

        // Send message on button click
        $('#send-button').click(function() {
            sendMessage();
        });

        // Send message on Enter key (but allow Shift+Enter for new line)
        $('#message-input').keydown(function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        // Poll for new messages every 5 seconds
        let lastTimestamp = '{{ messages[-1].timestamp.strftime("%Y-%m-%d %H:%M:%S") if messages else "" }}';
        let lastMessageId = {{ messages[-1].id if messages else 0 }};

        function pollMessages() {
            $.ajax({
                url: '{{ url_for("chat.get_messages", project_id=project.id) }}',
                method: 'GET',
                data: {
                    last_timestamp: lastTimestamp,
                    last_message_id: lastMessageId
                },
                success: function(response) {
                    if (response.status === 'success' && response.messages.length > 0) {
                        // Update last timestamp and message ID
                        lastTimestamp = response.messages[response.messages.length - 1].timestamp;
                        lastMessageId = response.messages[response.messages.length - 1].id;

                        // Add new messages to chat
                        response.messages.forEach(function(message) {
                            // Check if message already exists in the chat
                            if ($(`#message-${message.id}`).length === 0) {
                                const messageClass = message.is_current_user ? 'message-sent' : 'message-received';
                                const messageHtml = `
                                    <div id="message-${message.id}" class="message ${messageClass}">
                                        <div class="message-content">${message.content}</div>
                                        <div class="message-info">
                                            <span class="message-sender">${message.user_name}</span> -
                                            <span class="message-time">${message.timestamp}</span>
                                        </div>
                                    </div>
                                `;
                                $('#chat-messages').append(messageHtml);
                            }
                        });

                        // Scroll to bottom if user is already at bottom
                        const chatMessages = document.getElementById('chat-messages');
                        const isAtBottom = chatMessages.scrollHeight - chatMessages.clientHeight <= chatMessages.scrollTop + 100;

                        if (isAtBottom) {
                            scrollToBottom();
                        }
                    }
                },
                error: function(xhr) {
                    console.error('Error polling messages:', xhr.responseText);
                }
            });
        }

        // Start polling
        setInterval(pollMessages, 5000);

        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    });
</script>
{% endblock %}
