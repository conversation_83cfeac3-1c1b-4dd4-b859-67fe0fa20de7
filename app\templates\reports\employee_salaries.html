{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
        <a href="{{ url_for('report.index') }}" class="d-none d-sm-inline-block btn btn-sm btn-primary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> العودة للتقارير
        </a>
    </div>

    <!-- تقرير رواتب الموظفين -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">خيارات التقرير</h6>
        </div>
        <div class="card-body">
            <form action="{{ url_for('report.export') }}" method="POST">
                <input type="hidden" name="report_type" value="employee_salaries">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label for="employee_id" class="form-label">الموظف</label>
                        <select class="form-select" id="employee_id" name="employee_id">
                            <option value="all" selected>جميع الموظفين</option>
                            {% for employee in employees %}
                            <option value="{{ employee.id }}">{{ employee.get_full_name() }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="status" class="form-label">حالة الراتب</label>
                        <select class="form-select" id="status" name="status">
                            <option value="all" selected>جميع الحالات</option>
                            <option value="pending">قيد الانتظار</option>
                            <option value="paid">مدفوع</option>
                            <option value="cancelled">ملغي</option>
                        </select>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="date_from" class="form-label">من تاريخ</label>
                        <input type="date" class="form-control" id="date_from" name="date_from">
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="date_to" class="form-label">إلى تاريخ</label>
                        <input type="date" class="form-control" id="date_to" name="date_to">
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="export_format" class="form-label">تنسيق التصدير</label>
                        <select class="form-select" id="export_format" name="export_format">
                            <option value="pdf" selected>PDF</option>
                            <option value="excel">Excel</option>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-file-export me-1"></i>تصدير التقرير
                            </button>
                            <a href="{{ url_for('report.employee_salaries') }}" class="btn btn-secondary">
                                <i class="fas fa-redo me-1"></i>إعادة تعيين
                            </a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- معاينة البيانات -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">معاينة البيانات</h6>
            <div class="d-flex align-items-center">
                <select class="form-select form-select-sm me-2" onchange="changePerPage(this.value)" style="width: auto;">
                    <option value="25" {% if per_page == 25 %}selected{% endif %}>25 راتب</option>
                    <option value="50" {% if per_page == 50 %}selected{% endif %}>50 راتب</option>
                    <option value="100" {% if per_page == 100 %}selected{% endif %}>100 راتب</option>
                </select>
            </div>
        </div>
        <div class="card-body">
            {% if salaries %}
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>#</th>
                            <th>الموظف</th>
                            <th>الشهر</th>
                            <th>السنة</th>
                            <th>الراتب الأساسي</th>
                            <th>البدلات</th>
                            <th>الخصومات</th>
                            <th>صافي الراتب</th>
                            <th>الحالة</th>
                            <th>تاريخ الدفع</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% if salaries.items %}
                            {% for salary in salaries.items %}
                            <tr>
                                <td>{{ loop.index + ((salaries.page - 1) * salaries.per_page) }}</td>
                                <td>{{ salary.employee.get_full_name() }}</td>
                                <td>{{ salary.month }}</td>
                                <td>{{ salary.year }}</td>
                                <td>${{ "%.2f"|format(salary.base_salary) }}</td>
                                <td>${{ "%.2f"|format(salary.allowances or 0) }}</td>
                                <td>${{ "%.2f"|format(salary.deductions or 0) }}</td>
                                <td>${{ "%.2f"|format(salary.net_salary) }}</td>
                                <td>
                                    {% if salary.status == 'pending' %}
                                    <span class="badge bg-warning">قيد الانتظار</span>
                                    {% elif salary.status == 'paid' %}
                                    <span class="badge bg-success">مدفوع</span>
                                    {% elif salary.status == 'cancelled' %}
                                    <span class="badge bg-danger">ملغي</span>
                                    {% endif %}
                                </td>
                                <td>{{ salary.payment_date.strftime('%Y-%m-%d') if salary.payment_date else 'غير محدد' }}</td>
                            </tr>
                            {% endfor %}
                        {% else %}
                            {% for salary in salaries %}
                            <tr>
                                <td>{{ loop.index }}</td>
                                <td>{{ salary.employee.get_full_name() }}</td>
                                <td>{{ salary.month }}</td>
                                <td>{{ salary.year }}</td>
                                <td>${{ "%.2f"|format(salary.base_salary) }}</td>
                                <td>${{ "%.2f"|format(salary.allowances or 0) }}</td>
                                <td>${{ "%.2f"|format(salary.deductions or 0) }}</td>
                                <td>${{ "%.2f"|format(salary.net_salary) }}</td>
                                <td>
                                    {% if salary.status == 'pending' %}
                                    <span class="badge bg-warning">قيد الانتظار</span>
                                    {% elif salary.status == 'paid' %}
                                    <span class="badge bg-success">مدفوع</span>
                                    {% elif salary.status == 'cancelled' %}
                                    <span class="badge bg-danger">ملغي</span>
                                    {% endif %}
                                </td>
                                <td>{{ salary.payment_date.strftime('%Y-%m-%d') if salary.payment_date else 'غير محدد' }}</td>
                            </tr>
                            {% endfor %}
                        {% endif %}
                    </tbody>
                </table>
            </div>
            
            <!-- تقسيم الصفحات -->
            {% if salaries.items and salaries.pages > 1 %}
            <div class="d-flex justify-content-between align-items-center mt-3">
                <div>
                    <small class="text-muted">
                        عرض {{ salaries.per_page * (salaries.page - 1) + 1 }} إلى 
                        {{ salaries.per_page * (salaries.page - 1) + salaries.items|length }} 
                        من {{ salaries.total }} راتب
                    </small>
                </div>
                <nav aria-label="تقسيم صفحات الرواتب">
                    <ul class="pagination pagination-sm mb-0">
                        {% if salaries.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('report.employee_salaries', page=salaries.prev_num, per_page=per_page) }}">السابق</a>
                        </li>
                        {% endif %}
                        
                        {% for page_num in salaries.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != salaries.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('report.employee_salaries', page=page_num, per_page=per_page) }}">{{ page_num }}</a>
                                </li>
                                {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                                {% endif %}
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">…</span>
                            </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if salaries.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('report.employee_salaries', page=salaries.next_num, per_page=per_page) }}">التالي</a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
            {% endif %}
            {% else %}
            <div class="text-center py-4">
                <p class="text-muted mb-0">لا توجد رواتب لعرضها</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function changePerPage(perPage) {
    const url = new URL(window.location);
    url.searchParams.set('per_page', perPage);
    url.searchParams.set('page', 1); // Reset to first page
    window.location.href = url.toString();
}

$(document).ready(function() {
    // Initialize select2
    $('#employee_id, #status, #export_format').select2({
        theme: 'bootstrap-5',
        width: '100%',
        dir: 'rtl'
    });
});
</script>
{% endblock %}
