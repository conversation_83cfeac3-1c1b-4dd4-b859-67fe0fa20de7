{% extends 'base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">التقارير</h1>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">أنواع التقارير</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-4">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title">تقرير الموظفين</h5>
                                    <p class="card-text">عرض تقرير مفصل عن الموظفين وبياناتهم.</p>
                                    <a href="{{ url_for('report.employees') }}" class="btn btn-primary">
                                        <i class="fas fa-users me-1"></i>عرض التقرير
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-4">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title">تقرير الأقسام</h5>
                                    <p class="card-text">عرض تقرير مفصل عن الأقسام وبياناتها.</p>
                                    <a href="{{ url_for('report.departments') }}" class="btn btn-primary">
                                        <i class="fas fa-building me-1"></i>عرض التقرير
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-4">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title">تقرير الإجازات</h5>
                                    <p class="card-text">عرض تقرير مفصل عن طلبات الإجازات.</p>
                                    <a href="{{ url_for('report.leave') }}" class="btn btn-primary">
                                        <i class="fas fa-calendar-alt me-1"></i>عرض التقرير
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-4">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title">تقرير الاجتماعات</h5>
                                    <p class="card-text">عرض تقرير مفصل عن الاجتماعات.</p>
                                    <a href="{{ url_for('report.meetings') }}" class="btn btn-primary">
                                        <i class="fas fa-handshake me-1"></i>عرض التقرير
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-4">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title">تقرير العقوبات</h5>
                                    <p class="card-text">عرض تقرير مفصل عن العقوبات.</p>
                                    <a href="{{ url_for('report.penalties') }}" class="btn btn-primary">
                                        <i class="fas fa-exclamation-triangle me-1"></i>عرض التقرير
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-4">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title">تقرير العملاء</h5>
                                    <p class="card-text">عرض تقرير مفصل عن العملاء وبياناتهم.</p>
                                    <a href="{{ url_for('report.clients') }}" class="btn btn-primary">
                                        <i class="fas fa-user-tie me-1"></i>عرض التقرير
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-4">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title">تقرير المعاملات المالية</h5>
                                    <p class="card-text">عرض تقرير مفصل عن المعاملات المالية.</p>
                                    <a href="{{ url_for('report.transactions') }}" class="btn btn-primary">
                                        <i class="fas fa-money-bill-wave me-1"></i>عرض التقرير
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-4">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title">تقرير الفواتير</h5>
                                    <p class="card-text">عرض تقرير مفصل عن الفواتير.</p>
                                    <a href="{{ url_for('report.invoices') }}" class="btn btn-primary">
                                        <i class="fas fa-file-invoice-dollar me-1"></i>عرض التقرير
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-4">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title">تقرير تفاصيل الموظف</h5>
                                    <p class="card-text">عرض تقرير مفصل عن موظف محدد بتنسيق السيرة الذاتية.</p>
                                    <a href="{{ url_for('report.employee_detail') }}" class="btn btn-primary">
                                        <i class="fas fa-id-card me-1"></i>عرض التقرير
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">سجل التقارير</h5>
                    <div>
                        <button type="button" class="btn btn-danger btn-sm" id="deleteSelectedBtn" style="display: none;" onclick="deleteSelectedReports()">
                            <i class="fas fa-trash me-1"></i>حذف المحدد
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Search and Filter Form -->
                    <form method="GET" action="{{ url_for('report.index') }}" class="mb-4">
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <div class="input-group">
                                    <input type="text" class="form-control" id="search" name="search" placeholder="ابحث بعنوان التقرير" value="{{ search_query }}">
                                    <button class="btn btn-primary" type="submit">
                                        <i class="fas fa-search"></i> بحث
                                    </button>
                                    {% if search_query %}
                                    <a href="{{ url_for('report.index') }}" class="btn btn-secondary">
                                        <i class="fas fa-times"></i> مسح
                                    </a>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="report_type" name="report_type" onchange="this.form.submit()">
                                    <option value="all" {% if report_type == 'all' or not report_type %}selected{% endif %}>جميع أنواع التقارير</option>
                                    {% for type in report_types %}
                                    <option value="{{ type.value }}" {% if report_type == type.value %}selected{% endif %}>{{ type.label }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" id="per_page" name="per_page" onchange="this.form.submit()">
                                    <option value="25" {% if current_per_page == 25 %}selected{% endif %}>عرض 25 تقرير</option>
                                    <option value="50" {% if current_per_page == 50 %}selected{% endif %}>عرض 50 تقرير</option>
                                    <option value="100" {% if current_per_page == 100 %}selected{% endif %}>عرض 100 تقرير</option>
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="input-group">
                                    <span class="input-group-text">من تاريخ</span>
                                    <input type="date" class="form-control" id="date_from" name="date_from" value="{{ date_from }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="input-group">
                                    <span class="input-group-text">إلى تاريخ</span>
                                    <input type="date" class="form-control" id="date_to" name="date_to" value="{{ date_to }}">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-filter"></i> تصفية
                                </button>
                            </div>
                            <div class="col-md-2">
                                <a href="{{ url_for('report.index') }}" class="btn btn-secondary w-100">
                                    <i class="fas fa-redo"></i> إعادة تعيين
                                </a>
                            </div>
                        </div>
                    </form>

                    {% if reports.items %}
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>
                                        <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                    </th>
                                    <th>#</th>
                                    <th>عنوان التقرير</th>
                                    <th>نوع التقرير</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>تم إنشاؤه بواسطة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for report in reports.items %}
                                <tr>
                                    <td>
                                        <input type="checkbox" class="report-checkbox" value="{{ report.id }}" onchange="updateDeleteButton()">
                                    </td>
                                    <td>{{ loop.index }}</td>
                                    <td>{{ report.title }}</td>
                                    <td>
                                        {% if report.report_type == 'employees' %}
                                        <span class="badge bg-primary">الموظفين</span>
                                        {% elif report.report_type == 'departments' %}
                                        <span class="badge bg-success">الأقسام</span>
                                        {% elif report.report_type == 'leave' %}
                                        <span class="badge bg-info">الإجازات</span>
                                        {% elif report.report_type == 'meetings' %}
                                        <span class="badge bg-warning">الاجتماعات</span>
                                        {% elif report.report_type == 'penalties' %}
                                        <span class="badge bg-danger">العقوبات</span>
                                        {% elif report.report_type == 'clients' %}
                                        <span class="badge bg-secondary">العملاء</span>
                                        {% elif report.report_type == 'transactions' %}
                                        <span class="badge bg-info text-dark">المعاملات المالية</span>
                                        {% elif report.report_type == 'invoices' %}
                                        <span class="badge bg-primary">الفواتير</span>
                                        {% else %}
                                        <span class="badge bg-dark">{{ report.report_type }}</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ report.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                    <td>{{ report.created_by.get_full_name() }}</td>

                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ url_for('report.download', id=report.id) }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-download"></i>
                                            </a>
                                            {% if current_user.has_role('admin') %}
                                            <form action="{{ url_for('report.remove', id=report.id) }}" method="POST" class="d-inline" onsubmit="return confirm('هل أنت متأكد من رغبتك في حذف هذا التقرير؟');">
                                                <button type="submit" class="btn btn-sm btn-danger">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-between align-items-center mt-4">
                        <div>
                            عرض {{ reports.items|length }} من {{ reports.total }} تقرير
                            {% if search_query %}
                            <span class="text-muted">(تصفية بواسطة: {{ search_query }})</span>
                            {% endif %}
                            {% if report_type and report_type != 'all' %}
                            <span class="text-muted">
                                (النوع:
                                {% for type in report_types %}
                                    {% if type.value == report_type %}
                                        {{ type.label }}
                                    {% endif %}
                                {% endfor %}
                                )
                            </span>
                            {% endif %}
                            {% if date_from or date_to %}
                            <span class="text-muted">
                                (التاريخ:
                                {% if date_from %}من {{ date_from }}{% endif %}
                                {% if date_to %}إلى {{ date_to }}{% endif %}
                                )
                            </span>
                            {% endif %}
                        </div>
                        <nav aria-label="Page navigation">
                            <ul class="pagination">
                                {% if reports.has_prev %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('report.index', page=reports.prev_num, per_page=current_per_page, search=search_query, report_type=report_type, date_from=date_from, date_to=date_to) }}" aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                                {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                                {% endif %}

                                {% set start_page = reports.page - 2 if reports.page > 2 else 1 %}
                                {% set end_page = start_page + 4 if start_page + 4 <= reports.pages else reports.pages %}
                                {% set start_page = end_page - 4 if end_page - 4 >= 1 else 1 %}

                                {% for page_num in range(start_page, end_page + 1) %}
                                <li class="page-item {% if page_num == reports.page %}active{% endif %}">
                                    <a class="page-link" href="{{ url_for('report.index', page=page_num, per_page=current_per_page, search=search_query, report_type=report_type, date_from=date_from, date_to=date_to) }}">{{ page_num }}</a>
                                </li>
                                {% endfor %}

                                {% if reports.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('report.index', page=reports.next_num, per_page=current_per_page, search=search_query, report_type=report_type, date_from=date_from, date_to=date_to) }}" aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                    {% else %}
                    <div class="alert alert-info">
                        {% if search_query or report_type != 'all' or date_from or date_to %}
                        <i class="fas fa-info-circle me-1"></i>لا توجد نتائج مطابقة للبحث.
                        <a href="{{ url_for('report.index') }}" class="alert-link">عرض جميع التقارير</a>
                        {% else %}
                        <i class="fas fa-info-circle me-1"></i>لا توجد تقارير محفوظة حتى الآن.
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const reportCheckboxes = document.querySelectorAll('.report-checkbox');

    reportCheckboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });

    updateDeleteButton();
}

function updateDeleteButton() {
    const checkedBoxes = document.querySelectorAll('.report-checkbox:checked');
    const deleteBtn = document.getElementById('deleteSelectedBtn');

    if (checkedBoxes.length > 0) {
        deleteBtn.style.display = 'inline-block';
        deleteBtn.textContent = `حذف المحدد (${checkedBoxes.length})`;
    } else {
        deleteBtn.style.display = 'none';
    }

    // تحديث حالة checkbox تحديد الكل
    const allCheckboxes = document.querySelectorAll('.report-checkbox');
    const selectAllCheckbox = document.getElementById('selectAll');

    if (checkedBoxes.length === allCheckboxes.length && allCheckboxes.length > 0) {
        selectAllCheckbox.checked = true;
        selectAllCheckbox.indeterminate = false;
    } else if (checkedBoxes.length > 0) {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = true;
    } else {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = false;
    }
}

function deleteSelectedReports() {
    const checkedBoxes = document.querySelectorAll('.report-checkbox:checked');
    const reportIds = Array.from(checkedBoxes).map(cb => cb.value);

    if (reportIds.length === 0) {
        alert('يرجى تحديد تقارير للحذف');
        return;
    }

    if (confirm(`هل أنت متأكد من حذف ${reportIds.length} تقرير؟ لا يمكن التراجع عن هذا الإجراء.`)) {
        // إرسال طلب حذف
        fetch('{{ url_for("report.delete_multiple") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name=csrf-token]')?.getAttribute('content') || ''
            },
            body: JSON.stringify({
                report_ids: reportIds
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ: ' + (data.message || 'فشل في حذف التقارير'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في الاتصال بالخادم');
        });
    }
}
</script>
{% endblock %}
