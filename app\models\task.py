from datetime import datetime
from app import db
from sqlalchemy import event

class Task(db.Model):
    __tablename__ = 'tasks'
    
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    start_date = db.Column(db.DateTime, default=datetime.utcnow)
    due_date = db.Column(db.DateTime)
    status = db.Column(db.String(20), default='pending')  # pending, in_progress, completed, cancelled
    priority = db.Column(db.String(20), default='medium')  # low, medium, high
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    completed_at = db.Column(db.DateTime)
    
    # Relationships
    project_id = db.Column(db.<PERSON>te<PERSON>, db.<PERSON><PERSON><PERSON>('projects.id'))
    assignee_id = db.Column(db.Integer, db.<PERSON><PERSON>ey('users.id'))
    
    # Task comments
    comments = db.relationship('TaskComment', backref='task', lazy='dynamic', cascade='all, delete-orphan')
    
    # Task attachments
    attachments = db.relationship('TaskAttachment', backref='task', lazy='dynamic', cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<Task {self.title}>'
    
    def is_overdue(self):
        if self.due_date and self.status != 'completed':
            return datetime.utcnow() > self.due_date
        return False
    
    def complete_task(self):
        self.status = 'completed'
        self.completed_at = datetime.utcnow()

    def get_status_display(self):
        """الحصول على نص الحالة للعرض"""
        status_map = {
            'pending': 'معلق',
            'in_progress': 'قيد التنفيذ',
            'completed': 'مكتمل',
            'cancelled': 'ملغي'
        }
        return status_map.get(self.status, 'معلق')

    def get_status_color(self):
        """الحصول على لون الحالة"""
        color_map = {
            'pending': 'secondary',
            'in_progress': 'primary',
            'completed': 'success',
            'cancelled': 'danger'
        }
        return color_map.get(self.status, 'secondary')

    def get_priority_color(self):
        """الحصول على لون الأولوية"""
        color_map = {
            'low': 'success',
            'medium': 'warning',
            'high': 'danger'
        }
        return color_map.get(self.priority, 'secondary')

    @property
    def assigned_to(self):
        """الحصول على المستخدم المكلف بالمهمة"""
        from app.models.user import User
        if self.assignee_id:
            return User.query.get(self.assignee_id)
        return None

    def sync_timeline_steps(self):
        """مزامنة جميع خطوات التايم لاين المرتبطة بهذه المهمة"""
        from app.models.timeline import TimelineStep

        # البحث عن جميع خطوات التايم لاين المرتبطة بهذه المهمة
        related_steps = TimelineStep.query.filter_by(
            source_type='task',
            task_id=self.id
        ).all()

        # مزامنة كل خطوة مع بيانات المهمة الحالية
        for step in related_steps:
            step.title = self.title
            step.description = self.description
            step.status = self.status

        # حفظ التغييرات
        db.session.commit()

        return len(related_steps)

class TaskComment(db.Model):
    __tablename__ = 'task_comments'
    
    id = db.Column(db.Integer, primary_key=True)
    content = db.Column(db.Text, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    task_id = db.Column(db.Integer, db.ForeignKey('tasks.id'))
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    user = db.relationship('User', backref='task_comments')
    
    def __repr__(self):
        return f'<TaskComment {self.id}>'

class TaskAttachment(db.Model):
    __tablename__ = 'task_attachments'
    
    id = db.Column(db.Integer, primary_key=True)
    filename = db.Column(db.String(255), nullable=False)
    filepath = db.Column(db.String(255), nullable=False)
    file_type = db.Column(db.String(50))
    file_size = db.Column(db.Integer)  # Size in bytes
    uploaded_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    task_id = db.Column(db.Integer, db.ForeignKey('tasks.id'))
    uploaded_by_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    uploaded_by = db.relationship('User', backref='task_attachments')
    
    def __repr__(self):
        return f'<TaskAttachment {self.filename}>'


# Event listeners للمزامنة التلقائية
@event.listens_for(Task, 'after_update')
def sync_timeline_steps_after_task_update(mapper, connection, target):
    """مزامنة خطوات التايم لاين تلقائياً عند تحديث المهمة"""
    try:
        # استخدام session منفصلة لتجنب مشاكل التزامن
        from sqlalchemy.orm import sessionmaker
        Session = sessionmaker(bind=connection)
        session = Session()

        # البحث عن خطوات التايم لاين المرتبطة بهذه المهمة
        from app.models.timeline import TimelineStep
        related_steps = session.query(TimelineStep).filter_by(
            source_type='task',
            task_id=target.id
        ).all()

        # مزامنة كل خطوة
        for step in related_steps:
            step.title = target.title
            step.description = target.description
            step.status = target.status

        session.commit()
        session.close()

    except Exception as e:
        print(f"خطأ في المزامنة التلقائية: {e}")
        # لا نريد أن يفشل تحديث المهمة بسبب خطأ في المزامنة
