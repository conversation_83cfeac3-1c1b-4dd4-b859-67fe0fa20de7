{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
        <a href="{{ url_for('finance.accounts') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i>العودة إلى شجرة الحسابات
        </a>
    </div>

    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-plus me-2"></i>إضافة حساب جديد
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">اسم الحساب <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="code" class="form-label">رمز الحساب</label>
                                    <input type="text" class="form-control" id="code" name="code" 
                                           placeholder="مثال: ACC001">
                                    <div class="form-text">رمز فريد للحساب (اختياري)</div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">وصف الحساب</label>
                            <textarea class="form-control" id="description" name="description" rows="3"
                                      placeholder="وصف مختصر للحساب وغرضه"></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="account_type" class="form-label">نوع الحساب</label>
                                    <select class="form-select" id="account_type" name="account_type">
                                        <option value="general">💼 عام</option>
                                        <option value="cash">💵 نقدي</option>
                                        <option value="bank">🏦 بنك</option>
                                        <option value="income">📈 إيرادات</option>
                                        <option value="expense">📉 مصروفات</option>
                                        <option value="pending">⏳ معلق</option>
                                        <option value="overdue">⚠️ متأخر</option>
                                        <option value="investment">📊 استثمارات</option>
                                        <option value="asset">🏢 أصول</option>
                                        <option value="liability">📋 التزامات</option>
                                        <option value="equity">💎 حقوق الملكية</option>
                                        <option value="receivable">📥 مدينون</option>
                                        <option value="payable">📤 دائنون</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="parent_id" class="form-label">الحساب الأب</label>
                                    <select class="form-select" id="parent_id" name="parent_id">
                                        <option value="">حساب رئيسي</option>
                                        {% for account in all_accounts %}
                                        <option value="{{ account.id }}">
                                            {{ account.full_path }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                    <div class="form-text">اختر الحساب الأب لجعل هذا حساباً فرعياً</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="currency_id" class="form-label">العملة</label>
                                    <select class="form-select" id="currency_id" name="currency_id">
                                        {% for currency in currencies %}
                                        <option value="{{ currency.id }}" {% if currency.is_default %}selected{% endif %}>
                                            {{ currency.symbol }} - {{ currency.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="account_color" class="form-label">لون خلفية الحساب</label>
                                    <div class="input-group">
                                        <input type="color" class="form-control form-control-color" id="account_color" name="account_color" value="#007bff">
                                        <input type="text" class="form-control" id="color_text" value="#007bff" readonly>
                                    </div>
                                    <div class="form-text">اختر لون خلفية الحساب</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="text_color" class="form-label">لون النص</label>
                                    <div class="input-group">
                                        <input type="color" class="form-control form-control-color" id="text_color" name="text_color" value="#ffffff">
                                        <input type="text" class="form-control" id="text_color_text" value="#ffffff" readonly>
                                    </div>
                                    <div class="form-text">اختر لون النص للحساب</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="folder_id" class="form-label">المجلد</label>
                                    <select class="form-select" id="folder_id" name="folder_id">
                                        <option value="">بدون مجلد</option>
                                        {% for folder in folders %}
                                        <option value="{{ folder.id }}">
                                            <i class="{{ folder.icon }}"></i> {{ folder.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                    <div class="form-text">اختر مجلداً لتنظيم الحساب</div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('finance.accounts') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>حفظ الحساب
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-generate code based on name
    const nameInput = document.getElementById('name');
    const codeInput = document.getElementById('code');

    // Color picker synchronization
    const colorPicker = document.getElementById('account_color');
    const colorText = document.getElementById('color_text');
    const textColorPicker = document.getElementById('text_color');
    const textColorText = document.getElementById('text_color_text');

    colorPicker.addEventListener('change', function() {
        colorText.value = this.value;
    });

    textColorPicker.addEventListener('change', function() {
        textColorText.value = this.value;
    });
    
    nameInput.addEventListener('input', function() {
        if (!codeInput.value) {
            // Generate a simple code from the name
            let code = this.value
                .replace(/[^\w\s]/gi, '') // Remove special characters
                .replace(/\s+/g, '_') // Replace spaces with underscores
                .toUpperCase()
                .substring(0, 10); // Limit to 10 characters
            
            if (code) {
                codeInput.value = code;
            }
        }
    });

    // Form validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        const name = nameInput.value.trim();
        
        if (!name) {
            e.preventDefault();
            alert('اسم الحساب مطلوب');
            nameInput.focus();
            return false;
        }
    });
});
</script>
{% endblock %}
