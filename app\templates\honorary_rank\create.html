{% extends 'base.html' %}

{% block styles %}
<style>
.animated-gradient {
    background-size: 200% 200% !important;
    animation: gradientShift 3s ease infinite !important;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">إنشاء رتبة شرفية جديدة</h1>
        <a href="{{ url_for('honorary_rank.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i>العودة إلى القائمة
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">تفاصيل الرتبة الشرفية</h6>
        </div>
        <div class="card-body">
            <form method="POST" action="{{ url_for('honorary_rank.create') }}">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="name" class="form-label">اسم الرتبة <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>

                    <div class="col-md-12 mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_premium" name="is_premium" onchange="togglePremiumOptions()">
                            <label class="form-check-label" for="is_premium">
                                رتبة مميزة (تدرج لوني)
                            </label>
                        </div>
                    </div>

                    <div id="basic-color-section" class="col-md-6 mb-3">
                        <label for="color" class="form-label">لون الرتبة</label>
                        <input type="color" class="form-control form-control-color w-100" id="color" name="color" value="#007bff">
                        <div class="form-text">اختر لوناً للرتبة</div>
                    </div>

                    <div id="premium-color-section" style="display: none;">
                        <div class="col-md-6 mb-3">
                            <label for="color_start" class="form-label">اللون الأول</label>
                            <input type="color" class="form-control form-control-color w-100" id="color_start" name="color_start" value="#007bff">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="color_end" class="form-label">اللون الثاني</label>
                            <input type="color" class="form-control form-control-color w-100" id="color_end" name="color_end" value="#28a745">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="gradient_direction" class="form-label">اتجاه التدرج</label>
                            <select class="form-select" id="gradient_direction" name="gradient_direction">
                                <option value="to right">من اليسار إلى اليمين</option>
                                <option value="to left">من اليمين إلى اليسار</option>
                                <option value="to bottom">من الأعلى إلى الأسفل</option>
                                <option value="to top">من الأسفل إلى الأعلى</option>
                                <option value="45deg">قطري (45 درجة)</option>
                                <option value="135deg">قطري عكسي (135 درجة)</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="animated_gradient" name="animated_gradient">
                                <label class="form-check-label" for="animated_gradient">
                                    تأثير متحرك (تغيير اللون باستمرار)
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-12 mb-3">
                        <label for="description" class="form-label">وصف الرتبة</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>

                    <div class="col-md-12 mb-3">
                        <label for="icon" class="form-label">أيقونة الرتبة</label>
                        <select class="form-select" id="icon" name="icon">
                            <option value="fa-award" selected>جائزة (Award)</option>
                            <option value="fa-medal">ميدالية (Medal)</option>
                            <option value="fa-trophy">كأس (Trophy)</option>
                            <option value="fa-star">نجمة (Star)</option>
                            <option value="fa-certificate">شهادة (Certificate)</option>
                            <option value="fa-crown">تاج (Crown)</option>
                            <option value="fa-gem">جوهرة (Gem)</option>
                            <option value="fa-shield-alt">درع (Shield)</option>
                            <option value="fa-bookmark">علامة مرجعية (Bookmark)</option>
                            <option value="fa-heart">قلب (Heart)</option>
                            <option value="fa-thumbs-up">إعجاب (Thumbs Up)</option>
                            <option value="fa-microphone">مايك (Microphone)</option>
                            <option value="fa-book">دفتر (Book)</option>
                            <option value="fa-file-excel">إكسل (Excel)</option>
                            <option value="fa-palette">فوتوشوب (Photoshop)</option>
                            <option value="fa-camera">كاميرا (Camera)</option>
                            <option value="fa-video">فيديو (Video)</option>
                            <option value="fa-music">موسيقى (Music)</option>
                            <option value="fa-paint-brush">فرشاة (Paint Brush)</option>
                            <option value="fa-code">كود (Code)</option>
                            <option value="fa-laptop">لابتوب (Laptop)</option>
                            <option value="fa-mobile-alt">موبايل (Mobile)</option>
                            <option value="fa-rocket">صاروخ (Rocket)</option>
                            <option value="fa-lightbulb">لمبة (Light Bulb)</option>
                            <option value="fa-graduation-cap">قبعة التخرج (Graduation Cap)</option>
                            <option value="fa-brain">دماغ (Brain)</option>
                            <option value="fa-eye">عين (Eye)</option>
                            <option value="fa-hand-holding-heart">يد تحمل قلب (Hand Holding Heart)</option>
                            <option value="fa-fire">نار (Fire)</option>
                            <option value="fa-bolt">برق (Lightning)</option>
                            <option value="fa-magic">سحر (Magic)</option>
                            <option value="fa-diamond">ماس (Diamond)</option>
                            <option value="fa-key">مفتاح (Key)</option>
                            <option value="fa-lock">قفل (Lock)</option>
                            <option value="fa-unlock">فتح القفل (Unlock)</option>
                        </select>
                        <div class="form-text">اختر أيقونة للرتبة</div>
                    </div>

                    <div class="col-md-12 mb-3">
                        <label class="form-label">معاينة الرتبة:</label>
                        <div class="p-3 border rounded">
                            <span id="preview-badge" class="badge bg-primary">
                                <i id="preview-icon" class="fas fa-award me-1"></i>
                                <span id="preview-name">اسم الرتبة</span>
                            </span>
                        </div>
                    </div>

                    <div class="col-12 mt-3">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>إنشاء الرتبة
                        </button>
                        <a href="{{ url_for('honorary_rank.index') }}" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i>إلغاء
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    function togglePremiumOptions() {
        const isPremium = document.getElementById('is_premium').checked;
        const basicSection = document.getElementById('basic-color-section');
        const premiumSection = document.getElementById('premium-color-section');

        if (isPremium) {
            basicSection.style.display = 'none';
            premiumSection.style.display = 'block';
        } else {
            basicSection.style.display = 'block';
            premiumSection.style.display = 'none';
        }
        updatePreview();
    }

    $(document).ready(function() {
        // Update preview when inputs change
        function updatePreview() {
            const name = $('#name').val() || 'اسم الرتبة';
            const icon = $('#icon').val();
            const isPremium = $('#is_premium').is(':checked');

            $('#preview-name').text(name);
            $('#preview-icon').attr('class', 'fas ' + icon + ' me-1');

            if (isPremium) {
                const colorStart = $('#color_start').val();
                const colorEnd = $('#color_end').val();
                const direction = $('#gradient_direction').val();
                const animated = $('#animated_gradient').is(':checked');

                let gradient = `linear-gradient(${direction}, ${colorStart}, ${colorEnd})`;
                $('#preview-badge').css('background', gradient);
                $('#preview-badge').css('color', '#fff');

                if (animated) {
                    $('#preview-badge').addClass('animated-gradient');
                } else {
                    $('#preview-badge').removeClass('animated-gradient');
                }
            } else {
                const color = $('#color').val();
                $('#preview-badge').css('background', color);
                $('#preview-badge').css('color', isLightColor(color) ? '#000' : '#fff');
                $('#preview-badge').removeClass('animated-gradient');
            }
        }
        
        // Check if color is light (to determine text color)
        function isLightColor(color) {
            const hex = color.replace('#', '');
            const r = parseInt(hex.substr(0, 2), 16);
            const g = parseInt(hex.substr(2, 2), 16);
            const b = parseInt(hex.substr(4, 2), 16);
            const brightness = ((r * 299) + (g * 587) + (b * 114)) / 1000;
            return brightness > 155;
        }
        
        // Update preview on input change
        $('#name, #color, #icon, #color_start, #color_end, #gradient_direction, #animated_gradient, #is_premium').on('input change', updatePreview);
        
        // Initial preview update
        updatePreview();
    });
</script>
{% endblock %}
