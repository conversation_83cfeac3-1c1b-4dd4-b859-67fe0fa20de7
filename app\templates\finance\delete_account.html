{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
        <a href="{{ url_for('finance.accounts') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i>العودة إلى شجرة الحسابات
        </a>
    </div>

    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card shadow border-danger">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>تأكيد حذف الحساب
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-danger" role="alert">
                        <h5 class="alert-heading">
                            <i class="fas fa-warning me-2"></i>تحذير!
                        </h5>
                        <p>أنت على وشك حذف حساب من شجرة الحسابات نهائياً. هذا الإجراء لا يمكن التراجع عنه.</p>
                        <hr>
                        <p class="mb-0">سيتم حذف الحساب وجميع الحسابات الفرعية التابعة له وجميع المعاملات المرتبطة.</p>
                    </div>

                    <!-- تفاصيل الحساب المراد حذفه -->
                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h6 class="mb-0">تفاصيل الحساب المراد حذفه</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>اسم الحساب:</strong></td>
                                            <td>{{ account.name }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>رقم الحساب:</strong></td>
                                            <td>{{ account.account_number or 'غير محدد' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>نوع الحساب:</strong></td>
                                            <td>
                                                {% if account.account_type == 'cash' %}
                                                <span class="badge bg-warning">نقدي</span>
                                                {% elif account.account_type == 'bank' %}
                                                <span class="badge bg-primary">بنكي</span>
                                                {% elif account.account_type == 'income' %}
                                                <span class="badge bg-success">دخل</span>
                                                {% elif account.account_type == 'expense' %}
                                                <span class="badge bg-danger">مصروف</span>
                                                {% elif account.account_type == 'pending' %}
                                                <span class="badge bg-warning">معلق</span>
                                                {% elif account.account_type == 'overdue' %}
                                                <span class="badge bg-danger">متأخر</span>
                                                {% else %}
                                                <span class="badge bg-secondary">{{ account.account_type }}</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>الرصيد الحالي:</strong></td>
                                            <td>
                                                <span class="h6 {% if account.balance >= 0 %}text-success{% else %}text-danger{% endif %}">
                                                    ${{ "{:,.2f}".format(account.balance) }}
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>الحساب الأب:</strong></td>
                                            <td>{{ account.parent.name if account.parent else 'حساب رئيسي' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>تاريخ الإنشاء:</strong></td>
                                            <td>{{ account.created_at.strftime('%Y-%m-%d %H:%M') if account.created_at else 'غير محدد' }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>

                            {% if account.description %}
                            <div class="mt-3">
                                <h6 class="text-muted">الوصف:</h6>
                                <div class="alert alert-info">
                                    {{ account.description }}
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- الحسابات الفرعية -->
                    {% if child_accounts %}
                    <div class="card mb-4">
                        <div class="card-header bg-warning text-dark">
                            <h6 class="mb-0">
                                <i class="fas fa-sitemap me-2"></i>الحسابات الفرعية التي سيتم حذفها ({{ child_accounts|length }})
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-warning">
                                <strong>تحذير:</strong> سيتم حذف جميع الحسابات الفرعية التالية مع الحساب الرئيسي:
                            </div>
                            <div class="row">
                                {% for child in child_accounts %}
                                <div class="col-md-6 mb-3">
                                    <div class="card border-warning">
                                        <div class="card-body py-2">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <h6 class="mb-1">{{ child.name }}</h6>
                                                    <small class="text-muted">{{ child.account_number or 'بدون رقم' }}</small>
                                                </div>
                                                <div class="text-end">
                                                    <span class="badge {% if child.balance >= 0 %}bg-success{% else %}bg-danger{% endif %}">
                                                        ${{ "{:,.2f}".format(child.balance) }}
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- تأثير الحذف على النظام -->
                    <div class="card mb-4">
                        <div class="card-header bg-info text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-info-circle me-2"></i>تأثير الحذف على النظام
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-12">
                                    <ul class="list-group list-group-flush">
                                        <li class="list-group-item px-0">
                                            <i class="fas fa-times text-danger me-2"></i>
                                            سيتم حذف الحساب نهائياً من شجرة الحسابات
                                        </li>
                                        {% if child_accounts %}
                                        <li class="list-group-item px-0">
                                            <i class="fas fa-times text-danger me-2"></i>
                                            سيتم حذف {{ child_accounts|length }} حساب فرعي
                                        </li>
                                        {% endif %}
                                        <li class="list-group-item px-0">
                                            <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                                            قد تتأثر المعاملات المالية المرتبطة بهذا الحساب
                                        </li>
                                        <li class="list-group-item px-0">
                                            <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                                            قد تتأثر الفواتير والرواتب المرتبطة بهذا الحساب
                                        </li>
                                        <li class="list-group-item px-0">
                                            <i class="fas fa-info text-info me-2"></i>
                                            سيتم الاحتفاظ بسجل المعاملات في قاعدة البيانات للمراجعة
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار التأكيد -->
                    <div class="d-flex justify-content-between align-items-center">
                        <a href="{{ url_for('finance.accounts') }}" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i>إلغاء
                        </a>
                        
                        <div>
                            <a href="{{ url_for('finance.edit_account', id=account.id) }}" class="btn btn-outline-warning me-2">
                                <i class="fas fa-edit me-1"></i>تعديل بدلاً من الحذف
                            </a>
                            <form method="POST" style="display: inline;" onsubmit="return confirmDelete()">
                                {{ csrf_token() }}
                                <button type="submit" class="btn btn-danger">
                                    <i class="fas fa-trash me-1"></i>تأكيد الحذف النهائي
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function confirmDelete() {
    const accountName = "{{ account.name }}";
    const childCount = {{ child_accounts|length if child_accounts else 0 }};
    
    let message = `هل أنت متأكد تماماً من حذف الحساب "${accountName}"؟`;
    
    if (childCount > 0) {
        message += `\n\nسيتم أيضاً حذف ${childCount} حساب فرعي.`;
    }
    
    message += '\n\nلا يمكن التراجع عن هذا الإجراء!';
    
    return confirm(message);
}

// Auto focus on cancel button for safety
document.addEventListener('DOMContentLoaded', function() {
    const cancelButton = document.querySelector('a[href*="accounts"]');
    if (cancelButton) {
        cancelButton.focus();
    }
});
</script>
{% endblock %}
