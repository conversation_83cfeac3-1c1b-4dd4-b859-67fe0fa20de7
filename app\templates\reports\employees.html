{% extends 'base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">تقرير الموظفين</h1>
        <a href="{{ url_for('report.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i>العودة إلى التقارير
        </a>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">خيارات التقرير</h5>
                </div>
                <div class="card-body">
                    <form action="{{ url_for('report.export', report_type='employees') }}" method="POST">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <label for="department_id" class="form-label">القسم</label>
                                <select class="form-select" id="department_id" name="department_id">
                                    <option value="all" selected>جميع الأقسام</option>
                                    {% for department in departments %}
                                    <option value="{{ department.id }}">{{ department.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="date_from" class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="date_from" name="date_from">
                                <small class="text-muted">فلترة المشاريع من هذا التاريخ</small>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="date_to" class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="date_to" name="date_to">
                                <small class="text-muted">فلترة المشاريع حتى هذا التاريخ</small>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="export_format" class="form-label">تنسيق التصدير</label>
                                <select class="form-select" id="export_format" name="export_format">
                                    <option value="pdf" selected>PDF</option>
                                    <option value="excel">Excel</option>
                                </select>
                            </div>
                        </div>
                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-file-export me-1"></i>تصدير التقرير
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">معاينة البيانات</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>#</th>
                                    <th>الاسم</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>الأقسام</th>
                                    <th>إجمالي المشاريع</th>
                                    <th>المشاريع المكتملة</th>
                                    <th>المشاريع الجارية</th>
                                    <th>المشاريع المعلقة</th>
                                    <th>تاريخ الانضمام</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for employee in employees %}
                                {% if employee.is_active %}
                                <tr>
                                    <td>{{ loop.index }}</td>
                                    <td>{{ employee.get_full_name() }}</td>
                                    <td>{{ employee.email }}</td>
                                    <td>{{ employee.department.name if employee.department else 'غير محدد' }}</td>
                                    <td>
                                        <span class="badge bg-info">{{ employee.projects|length }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-success">{{ employee.projects|selectattr('status', 'equalto', 'completed')|list|length }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-warning">{{ employee.projects|selectattr('status', 'equalto', 'in_progress')|list|length }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ employee.projects|selectattr('status', 'equalto', 'pending')|list|length }}</span>
                                    </td>
                                    <td>{{ employee.date_joined.strftime('%Y-%m-%d') if employee.date_joined else 'غير محدد' }}</td>
                                </tr>
                                {% endif %}
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Initialize select2
        $('#department_id, #status, #export_format').select2({
            theme: 'bootstrap-5',
            width: '100%',
            dir: 'rtl'
        });
    });
</script>
{% endblock %}
