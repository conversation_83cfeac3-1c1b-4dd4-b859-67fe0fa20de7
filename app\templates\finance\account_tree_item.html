{% set level = level|default(0) %}
<div class="account-item level-{{ level }} {% if not account.is_active %}inactive{% endif %}"
     style="border-left: 4px solid {{ account.account_color or '#007bff' }};
            background: linear-gradient(135deg, {{ account.account_color or '#007bff' }}, {{ account.account_color or '#007bff' }}dd);
            color: {{ account.text_color or '#ffffff' }};
            border-radius: 10px; margin-bottom: 8px;
            box-shadow: 0 3px 6px rgba(0,0,0,0.12);
            border: 1px solid rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);">
    <div class="account-info">
        <div class="account-details">
            {% if account.children %}
            <button class="toggle-children" data-target="children-{{ account.id }}">
                <i class="fas fa-minus"></i>
            </button>
            {% endif %}
            
            <div class="account-name">
                {% if account.account_type == 'cash' %}
                <span class="me-2">💵</span>
                {% elif account.account_type == 'bank' %}
                <span class="me-2">🏦</span>
                {% elif account.account_type == 'income' %}
                <span class="me-2">📈</span>
                {% elif account.account_type == 'expense' %}
                <span class="me-2">📉</span>
                {% elif account.account_type == 'pending' %}
                <span class="me-2">⏳</span>
                {% elif account.account_type == 'overdue' %}
                <span class="me-2">⚠️</span>
                {% elif account.account_type == 'investment' %}
                <span class="me-2">📊</span>
                {% elif account.account_type == 'asset' %}
                <span class="me-2">🏢</span>
                {% elif account.account_type == 'liability' %}
                <span class="me-2">📋</span>
                {% elif account.account_type == 'equity' %}
                <span class="me-2">💎</span>
                {% elif account.account_type == 'receivable' %}
                <span class="me-2">📥</span>
                {% elif account.account_type == 'payable' %}
                <span class="me-2">📤</span>
                {% elif account.children %}
                <i class="fas fa-folder-open me-2"></i>
                {% else %}
                <span class="me-2">💼</span>
                {% endif %}
                <strong>{{ account.name }}</strong>
            </div>
            
            {% if account.code %}
            <div class="account-code">رمز الحساب: {{ account.code }}</div>
            {% endif %}
            
            {% if account.description %}
            <div class="account-description" style="font-size: 0.9em; margin-top: 5px;">
                {{ account.description }}
            </div>
            {% endif %}
        </div>
        
        <div class="account-balance">
            {% if filter_applied and account.filtered_balance is defined %}
            <div class="mb-1">
                <small class="text-muted">رصيد الفترة:</small>
                <span class="badge {% if account.filtered_balance >= 0 %}bg-info{% else %}bg-warning{% endif %} fs-6">
                    {% if account.currency %}{{ account.currency.symbol }}{% else %}${% endif %}{{ "{:,.2f}".format(account.filtered_balance) }}
                </span>
            </div>
            <div>
                <small class="text-muted">الرصيد الإجمالي:</small>
                <span class="badge {% if account.balance >= 0 %}bg-success{% else %}bg-danger{% endif %} fs-6">
                    {% if account.currency %}{{ account.currency.symbol }}{% else %}${% endif %}{{ "{:,.2f}".format(account.balance) }}
                </span>
            </div>
            {% else %}
            <span class="badge {% if account.balance >= 0 %}bg-success{% else %}bg-danger{% endif %} fs-5">
                {% if account.currency %}{{ account.currency.symbol }}{% else %}${% endif %}{{ "{:,.2f}".format(account.balance) }}
            </span>
            {% endif %}
        </div>
        
        {% if current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance') %}
        <div class="account-actions">
            <a href="{{ url_for('finance.view_account', id=account.id) }}"
               class="btn btn-sm btn-outline-info" title="عرض التفاصيل">
                <i class="fas fa-eye"></i>
            </a>
            <a href="{{ url_for('finance.edit_account', id=account.id) }}"
               class="btn btn-sm btn-outline-light" title="تعديل">
                <i class="fas fa-edit"></i>
            </a>

            {% if current_user.has_role('admin') or current_user.has_role('finance') %}
            <a href="{{ url_for('finance.delete_account', id=account.id) }}"
               class="btn btn-sm btn-outline-danger" title="حذف">
                <i class="fas fa-trash"></i>
            </a>
            {% endif %}
        </div>
        {% endif %}
    </div>
    
    {% if account.children %}
    <div class="children-container" id="children-{{ account.id }}">
        {% for child in account.children %}
            {% with account=child, level=level+1 %}
                {% include 'finance/account_tree_item.html' %}
            {% endwith %}
        {% endfor %}
    </div>
    {% endif %}
</div>
